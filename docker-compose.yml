version: '3'

services:
  api-crm:
    build: .
    ports:
      - 2322:1619
    deploy:
      restart_policy:
        condition: any
        delay: 5s
        # max_attempts: 3 #use when restart policy set to always
        window: 120s
      resources:
        limits:
          memory: 300M
        reservations:
          memory: 200M    # Reserve minimum memory
    mem_swappiness: 0    # Reduce swap usage
    restart: unless-stopped
    oom_kill_disable: false     # Let Docker kill and restart when OOM
    oom_score_adj: -500         # Lower priority for OOM killer
    healthcheck:                 # Add health checks
      test: ["CMD", "curl", "-f", "http://localhost:YOUR_PORT/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s