package usecase

import (
	"fmt"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"time"
)

type selfOrderUseCase struct {
	repo domain.SelfOrderRepository
}

func NewSelfOrderUseCase(repository domain.SelfOrderRepository) domain.SelfOrderUseCase {
	return &selfOrderUseCase{repository}
}

func (s selfOrderUseCase) UpdateSelfOrder(code string, customerName string, contact string, userSession domain.UserSession) error {
	order, err := s.repo.FetchSelfOrderByOrderCode(code, userSession.AdminId)
	if log.IfError(err) {
		return err
	}

	if len(order) == 0 {
		return fmt.Errorf("not found or already expired")
	}

	if utils.ToInt64(order["expired_at"]) < time.Now().Unix()*1000 {
		return fmt.Errorf("order has already expired")
	}

	return s.repo.UpdateSelfOrder(code, customerName, contact)
}
