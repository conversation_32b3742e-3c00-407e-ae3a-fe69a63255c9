package http

import (
	"encoding/json"
	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type selfOrderHandler struct {
	uc domain.SelfOrderUseCase
}

func NewHttpSelfOrderHandler(app *fasthttprouter.Router, useCase domain.SelfOrderUseCase) {
	handler := &selfOrderHandler{useCase}
	//auth.MultiMiddleware(v1.AddSelfOrder, auth.CORS, auth.ValidatePublicKey)
	app.PUT("/v1/self-order/:id", auth.MultiMiddleware(handler.UpdateSelfOrder, auth.CORS, auth.ValidatePublicKey))
	app.OPTIONS("/v1/self-order/:id", auth.EnableCors)
}

func (h selfOrderHandler) UpdateSelfOrder(ctx *fasthttp.RequestCtx) {
	orderCode := ctx.UserValue("id")
	customerName := string(ctx.PostArgs().Peek("customer_name"))
	contact := string(ctx.PostArgs().Peek("contact"))

	err := h.uc.UpdateSelfOrder(utils.ToString(orderCode), customerName, contact, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}
