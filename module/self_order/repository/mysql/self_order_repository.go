package mysql

import (
	"database/sql"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type selfOrderRepository struct {
	db db.Repository
}

func NewMysqlSelfOrderRepository(conn *sql.DB) domain.SelfOrderRepository {
	return &selfOrderRepository{db.Repository{Conn: conn}}
}

func (s selfOrderRepository) FetchSelfOrderByOrderCode(code string, adminId int64) (map[string]interface{}, error) {
	query := `SELECT
    so.*
FROM
    self_order so
JOIN outlets o ON
    o.outlet_id = so.outlet_fkid
WHERE
    so.order_code = ? AND o.admin_fkid = ?`
	return db.Query(query, code, adminId)
}

func (s selfOrderRepository) UpdateSelfOrder(code string, customerName string, contact string) error {
	_, err := db.Update("self_order", map[string]interface{}{
		"customer_name": customerName,
		"contact":       contact,
	}, "order_code = ?", code)
	return err
}
