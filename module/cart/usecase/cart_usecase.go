package usecase

import (
	"fmt"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
)

type cartUseCase struct {
	repo        domain.CartRepository
	repoProduct product.Repository
	ucOutlet    domain.OutletUseCase
}

func NewCartUseCase(repository domain.CartRepository, repositoryProduct product.Repository, ucOutlet domain.OutletUseCase) domain.CartUseCase {
	return &cartUseCase{repository, repositoryProduct, ucOutlet}
}

func (c *cartUseCase) FetchCart(userSession domain.UserSession, param domain.CartRequestQuery) ([]map[string]interface{}, error) {
	cartList, err := c.repo.FetchCart(userSession, param)
	if err != nil {
		return nil, err
	}

	productDetailIds := make([]int, 0)
	outletIdMap := make(map[int]bool)
	for _, cart := range cartList {
		productDetailIds = append(productDetailIds, cast.ToInt(cart["product_detail_fkid"]))
		outletIdMap[cast.ToInt(cart["outlet_fkid"])] = true
	}

	productList, _ := c.repoProduct.FetchProductByDetailIds(productDetailIds)
	productMap := make(map[int]interface{})
	for _, product := range productList {
		productMap[cast.ToInt(product["product_detail_id"])] = product
	}

	outletIds := make([]int, 0)
	for id := range outletIdMap {
		outletIds = append(outletIds, id)
	}

	outlets, _ := c.ucOutlet.FetchOutletByIds(outletIds...)
	outletMap := make(map[int]map[string]interface{})
	for _, outlet := range outlets {
		outletMap[cast.ToInt(outlet["outlet_id"])] = utils.TakesOnly(outlet, "name", "enable_order")
	}

	for i, cart := range cartList {
		cartList[i]["product"] = productMap[cast.ToInt(cart["product_detail_fkid"])]
		cartList[i]["outlet"] = outletMap[cast.ToInt(cart["outlet_fkid"])]
	}

	return cartList, nil
}

func (c *cartUseCase) AddCart(productDetailId int, userSession domain.UserSession) error {
	log.Info("adding to cart: '%d' by '%v'", productDetailId, userSession.MemberId)
	err := c.repo.AddCart(productDetailId, 1, userSession)
	if err == nil {
		return nil
	}

	//check what causing the error
	//- check whether the item already added
	if c.isItemOnCart(productDetailId, userSession) {
		return fmt.Errorf("cart item already added")
	}

	return err
}

func (c *cartUseCase) RemoveCart(productDetailIds []int, userSession domain.UserSession) error {
	rows, err := c.repo.RemoveCart(productDetailIds, userSession)
	if err != nil {
		return err
	}

	if rows == 0 {
		// return fmt.Errorf("items not in the cart")
		log.Info("delete cart, but item not in db, prodDetId: %v | user: %v", productDetailIds, userSession.MemberId)
	}

	return nil
}

func (c *cartUseCase) UpdateCart(productDetailId int, qty int, userSession domain.UserSession) error {
	if productDetailId <= 0 {
		return fmt.Errorf("invalid product_detail_id: %v", productDetailId)
	}

	return c.repo.UpdateCart(productDetailId, qty, userSession)
}

func (c *cartUseCase) UpdateCartBatch(body []domain.CartUpdateBody, user domain.UserSession) error {
	//remove all cart
	_, err := c.repo.RemoveCart([]int{}, user)
	if log.IfError(err) {
		return err
	}

	//insert new
	for _, item := range body {
		err = c.repo.AddCart(int(item.ProductDetailId), item.Qty, user)
		log.IfError(err)
	}

	return nil
}

func (c *cartUseCase) CountCart(userSession domain.UserSession) (map[string]interface{}, error) {
	return c.repo.CountCart(userSession)
}

func (c *cartUseCase) isItemOnCart(productDetailId int, userSession domain.UserSession) bool {
	data, err := c.repo.FetchCart(userSession, domain.CartRequestQuery{ProductDetailId: productDetailId})
	return err == nil && len(data) > 0
}
