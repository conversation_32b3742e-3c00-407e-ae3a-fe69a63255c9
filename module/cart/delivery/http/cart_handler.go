package http

import (
	"encoding/json"
	"fmt"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type cartHandler struct {
	uc domain.CartUseCase
}

func NewHttpCartHandler(app *fasthttprouter.Router, useCase domain.CartUseCase) {
	handler := &cartHandler{useCase}
	app.GET("/v1/cart", auth.ValidateToken(handler.FetchCart))
	app.POST("/v1/cart", auth.ValidateToken(handler.AddCart))
	app.DELETE("/v1/cart", auth.ValidateToken(handler.RemoveCart))
	app.PUT("/v1/cart", auth.ValidateToken(handler.UpdateCart))

	app.GET("/v1/cart-count", auth.ValidateToken(handler.CountCart))
}

func (h cartHandler) FetchCart(ctx *fasthttp.RequestCtx) {
	var queryParam domain.CartRequestQuery
	queryMap := make(map[string]interface{})
	ctx.QueryArgs().VisitAll(func(key, value []byte) {
		queryMap[string(key)] = string(value)
	})

	queryJson, err := json.Marshal(queryMap)
	log.IfError(err)
	err = json.Unmarshal(queryJson, &queryParam)
	log.IfError(err)
	fmt.Println("queryParam: ", queryParam)

	result, err := h.uc.FetchCart(domain.UserSessionFastHttp(ctx), queryParam)
	if err != nil {
		log.Info("fetchCart err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h cartHandler) AddCart(ctx *fasthttp.RequestCtx) {
	productDetailId := cast.ToInt(ctx.PostArgs().Peek("product_detail_id"))
	err := h.uc.AddCart(productDetailId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("addCart err: %v", err)
		// ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}

func (h cartHandler) RemoveCart(ctx *fasthttp.RequestCtx) {
	type id struct {
		ProductDetailId int `json:"product_detail_id"`
	}
	var ids []id
	err := json.Unmarshal(ctx.PostBody(), &ids)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	idsArr := make([]int, 0)
	for _, id := range ids {
		idsArr = append(idsArr, id.ProductDetailId)
	}

	err = h.uc.RemoveCart(idsArr, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}

func (h cartHandler) UpdateCart(ctx *fasthttp.RequestCtx) {
	productDetailId := cast.ToInt(ctx.PostArgs().Peek("product_detail_id"))
	qty := cast.ToInt(ctx.PostArgs().Peek("qty"))
	requestBody := ctx.Request.Body()

	log.Info("update cart, prodDetId: %v, reqBody: '%v'", productDetailId, string(requestBody))
	if productDetailId == 0 && (requestBody == nil || string(requestBody) == "") {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	var err error
	if productDetailId != 0 {
		err = h.uc.UpdateCart(productDetailId, qty, domain.UserSessionFastHttp(ctx))
	} else {
		var data []domain.CartUpdateBody
		err = json.Unmarshal(requestBody, &data)
		log.IfError(err)
		err = h.uc.UpdateCartBatch(data, domain.UserSessionFastHttp(ctx))
	}

	if err != nil {
		log.Info("UpdateCart err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "cart successfully updated"})
}

func (h cartHandler) CountCart(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.CountCart(domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
