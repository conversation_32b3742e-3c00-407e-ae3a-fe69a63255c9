package mysql

import (
	"database/sql"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type cartRepository struct {
	db db.Repository
}

func NewMysqlCartRepository(conn *sql.DB) domain.CartRepository {
	return &cartRepository{db.Repository{Conn: conn}}
}

func (c cartRepository) FetchCart(userSession domain.UserSession, param domain.CartRequestQuery) ([]map[string]interface{}, error) {
	query := `select cc.*, pd.outlet_fkid, pd.product_fkid from crm_cart cc 
join products_detail pd on cc.product_detail_fkid=pd.product_detail_id 
join products p on p.product_id=pd.product_fkid
join outlets o on o.outlet_id=pd.outlet_fkid
where cc.member_fkid = ? and cc.admin_fkid = ? and o.app_show = 1 and p.app_show=1`

	args := make([]interface{}, 0)
	args = append(args, userSession.MemberId, userSession.AdminId)

	if cast.ToInt(param.OutletId) > 0 {
		query += " and pd.outlet_fkid = ? "
		args = append(args, param.OutletId)
	}
	if param.ProductDetailId > 0 {
		query += " and pd.product_detail_id =? "
		args = append(args, param.ProductDetailId)
	}

	return db.QueryArray(query, args...)
}

func (c cartRepository) AddCart(productDetailId, qty int, userSession domain.UserSession) error {
	_, err := db.Insert("crm_cart", map[string]interface{}{
		"product_detail_fkid": productDetailId,
		"member_fkid":         userSession.MemberId,
		"admin_fkid":          userSession.AdminId,
		"qty":                 qty,
		"time_created":        time.Now().Unix() * 1000,
	})
	return err
}

func (c cartRepository) RemoveCart(productDetailIds []int, userSession domain.UserSession) (int64, error) {
	sqlWhere := " member_fkid = @memberId and admin_fkid = @adminId "
	if len(productDetailIds) > 0 {
		sqlWhere += " and product_detail_fkid in @productDetailIds "
	}

	sqlWhere, params := db.MapParam(sqlWhere, map[string]interface{}{
		"memberId":         userSession.MemberId,
		"adminId":          userSession.AdminId,
		"productDetailIds": productDetailIds,
	})

	resp, err := db.Delete("crm_cart", sqlWhere, params...)
	rows, _ := resp.RowsAffected()
	log.Info("delete cart, total : %v | effected: %v", len(productDetailIds), rows)
	return rows, err
}

func (c cartRepository) UpdateCart(productDetailId int, qty int, userSession domain.UserSession) error {
	_, err := db.Update("crm_cart", map[string]interface{}{
		"qty": qty,
	}, "product_detail_fkid = ? and member_fkid = ?", productDetailId, userSession.MemberId)
	return err
}

func (c cartRepository) CountCart(userSession domain.UserSession) (map[string]interface{}, error) {
	sql := `
	SELECT
	sum(qty) AS count,
	count(*) AS total_unique_product
FROM
	crm_cart cc
	JOIN products_detail pd ON cc.product_detail_fkid = pd.product_detail_id
	JOIN products p ON p.product_id = pd.product_fkid
	JOIN outlets o ON o.outlet_id = pd.outlet_fkid
WHERE
	cc.member_fkid = ?
	AND cc.admin_fkid = ?
	AND o.app_show = 1
	AND p.app_show = 1`
	return db.Query(sql, userSession.MemberId, userSession.AdminId)
}

//func (c cartRepository) FetchProductByIds(productDetailIds []interface{}) ([]map[string]interface{}, error) {
//	whereIn := strings.Repeat("?,", len(productDetailIds))
//	whereIn = strings.TrimRight(whereIn, ",")
//
//	sql := `SELECT
//	concat(p.name, COALESCE(concat(' (', pdv.variant_name, ')'), '')) as name,
//	p.photo, pd.price_sell, pd.product_detail_id, p.description, u.name as unit
//FROM
//	products_detail pd
//	JOIN products p ON p.product_id = pd.product_fkid
//join unit u on p.unit_fkid=u.unit_id
//	LEFT JOIN products_detail_variant pdv ON pd.variant_fkid = pdv.variant_id
//where pd.product_detail_id in (` + whereIn + `)`
//	return db.QueryArray(sql, productDetailIds...)
//}
