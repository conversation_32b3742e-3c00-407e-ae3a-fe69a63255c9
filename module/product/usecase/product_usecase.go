package usecase

import (
	"errors"
	"fmt"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/goccy/go-json"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/memory"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
)

type productUseCase struct {
	repo         product.Repository
	repoCampaign campaign.CampaignRepository
	repoOutlet   domain.OutletRepository
}

func NewProductUseCase(repository product.Repository,
	repoCampaign campaign.CampaignRepository,
	repoOutlet domain.OutletRepository) product.UseCase {
	return &productUseCase{repository, repoCampaign, repoOutlet}
}

func (p *productUseCase) FetchProduct(param *domain.ProductRequest, user *domain.UserSession) (*[]models.ProductAvailability, error) {
	memBefore := memory.Consumed()
	result := make([]models.ProductAvailability, 0)
	products, err := p.repo.FetchProduct(param, user)
	if log.IfError(err) {
		return nil, err
	}

	if len(*products) == 0 {
		log.Info("no product... %v", user.AdminId)
		return &result, nil
	}

	unitIdMap := make(map[int]int)
	subCategoryIdMap := make(map[int]int)
	outletIdMap := make(map[int]int)
	for _, product := range *products {
		unitIdMap[product.UnitFkid] = 1
		subCategoryIdMap[product.ProductSubcategoryFkid] = 1
		outletIdMap[product.OutletFkid] = 1
	}

	unitIds := array.GetKeysOfMap(unitIdMap)
	subcategoryIds := array.GetKeysOfMap(subCategoryIdMap)
	outletIds := array.GetKeysOfMap[int](outletIdMap)

	unitChan := make(chan *[]models.UnitEntity)
	go func(unitIds *[]int, model chan *[]models.UnitEntity) {
		units, err := p.repo.FetchUnitsByID(*unitIds)
		log.IfError(err)
		model <- units
	}(&unitIds, unitChan)

	subcategoryStream := make(chan *[]models.SubcategoryWithPosition)
	p.repo.FetchSubcategoryByIds(subcategoryIds)
	go func(ids *[]int, model chan *[]models.SubcategoryWithPosition) {
		result, err := p.repo.FetchSubcategoryByIds(*ids)
		log.IfError(err)
		model <- result
	}(&subcategoryIds, subcategoryStream)

	outletStream := make(chan *[]models.OutletEntity)
	go func(ids *[]int, model chan *[]models.OutletEntity) {
		result, err := p.repoOutlet.FetchOutletName(*ids)
		log.IfError(err)
		model <- result
	}(&outletIds, outletStream)

	productAvailableStream := make(chan *[]models.ProductAvailableEntity)
	go func(adminId int64, model chan *[]models.ProductAvailableEntity) {
		day := strings.ToLower(time.Now().Add(7 * time.Hour).UTC().Weekday().String())
		result, err := p.repo.FetchProductAvailable(&domain.ProductAvailableFilter{AdminId: int(adminId), Day: day})
		log.IfError(err)
		model <- result
	}(user.AdminId, productAvailableStream)

	//any additional request (include)
	taxStream := make(chan *[]models.ProductGratuity)
	go p.getTax(param, user, taxStream)

	units := <-unitChan
	subcategories := <-subcategoryStream
	outletNames := <-outletStream
	productAvailables := <-productAvailableStream
	taxes := <-taxStream

	fmt.Println("taxes: ", len(*taxes))

	unitMap := make(map[int]*models.UnitEntity)
	for _, unit := range *units {
		unitMap[unit.UnitID] = &unit
	}

	subcategoryMap := make(map[int]*models.SubcategoryWithPosition)
	for _, subcategory := range *subcategories {
		subcategoryMap[subcategory.ProductSubcategoryID] = &subcategory
	}

	outletNameMap := make(map[int]string)
	for _, outlet := range *outletNames {
		outletNameMap[outlet.OutletID] = outlet.Name
	}

	productAvailableMap := make(map[int]*models.ProductAvailableEntity)
	for _, product := range *productAvailables {
		productAvailableMap[product.ProductFkid] = &product
	}

	taxByProductMap := make(map[int][]models.GratuityEntity)
	for _, tax := range *taxes {
		if _, ok := taxByProductMap[tax.ProductDetailFkid]; !ok {
			taxByProductMap[tax.ProductDetailFkid] = make([]models.GratuityEntity, 0)
		}
		taxByProductMap[tax.ProductDetailFkid] = append(taxByProductMap[tax.ProductDetailFkid], tax.GratuityEntity)
	}

	//group product by its id
	productMap := make(map[int][]models.Product, 0)
	for _, product := range *products {
		if _, ok := productMap[product.ProductID]; !ok {
			productMap[product.ProductID] = make([]models.Product, 0)
		}
		productMap[product.ProductID] = append(productMap[product.ProductID], product)
	}

	tempProduct := make([][]models.Product, 0)
	for _, row := range productMap {
		if tempProduct == nil || len(tempProduct) <= 4 {
			tempProduct = append(tempProduct, row)
		}

		available, variant := getProductAvailable(&row, outletNameMap, productAvailableMap, taxByProductMap)
		product := row[0]
		subcategory := subcategoryMap[product.ProductSubcategoryFkid]
		if subcategory.Position == 0 {
			subcategory.Position = 99999
		}
		productAvailable, ok := productAvailableMap[product.ProductID]
		if !ok {
			productAvailable = models.ProductAvailableEntityDefault()
		}

		productResult := product.ToProductAvailability(available, variant, unitMap[product.UnitFkid], subcategory)

		productResult.HourStart = productAvailable.HourStart
		productResult.HourEnd = productAvailable.HourEnd
		// productResult.Availability = availability
		result = append(result, productResult)
	}

	//adding product highlight
	if os.Getenv("ENV") == "development" {
		for _, row := range tempProduct {
			available, variant := getProductAvailable(&row, outletNameMap, productAvailableMap, taxByProductMap)
			product := (row)[0]
			productHighlight := product.ToProductAvailability(available, variant, unitMap[product.UnitFkid], subcategoryMap[product.ProductSubcategoryFkid])

			productHighlight.Subcategory = "Best Item"
			productHighlight.SubcategoryPosition = 1
			productHighlight.SubcategoryConfig = models.SubcategoryConfig{ViewType: "grid"}

			result = append(result, productHighlight)
		}
	}

	for i, product := range result {
		// result[i].Availability = "available"
		// for _, avail := range product.Available {
		// 	if avail.EnableOrder.Status == "disable" {
		// 		result[i].Availability = "unavailable"
		// 		break
		// 	}
		// }

		result[i].Availability = "unavailable"
		for _, avail := range product.Available {
			if avail.EnableOrder.Status == models.ProductOrderEnable {
				result[i].Availability = "available"
				break
			}
		}

	}

	memAfter := memory.Consumed()
	fmt.Printf("total memory: %.3fkb\n", float64(memAfter-memBefore))

	return &result, nil
}

func (p *productUseCase) getTax(param *domain.ProductRequest, user *domain.UserSession, model chan *[]models.ProductGratuity) {
	var result []models.ProductGratuity
	if !array.Contain(param.Includes, "tax") {
		fmt.Println("no include tax...")
		model <- &result
		return
	}

	//first fetch all gratuity
	gratuities, err := p.repo.FetchGratuity(&domain.GratuityFilter{
		AdminId:       int(user.AdminId),
		TaxCategories: []string{domain.TaxCategoryTax, domain.TaxCategoryService},
		TaxStatuses:   []string{domain.TaxStatusPermanent},
	})

	if log.IfError(err) || len(*gratuities) == 0 {
		fmt.Println("total gratuities ", len(*gratuities))
		model <- &result
		return
	}

	taxIds := make([]int, 0)
	for _, tax := range *gratuities {
		taxIds = append(taxIds, tax.GratuityID)
	}

	//then get all product activated for those taxes
	productTaxes, err := p.repo.FetchProductGratuity(&domain.ProductGratuityFilter{GratuityId: taxIds})
	if log.IfError(err) || len(*productTaxes) == 0 {
		fmt.Println("no product active for tax...")
		model <- &result
		return
	}

	gratuityMap := make(map[int]models.GratuityEntity)
	for _, tax := range *gratuities {
		gratuityMap[tax.GratuityID] = tax
	}

	for _, product := range *productTaxes {
		result = append(result, models.ProductGratuity{
			GratuityEntity:    gratuityMap[product.TaxFkid],
			ProductDetailFkid: product.ProductDetailFkid,
		})
	}

	model <- &result
}

func getProductAvailable(data *[]models.Product, outletNames map[int]string, availableTime map[int]*models.ProductAvailableEntity, tax map[int][]models.GratuityEntity) (*[]models.Available, *[]models.ProductVariant) {
	available := make([]models.Available, 0)
	variant := make([]models.ProductVariant, 0)
	variantMap := make(map[int][]models.Product)

	for _, product := range *data {
		product.OutletName = outletNames[product.OutletFkid]
		productAvailable, ok := availableTime[product.ProductID]
		// taxes := tax[product.ProductDetailID]
		if !ok {
			productAvailable = models.ProductAvailableEntityDefault()
		}

		if product.VariantId > 0 {
			if _, ok := variantMap[product.VariantId]; !ok {
				variantMap[product.VariantId] = make([]models.Product, 0)
			}
			variantMap[product.VariantId] = append(variantMap[product.VariantId], product)
		} else {
			available = append(available, product.ToAvailale(*productAvailable))
		}
	}

	for id, variants := range variantMap {
		variantAvailable := make([]models.Available, 0)
		for _, product := range variants {
			product.OutletName = outletNames[product.OutletFkid]
			productAvailable, ok := availableTime[product.ProductID]
			if !ok {
				productAvailable = models.ProductAvailableEntityDefault()
			}
			variantAvailable = append(variantAvailable, product.ToAvailale(*productAvailable))
		}
		variant = append(variant, models.ProductVariant{
			VariantID: id,
			Name:      variants[0].VariantName,
			Available: variantAvailable,
		})
	}

	return &available, &variant
}

func (p *productUseCase) FetchProductSubcategory(param *domain.ProductSubcategoryRequest, user *domain.UserSession) ([]map[string]interface{}, error) {
	result, err := p.repo.FetchProductSubcategory(param, user)
	if err != nil {
		return nil, err
	}

	for i, raw := range result {
		ids := strings.Split(cast.ToString(raw["outlet_ids"]), ",")
		outletIds := make([]int, 0)
		for _, id := range ids {
			outletIds = append(outletIds, cast.ToInt(id))
		}
		result[i]["position"] = i + 1
		result[i]["outlet_ids"] = outletIds
	}
	return result, nil
}

func (p *productUseCase) UpdateSubcategory(param *[]domain.Subcategory, user *domain.UserSession) error {
	return p.repo.UpdateSubcategory(param, user)
}

func (p *productUseCase) FetchProductLinkMenu(param *domain.ProductLinkMenuRequest, user *domain.UserSession) (*[]models.LinkMenuEntity, error) {
	logTime := log.TimeInit()
	linkMenu, err := p.repo.FetchLinkMenu(param, user)
	if log.IfError(err) {
		return nil, err
	}

	logTime.AddLog("fetch link menu")
	linkMenuDetail, err := p.repo.FetchLinkMenuDetail(param, user)
	if log.IfError(err) {
		return nil, err
	}

	logTime.AddLog("fetch link menu detail")
	detailMap := make(map[int][]models.LinkMenuDetailEntity)
	for _, raw := range *linkMenuDetail {
		if _, ok := detailMap[raw.LinkmenuFkid]; !ok {
			detailMap[raw.LinkmenuFkid] = make([]models.LinkMenuDetailEntity, 0)
		}
		detailMap[raw.LinkmenuFkid] = append(detailMap[raw.LinkmenuFkid], raw)
	}

	for i, raw := range *linkMenu {
		(*linkMenu)[i].LinkMenuDetail = detailMap[raw.LinkmenuID]
	}

	logTime.Print()
	return linkMenu, nil
}

func (p *productUseCase) FetchProductUnitConvertion(param *domain.UnitConvertinRequest, user *domain.UserSession) ([]domain.UnitConversionResponse, error) {
	convertions, err := p.repo.FetchProductUnitConvertion(param, user)
	//check if error
	if log.IfError(err) {
		return nil, err
	}

	if convertions == nil || len(*convertions) == 0 {
		return []domain.UnitConversionResponse{}, nil
	}

	// Get unit IDs from convertions and save them to an integer array
	unitIDs := make([]int, 0, len(*convertions))
	for _, conv := range *convertions {
		unitIDs = append(unitIDs, int(conv.UnitFKID))
	}

	// Fetch units from repository with the parameter IDs (unitIDs)
	units, err := p.repo.FetchUnitsByID(unitIDs)
	if err != nil {
		return nil, err
	}

	unitMap := make(map[int]models.UnitEntity)
	for _, u := range *units {
		unitMap[u.UnitID] = u
	}

	//Create the result response by combining convertions and units
	result := make([]domain.UnitConversionResponse, 0, len(*convertions))
	for _, conv := range *convertions {
		response := domain.UnitConversionResponse{
			ProductUnitConversionEntity: conv,
			UnitEntity:                  unitMap[int(conv.UnitFKID)],
		}
		result = append(result, response)
	}

	return result, nil
}

// FetchGratuity implements product.UseCase.
func (p *productUseCase) FetchGratuity(param domain.GratuityRequest, user domain.UserSession) ([]map[string]interface{}, error) {
	logTime := log.TimeInit()
	defer logTime.Print()

	//fetch gratuity first
	gratuityList, err := p.repo.FetchGratuity(&domain.GratuityFilter{
		AdminId: int(user.AdminId),
		// TaxStatuses: []string{param.TaxStatus},
	})

	logTime.AddLog("FetchGratuity")
	if log.IfError(err) || len(*gratuityList) == 0 {
		log.Info("maybe empty:%v | param: %v", len(*gratuityList), param)
		return []map[string]interface{}{}, nil
	}

	ids := make([]int, 0)
	gratuityById := make(map[int]models.GratuityEntity)
	for _, g := range *gratuityList {
		ids = append(ids, g.GratuityID)
		gratuityById[g.GratuityID] = g
	}

	gratuityDetails, err := p.repo.FetchGratuityDetail(&domain.GratuityDetailFilter{
		OutletId:    param.OutletId,
		GratuityIds: ids,
	})
	logTime.AddLog("FetchGratuityDetail")

	//sort based on id
	sort.Slice(*gratuityDetails, func(i, j int) bool {
		return (*gratuityDetails)[i].TaxFkid < (*gratuityDetails)[j].TaxFkid
	})

	result := make([]map[string]interface{}, 0)
	products := make([]map[string]interface{}, 0)
	for i, gratuity := range *gratuityDetails {
		if i == len(*gratuityDetails)-1 || gratuity.TaxFkid != (*gratuityDetails)[i+1].TaxFkid {
			gratuityMap := gratuityById[gratuity.TaxFkid].ToMap()
			gratuityMap["product"] = products
			result = append(result, gratuityMap)
			products = make([]map[string]interface{}, 0)
		} else {
			products = append(products, map[string]interface{}{
				"product_detail_fkid": gratuity.ProductDetailFkid,
			})
		}
	}

	logTime.AddLog("modify data")
	return result, err

	// start := time.Now()
	// gratuities, err := p.repo.FetchGratuityWithProduct(param, user)
	// if log.IfError(err) {
	// 	return nil, err
	// }
	// log.Info("[benchmark] FetchGratuityWithProduct tooks %v, params: %v", time.Since(start), param)

	// //sort based on id
	// sort.Slice(gratuities, func(i, j int) bool {
	// 	return gratuities[i].GratuityID < gratuities[j].GratuityID
	// })

	// result := make([]map[string]interface{}, 0)
	// products := make([]map[string]interface{}, 0)
	// for i, gratuity := range gratuities {
	// 	if i == len(gratuities)-1 || gratuity.GratuityID != gratuities[i+1].GratuityID {
	// 		gratuityMap := gratuity.ToMap()
	// 		gratuityMap["product"] = products
	// 		result = append(result, gratuityMap)
	// 		products = make([]map[string]interface{}, 0)
	// 	} else {
	// 		products = append(products, map[string]interface{}{
	// 			"product_detail_fkid": gratuity.ProductDetailFkid,
	// 		})
	// 	}
	// }
	// return result, nil
}

func (p *productUseCase) FetchRecommendation(param domain.RecommendationRequest, user domain.UserSession) ([]map[string]interface{}, error) {
	//currently outlet_id is required, we only fetch product recommendation within outlet page
	if param.OutletId == 0 {
		return nil, exception.WithCode{Code: exception.ErrorInvalidRequest, Message: "outlet_id is required"}
	}

	//try fetch from cache
	result, err := p.repo.FetchProductRecommendation(param, user)
	log.Info("result from cache size: %v, err: %v", len(result), err)
	if err == nil && len(result) > 0 {
		log.Info("fetch product recommendation from cache... %v", len(result))
		return result, nil
	}

	//fetch recommendation products
	recommendations, err := p.FetchProductRecommendationList(param)
	if log.IfError(err) {
		return nil, err
	}

	fmt.Println(recommendations)

	//fetch product related info based on recommendation
	productIds := make([]int, 0)
	for _, rec := range recommendations {
		productIds = append(productIds, rec.ProductId)
	}

	products, err := p.repo.FetchProduct(&domain.ProductRequest{
		ProductIds: productIds,
		OutletId:   param.OutletId,
	}, &user)
	if log.IfError(err) {
		return nil, err
	}

	productMap := make(map[int]models.Product)
	for _, product := range *products {
		productMap[product.ProductID] = product
	}

	tempResp := make([]map[string]interface{}, 0)
	for _, rec := range recommendations {
		product := productMap[rec.ProductId]
		if product.ProductDetailID == 0 {
			continue
		}
		tempResp = append(tempResp, product.ToMap())
	}

	go p.repo.SaveProductRecommendation(param, user, tempResp)

	//make sure the product is belong to the outlet
	return tempResp, nil
}

func (p *productUseCase) FetchProductRecommendationList(param domain.RecommendationRequest) ([]domain.ProductRecommendation, error) {
	//fetch initial product recommender
	apiUrl := "getRecomendationInitial"
	query := map[string]interface{}{}
	if param.MemberId > 0 {
		apiUrl = "getRecomendationByMemberId"
		query["id"] = param.MemberId
	}

	//
	if len(param.ProductIds) > 0 {
		ids := make([]string, 0)
		for _, id := range param.ProductIds {
			ids = append(ids, cast.ToString(id))
		}
		apiUrl = "getRecomendationByProductIds"
		query["ids"] = strings.Join(ids, ",")
	}
	fmt.Println(apiUrl, query)

	request := utils.HttpRequest{
		Method: "GET",
		Url:    fmt.Sprintf("%s/%s", os.Getenv("API_RECOMMENDATION"), apiUrl),
		Params: query,
	}

	response, err := request.Execute()
	if err != nil {
		return nil, err
	}

	log.Info("response: %v", utils.SimplyToJson(response))
	if response.StatusCode != 200 {
		return nil, errors.New("failed to fetch recommendation products")
	}

	var responseBody struct {
		Status  string                         `json:"status"`
		Message string                         `json:"message"`
		Data    []domain.ProductRecommendation `json:"data"`
	}
	err = json.Unmarshal([]byte(response.Body), &responseBody)
	if err != nil {
		log.Info("error unmarshal: %v, response: %v", err, response.Body)
		return nil, err
	}

	return responseBody.Data, nil
}
