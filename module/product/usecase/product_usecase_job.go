package usecase

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
)

var muRunNotifyProduct sync.Mutex

func (p *productUseCase) RunNotifyProduct() {
	muRunNotifyProduct.Lock()
	defer muRunNotifyProduct.Unlock()

	availableProducts, err := p.repo.FetchNotifyAvailableProduct()
	log.IfError(err)

	log.Debug("notifyProduct - %d data to be sent...", len(*availableProducts))

	scheduledMsgs := make([]domain.ScheduleMessage, 0)
	for _, notify := range *availableProducts {
		notifData := utils.SimplyToJson(map[string]interface{}{
			"notification_type": "product",
			"notification_data": map[string]interface{}{
				"id": notify.ProductDetailFkid,
			},
		})
		sendMedias := map[string]string{
			"push_notif": notify.FirebaseToken,
			"email":      notify.MemberEmail,
			"whatsapp":   notify.MemberPhone,
		}
		for media, receiver := range sendMedias {
			if receiver == "" {
				continue
			}
			scheduledMsgs = append(scheduledMsgs, domain.ScheduleMessage{
				Title:         "yang kamu tunggu-tunggu sudah ready lo!",
				Message:       fmt.Sprintf("Hay %s, item %s sudah bisa di pesan lo di %s, \nBuruan di order ya sebelum kehabisan stok!", notify.MemberName, notify.ProductName, notify.OutletName),
				TimeDeliver:   time.Now().Unix() * 1000,
				DataCreated:   time.Now().Unix() * 1000,
				Media:         media,
				Receiver:      receiver,
				AdminFkid:     notify.AdminFkid,
				SentVia:       "WA_USER",
				Attachments:   utils.SimplyToJson([]string{notify.Photo}),
				MessageDetail: notifData,
			})
		}

		err = p.repo.RemoveNotifyProduct(notify.MemberFkid, notify.ProductDetailFkid)
		log.IfError(err)
	}

	log.Info("total scheduledMsg: %v", len(scheduledMsgs))
	err = p.repoCampaign.AddScheduleMessage(scheduledMsgs...)
	log.IfError(err)
}
