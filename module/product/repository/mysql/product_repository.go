package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/goccy/go-json"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/constant"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
)

type productRepository struct {
	db    db.Repository
	cache domain.CacheInterface
}

func NewMysqlProductRepository(conn *sql.DB, cache domain.CacheInterface) product.Repository {
	return &productRepository{db.Repository{Conn: conn, CacheDb: cache}, cache}
}

func (p productRepository) FetchProduct(param *domain.ProductRequest, user *domain.UserSession) (*[]models.Product, error) {
	sql := `SELECT p.product_id, p.name, pdv.variant_name, pdv.variant_id,p.photo, p.min_qty_order, p.description, 
	p.stock_management, pd.stock_qty, pd.product_detail_id, pd.outlet_fkid, pd.price_sell,
	p.unit_fkid, p.product_subcategory_fkid, pd.stock
	FROM products p 
	JOIN products_detail pd on p.product_id=pd.product_fkid
	LEFT JOIN products_detail_variant pdv on pd.variant_fkid=pdv.variant_id
	JOIN outlets o on pd.outlet_fkid=o.outlet_id
	where p.admin_fkid= @adminId
	and p.app_show=1 
	and o.app_show=1
	and p.data_status='on'
	and o.data_status='on'
	and (pd.active = 'on_all' or pd.active = 'on_sales')
	and pd.data_status='on' `

	if len(param.ProductIds) > 0 {
		sql += " AND p.product_id in @productIds "
	}
	if param.OutletId > 0 {
		sql += " AND pd.outlet_fkid = @outletId "
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    user.AdminId,
		"productIds": param.ProductIds,
		"outletId":   param.OutletId,
	})
	// p.db.LogQuery = true
	var result []models.Product
	err := p.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (p productRepository) FetchProductByIds(productIds []int) ([]map[string]interface{}, error) {
	if len(productIds) == 0 {
		return []map[string]interface{}{}, nil
	}

	whereIn := strings.Repeat("?,", len(productIds))
	whereIn = strings.TrimRight(whereIn, ",")
	sql := `SELECT
	p.product_id,
	p.name,
	(u.name) AS unit,
	photo,
	min_qty_order,
	p.description,
	min(pd.price_sell) AS price_sell,
	floor(AVG(price_sell)) AS price_sell_avg,
	min(price_sell) as price_sell_start,
	max(price_sell) as price_sell_end
FROM
	products p
	JOIN unit u ON p.unit_fkid = u.unit_id
	JOIN products_detail pd ON p.product_id = pd.product_fkid
WHERE
	product_id in (` + whereIn + `)
	and p.app_show = 1
GROUP BY
	p.product_id`
	return db.QueryArray(sql, utils.ToInterfaceArray(productIds)...)
}

func (p productRepository) FetchProductByDetailIds(productDetailIds []int) ([]map[string]interface{}, error) {
	if len(productDetailIds) == 0 {
		return []map[string]interface{}{}, nil
	}

	whereIn := strings.Repeat("?,", len(productDetailIds))
	whereIn = strings.TrimRight(whereIn, ",")

	sql := `SELECT
	concat(p.name, COALESCE(concat(' (', pdv.variant_name, ')'), '')) AS name,
	p.photo,
	pd.price_sell,
	pd.product_detail_id,
	pd.product_fkid,
	p.description,
	u.name AS unit,
	p.min_qty_order,
	pd.stock_qty,
	p.stock_management,
	IF(p.stock_management = 1 and (pd.stock_qty <= 0 or pd.stock_qty < p.min_qty_order), 'unavailable', pd.stock) as stock_status
FROM
	products_detail pd
	JOIN products p ON p.product_id = pd.product_fkid
	JOIN unit u ON p.unit_fkid = u.unit_id
	LEFT JOIN products_detail_variant pdv ON pd.variant_fkid = pdv.variant_id
where pd.product_detail_id in (` + whereIn + `)`
	return db.QueryArray(sql, utils.ToInterfaceArray(productDetailIds)...)
}

func (p productRepository) FetchProductSubcategory(param *domain.ProductSubcategoryRequest, user *domain.UserSession) ([]map[string]interface{}, error) {
	// sql := `SELECT ps.name, csc.position from products_subcategory ps
	// left join crm_subcategory_config csc on ps.product_subcategory_id=csc.subcategory_fkid
	// where ps.admin_fkid= @adminId and data_status='on' and data_type='product'
	// order by csc.position, ps.name`

	sql := `
	SELECT
	ps.name,
	ANY_VALUE(coalesce(csc.position, 999999)) as position,
	count(DISTINCT p.product_id) as total_product,
	GROUP_CONCAT(distinct pd.outlet_fkid) as outlet_ids,
	ps.product_subcategory_id
	FROM
		products_subcategory ps
	LEFT JOIN crm_subcategory_config csc ON ps.product_subcategory_id = csc.subcategory_fkid
	JOIN products p on p.product_subcategory_fkid=ps.product_subcategory_id
	JOIN products_detail pd ON pd.product_fkid = p.product_id
WHERE
	ps.admin_fkid = @adminId	
	AND ps.data_status='on'
	AND p.data_status = 'on'
	AND ps.data_type = 'product'
	AND pd.data_status='on'
	${WHERE}
group by ps.product_subcategory_id
having total_product > 0 
ORDER BY
	position,
	ps.name `

	whereSql := ""
	if len(param.OutetIds) > 0 {
		whereSql += " AND pd.outlet_fkid in @outletId "
	}

	sql = strings.Replace(sql, "${WHERE}", whereSql, 1)

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":  user.AdminId,
		"outletId": param.OutetIds,
	})

	return db.QueryArray(sql, params...)
}

func (p productRepository) UpdateSubcategory(param *[]domain.Subcategory, user *domain.UserSession) error {
	return db.WithTransaction(func(t db.Transaction) error {
		for _, row := range *param {
			conf, err := db.Query("select id from crm_subcategory_config where subcategory_fkid = ?", row.ProductSubcategoryId)
			if log.IfError(err) {
				return err
			}
			if len(conf) > 0 {
				t.Update("crm_subcategory_config", map[string]interface{}{
					"position":      row.Position,
					"data_modified": time.Now().Unix() * 1000,
				}, "subcategory_fkid = ?", row.ProductSubcategoryId)
			} else {
				t.Insert("crm_subcategory_config", map[string]interface{}{
					"subcategory_fkid": row.ProductSubcategoryId,
					"position":         row.Position,
					"data_created":     time.Now().Unix() * 1000,
					"data_modified":    time.Now().Unix() * 1000,
				})
			}
		}
		return nil
	})
}

func (p productRepository) FetchSubcategoryByIds(ids []int) (*[]models.SubcategoryWithPosition, error) {
	sql := `SELECT ps.product_subcategory_id, ps.name, csc.position 
	from products_subcategory ps 
	left join crm_subcategory_config csc on csc.subcategory_fkid=ps.product_subcategory_id
	where ps.product_subcategory_id IN @ids`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})

	var result []models.SubcategoryWithPosition
	err := p.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (p productRepository) FetchLinkMenu(param *domain.ProductLinkMenuRequest, user *domain.UserSession) (*[]models.LinkMenuEntity, error) {
	sql := `select * from products_linkmenu where admin_fkid = @adminId `
	if param.OutletId > 0 {
		sql += " AND outlet_fkid = @outletId "
	}
	if param.ProductDetailId > 0 {
		sql += " AND product_detail_fkid = @productDetailId "
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":         user.AdminId,
		"outletId":        param.OutletId,
		"productDetailId": param.ProductDetailId,
	})

	var result []models.LinkMenuEntity
	err := p.db.QueryCache("linkMenu", 24*time.Hour, sql, params...).Model(&result)
	// err := p.db.Prepare(sql, params...).GetWithCache("linkMenu", 24*time.Hour, &result)
	return &result, err
}

func (p productRepository) FetchLinkMenuDetail(param *domain.ProductLinkMenuRequest, user *domain.UserSession) (*[]models.LinkMenuDetailEntity, error) {
	sql := `select pld.* from products_linkmenu_detail pld 
	join products_linkmenu pl on pl.linkmenu_id=pld.linkmenu_fkid
	where pl.admin_fkid = ?`
	var result []models.LinkMenuDetailEntity
	err := p.db.QueryCache("linkMenuDetail", 24*time.Hour, sql, user.AdminId).Model(&result)
	// err := p.db.Prepare(sql, user.AdminId).GetWithCache("linkeMenuDetail", 24*time.Hour, &result)
	return &result, err
}

func (p *productRepository) FetchProductUnitConvertion(param *domain.UnitConvertinRequest, user *domain.UserSession) (*[]models.ProductUnitConversionEntity, error) {
	sql := `SELECT puc.*, floor(puc.qty) as qty from products_unit_convertion puc 
join unit u on puc.unit_fkid=u.unit_id
where u.admin_fkid=?`

	var result []models.ProductUnitConversionEntity
	err := p.db.QueryCache("productUnitConvertion", 24*time.Hour, sql, user.AdminId).Model(&result)
	// err := p.db.Prepare(sql, user.AdminId).GetWithCache("productUnitConvertion", 24*time.Hour, &result)
	// err := p.db.Prepare(sql).Get(&result)

	return &result, err
}

func (p *productRepository) FetchUnitsByID(unitIDs []int) (*[]models.UnitEntity, error) {
	// Create a placeholder for the unit placeholders
	// unitPlaceholders := make([]string, len(unitIDs))
	// unitParams := make([]interface{}, len(unitIDs))

	// // Generate the unit placeholders and parameters
	// for i, unitID := range unitIDs {
	// 	unitPlaceholders[i] = "?"
	// 	unitParams[i] = unitID
	// }

	// sql := fmt.Sprintf("SELECT * FROM unit WHERE unit_id IN (%s)", strings.Join(unitPlaceholders, ", "))
	if len(unitIDs) == 0 {
		return &[]models.UnitEntity{}, nil
	}

	sql := `SELECT * FROM unit WHERE unit_id IN @ids`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": unitIDs,
	})

	var units []models.UnitEntity
	err := p.db.QueryCache("unitById", 24*time.Hour, sql, params...).Model(&units)
	// err := p.db.Prepare(sql, params...).GetWithCache("unitById", 24*time.Hour, &units)

	return &units, err
}

func (p productRepository) FetchNotifyAvailableProduct() (*[]models.NotifyProductAvailable, error) {
	sql := `select min(p.name)            as product_name,
	min(o.name)            as outlet_name,
	min(p.photo) as photo,
	cnp.product_detail_fkid,
	cnp.member_fkid,
	min(cnp.admin_fkid)    as admin_fkid,
	mm.name                as member_name,
	mm.email               as member_email,
	mm.phone               as member_phone,
	min(mm.firebase_token) as firebase_token
from crm_notify_product cnp
	  join products_detail pd on cnp.product_detail_fkid = pd.product_detail_id
	  join products p on pd.product_fkid = p.product_id
	  join outlets o on pd.outlet_fkid = o.outlet_id
	  join (
 select m.name, m.email, m.phone, md.firebase_token, md.admin_fkid, m.member_id
 from members m
		  join members_detail md on m.member_id = md.member_fkid
) mm on mm.member_id = cnp.member_fkid and mm.admin_fkid = cnp.admin_fkid
where pd.stock_qty >= cnp.qty
group by cnp.product_detail_fkid, member_fkid `

	var result []models.NotifyProductAvailable
	err := p.db.Prepare(sql).Get(&result)
	return &result, err
}

func (p productRepository) RemoveNotifyProduct(memberId int, productDetailId int) error {
	//delete from crm_notify_product where member_fkid = ? and product_detail_fkid = ?
	_, err := db.Delete("crm_notify_product", "member_fkid = ? and product_detail_fkid = ?", memberId, productDetailId)
	return err
}

func (p productRepository) FetchProductAvailable(filter *domain.ProductAvailableFilter) (*[]models.ProductAvailableEntity, error) {
	sql := `
	SELECT cpa.* from crm_products_available cpa 
	join products p on p.product_id=cpa.product_fkid
	where p.admin_fkid= @adminId and p.data_status='on' and p.app_show=1
	and cpa.day= @day `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId": filter.AdminId,
		"day":     filter.Day,
	})
	var result []models.ProductAvailableEntity
	err := p.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (p productRepository) SaveProductRecommendation(param domain.RecommendationRequest, user domain.UserSession, tempResp []map[string]interface{}) error {
	args := []interface{}{
		param.OutletId,
		param.ProductId,
		param.ProductIds,
		user.AdminId,
		user.MemberId,
	}

	cacheKey := fmt.Sprintf("crmapi-%s_%s_%s", constant.GetEnv(), "product_recommendation", utils.ConcatArgs(args))
	return p.cache.Set(cacheKey, utils.SimplyToJson(tempResp), 24*time.Hour*7)
}

func (p productRepository) FetchProductRecommendation(param domain.RecommendationRequest, user domain.UserSession) ([]map[string]interface{}, error) {
	args := []interface{}{
		param.OutletId,
		param.ProductId,
		param.ProductIds,
		user.AdminId,
		user.MemberId,
	}

	cacheKey := fmt.Sprintf("crmapi-%s_%s_%s", constant.GetEnv(), "product_recommendation", utils.ConcatArgs(args))
	dataCache, err := p.cache.Get(cacheKey)
	if err == nil && dataCache != "" {
		var result []map[string]interface{}
		err = json.Unmarshal([]byte(dataCache), &result)
		if !log.IfError(err) {
			return result, nil
		}
	}

	return nil, nil
}
