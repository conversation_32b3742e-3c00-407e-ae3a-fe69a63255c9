package mysql

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func (p productRepository) FetchGratuity(filter *domain.GratuityFilter) (*[]models.GratuityEntity, error) {
	sql := `SELECT * FROM gratuity WHERE data_status = 'on' AND admin_fkid = @adminId $WHERE `

	var sqlWhere strings.Builder
	if len(filter.TaxCategories) > 0 {
		sqlWhere.WriteString(" AND tax_category IN @categories ")
	}
	if len(filter.TaxStatuses) > 0 {
		sqlWhere.WriteString(" AND tax_status IN @statuses ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    filter.AdminId,
		"categories": filter.TaxCategories,
		"statuses":   filter.TaxStatuses,
		"WHERE":      sqlWhere.String(),
	})

	var result []models.GratuityEntity
	err := p.db.QueryCache("gratuity", 8*time.Hour, sql, params...).Model(&result)
	if !log.IfError(err) {
		return &result, err
	}

	//TODO: remove below code, once above code works fine
	//fetch from cache
	key := fmt.Sprintf("%s-gratuity-%s", os.Getenv("ENV"), utils.ConcatArgs(params))
	cachedData, err := p.cache.Get(key)
	if err != nil && !(err == redis.Nil || strings.Contains(err.Error(), "unknown port")) {
		log.IfError(err)
	}
	log.Info("cachedData GratuityEntity %v", len(cachedData))
	if err == nil && cachedData != "" {
		err = json.Unmarshal([]byte(cachedData), &result)
		if !log.IfError(err) {
			fmt.Println("get gratuity from cache...")
			return &result, nil
		}
	}

	p.db.LogQuery = true
	err = p.db.Prepare(sql, params...).Get(&result)
	//cache the result
	p.cache.Set(key, utils.SimplyToJson(result), 50*time.Minute)

	return &result, err
}

func (p productRepository) FetchProductGratuity(filter *domain.ProductGratuityFilter) (*[]models.ProductDetailTaxEntity, error) {
	sql := `SELECT * from products_detail_taxdetail where data_status='on'`

	if len(filter.GratuityId) > 0 {
		sql += " AND tax_fkid In @taxIds "
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"taxIds": filter.GratuityId,
	})

	var result []models.ProductDetailTaxEntity
	err := p.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

// FetchGratuityWithProduct implements product.Repository.
func (p *productRepository) FetchGratuityWithProduct(param domain.GratuityRequest, user domain.UserSession) ([]models.ProductGratuity, error) {
	sql := `SELECT pdt.product_detail_fkid,
       g.gratuity_id,
       g.name,
       tax_category,
       tax_status,
       tax_type,
       jumlah
FROM   products_detail_taxdetail pdt
       JOIN gratuity g
         ON pdt.tax_fkid = g.gratuity_id
       JOIN products_detail pd
         ON pd.product_detail_id = pdt.product_detail_fkid
WHERE  g.data_status = 'on'       
       and g.admin_fkid = @adminId
 `

	if param.TaxStatus != "" {
		sql += " and tax_status = @taxStatus "
	}

	if param.OutletId > 0 {
		sql += " AND pd.outlet_fkid = @outletId "
	}

	//put order query last
	// sql += " ORDER  BY g.gratuity_id "

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   user.AdminId,
		"taxStatus": param.TaxStatus,
		"outletId":  param.OutletId,
	})

	var result []models.ProductGratuity

	//fetch from cache
	key := fmt.Sprintf("%s-gratuity-product-%s", os.Getenv("ENV"), utils.ConcatArgs(params))
	cachedData, err := p.cache.Get(key)
	fmt.Println("cache------ ", len(cachedData))
	if err != nil && !(err == redis.Nil || strings.Contains(err.Error(), "unknown port")) {
		log.IfError(err)
	}
	if err == nil && cachedData != "" {
		err = json.Unmarshal([]byte(cachedData), &result)
		if !log.IfError(err) {
			fmt.Println("get gratuity from cache...")
			return result, nil
		}
	}

	err = p.db.Query(sql, params...).Model(&result)

	//cache the result
	if len(result) > 0 {
		go p.cache.Set(key, utils.SimplyToJson(result), 35*time.Minute)
	}

	return result, err
}

// FetchGratuityDetail implements product.Repository.
func (p *productRepository) FetchGratuityDetail(filter *domain.GratuityDetailFilter) (*[]models.GratuityDetailEntity, error) {
	sql := `select pdt.* from products_detail_taxdetail pdt $JOIN where tax_fkid IN @ids $WHERE `

	var sqlWhere strings.Builder
	var sqlJoin strings.Builder

	if filter.OutletId > 0 {
		sqlWhere.WriteString(" AND pd.outlet_fkid = @outletId ")
		sqlJoin.WriteString(" JOIN products_detail pd ON pd.product_detail_id = pdt.product_detail_fkid ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids":      filter.GratuityIds,
		"outletId": filter.OutletId,
		"JOIN":     sqlJoin.String(),
		"WHERE":    sqlWhere.String(),
	})

	var result []models.GratuityDetailEntity
	err := p.db.QueryCache("gratuityDetail", 50*time.Minute, sql, params...).Model(&result)
	if !log.IfError(err) {
		return &result, err
	}

	//fetch from cache
	key := fmt.Sprintf("%s-gratuity-detail-%s", os.Getenv("ENV"), utils.ConcatArgs(params))
	cachedData, err := p.cache.Get(key)
	if err != nil && !(err == redis.Nil || strings.Contains(err.Error(), "unknown port")) {
		log.IfError(err)
	}
	log.Info("cachedData GratuityDetailEntity %v", len(cachedData))
	if err == nil && cachedData != "" {
		err = json.Unmarshal([]byte(cachedData), &result)
		if !log.IfError(err) {
			fmt.Println("get gratuity from cache...")
			return &result, nil
		}
	}

	err = p.db.Prepare(sql, params...).Get(&result)

	//cache the result
	if len(result) > 0 {
		go p.cache.Set(key, utils.SimplyToJson(result), 40*time.Minute)
	}
	return &result, err
}
