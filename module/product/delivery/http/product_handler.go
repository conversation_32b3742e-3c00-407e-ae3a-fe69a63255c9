package http

import (
	"encoding/json"
	"os"
	"strings"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
)

type productHandler struct {
	uc product.UseCase
}

func NewHttpProductHandler(app *fasthttprouter.Router, useCase product.UseCase) {
	handler := &productHandler{useCase}
	app.GET("/v1/subcategory", auth.ValidatePublicKey(handler.FetcProductSubcategory))
	app.PUT("/v1/subcategory", auth.ValidateToken(handler.UpdateSubcategory))

	app.GET("/v1/product-link-menu", auth.ValidatePublicKey(handler.FetchProductLinkMenu))

	//unit of product
	app.GET("/v1/product-unit-convertion", auth.ValidatePublicKey(handler.FetchProductUnitConvertion))

	//product
	app.GET("/v3/product", auth.ValidatePublicKey(handler.FetchProduct))

	//gratuity
	app.GET("/v1/product/gratuity", auth.ValidatePublicKey(handler.FetchGratuity))

	//recommendation
	app.GET("/v1/product/recommendation", auth.ValidatePublicKey(handler.FetchRecommendation))

	//cron, for development only
	if os.Getenv("ENV") == "development" {
		app.GET("/cron/product-notify", handler.RunNotifyProduct)
	}
}

func (h *productHandler) FetchProduct(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	params := domain.ProductRequest{
		Includes: strings.Split(string(ctx.QueryArgs().Peek("include")), ","),
	}

	result, err := h.uc.FetchProduct(&params, &user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *productHandler) FetcProductSubcategory(ctx *fasthttp.RequestCtx) {
	params := parser.ParseParamFastHttp(ctx)
	outletIdsStr := cast.ToString(params["outlet_ids"])
	outletIds := make([]int, 0)

	if ids := strings.Split(outletIdsStr, ","); outletIdsStr != "" && len(ids) > 0 {
		for _, id := range ids {
			id = strings.TrimSpace(id)
			if id != "" {
				outletIds = append(outletIds, utils.ToInt(id))
			}
		}
	}

	requestParam := &domain.ProductSubcategoryRequest{
		OutetIds: outletIds,
	}
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchProductSubcategory(requestParam, &user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *productHandler) UpdateSubcategory(ctx *fasthttp.RequestCtx) {
	var subcategory []domain.Subcategory
	log.IfError(json.Unmarshal(ctx.Request.Body(), &subcategory))
	user := domain.UserSessionFastHttp(ctx)
	err := h.uc.UpdateSubcategory(&subcategory, &user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *productHandler) FetchProductLinkMenu(ctx *fasthttp.RequestCtx) {
	param := &domain.ProductLinkMenuRequest{
		OutletId:        cast.ToInt(ctx.QueryArgs().Peek("outlet_id")),
		ProductDetailId: cast.ToInt(ctx.QueryArgs().Peek("product_detail_id")),
	}
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchProductLinkMenu(param, &user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *productHandler) FetchProductUnitConvertion(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchProductUnitConvertion(&domain.UnitConvertinRequest{}, &user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *productHandler) RunNotifyProduct(ctx *fasthttp.RequestCtx) {
	h.uc.RunNotifyProduct()
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "job is running..."})
}

func (h *productHandler) FetchGratuity(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchGratuity(domain.GratuityRequest{}, user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *productHandler) FetchRecommendation(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	productIds := strings.Split(string(ctx.QueryArgs().Peek("product_ids")), ",")
	productIdsInt := make([]int, 0)
	for _, id := range productIds {
		if productId := cast.ToInt(id); productId > 0 {
			productIdsInt = append(productIdsInt, productId)
		}
	}

	result, err := h.uc.FetchRecommendation(domain.RecommendationRequest{
		OutletId:   cast.ToInt(string(ctx.QueryArgs().Peek("outlet_id"))),
		ProductId:  cast.ToInt(string(ctx.QueryArgs().Peek("product_id"))),
		ProductIds: productIdsInt,
	}, user)

	if err != nil {
		if errWithCode, ok := err.(exception.WithCode); ok {
			ctx.SetStatusCode(errWithCode.Code)
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
