package product

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type UseCase interface {
	FetchProduct(param *domain.ProductRequest, user *domain.UserSession) (*[]models.ProductAvailability, error)

	FetchProductSubcategory(param *domain.ProductSubcategoryRequest, user *domain.UserSession) ([]map[string]interface{}, error)
	UpdateSubcategory(param *[]domain.Subcategory, user *domain.UserSession) error

	FetchProductLinkMenu(param *domain.ProductLinkMenuRequest, user *domain.UserSession) (*[]models.LinkMenuEntity, error)

	FetchProductUnitConvertion(param *domain.UnitConvertinRequest, user *domain.UserSession) ([]domain.UnitConversionResponse, error)

	FetchGratuity(param domain.GratuityRequest, user domain.UserSession) ([]map[string]interface{}, error)
	RunNotifyProduct()

	FetchRecommendation(param domain.RecommendationRequest, user domain.UserSession) ([]map[string]interface{}, error)
}
