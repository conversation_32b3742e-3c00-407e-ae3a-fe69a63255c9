package product

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type Repository interface {
	FetchProduct(param *domain.ProductRequest, user *domain.UserSession) (*[]models.Product, error)
	FetchProductByIds(productIds []int) ([]map[string]interface{}, error)
	FetchProductByDetailIds(productDetailIds []int) ([]map[string]interface{}, error)
	FetchProductSubcategory(param *domain.ProductSubcategoryRequest, user *domain.UserSession) ([]map[string]interface{}, error)

	FetchProductAvailable(filter *domain.ProductAvailableFilter) (*[]models.ProductAvailableEntity, error)

	UpdateSubcategory(param *[]domain.Subcategory, user *domain.UserSession) error
	FetchSubcategoryByIds(ids []int) (*[]models.SubcategoryWithPosition, error)

	FetchGratuity(filter *domain.GratuityFilter) (*[]models.GratuityEntity, error)
	FetchGratuityDetail(filter *domain.GratuityDetailFilter) (*[]models.GratuityDetailEntity, error)
	FetchProductGratuity(filter *domain.ProductGratuityFilter) (*[]models.ProductDetailTaxEntity, error)
	FetchGratuityWithProduct(param domain.GratuityRequest, user domain.UserSession) ([]models.ProductGratuity, error)

	FetchLinkMenu(param *domain.ProductLinkMenuRequest, user *domain.UserSession) (*[]models.LinkMenuEntity, error)
	FetchLinkMenuDetail(param *domain.ProductLinkMenuRequest, user *domain.UserSession) (*[]models.LinkMenuDetailEntity, error)

	FetchProductUnitConvertion(param *domain.UnitConvertinRequest, user *domain.UserSession) (*[]models.ProductUnitConversionEntity, error)
	FetchUnitsByID(unitIDs []int) (*[]models.UnitEntity, error)

	FetchNotifyAvailableProduct() (*[]models.NotifyProductAvailable, error)
	RemoveNotifyProduct(memberId int, productDetailId int) error

	SaveProductRecommendation(param domain.RecommendationRequest, user domain.UserSession, tempResp []map[string]interface{}) error
	FetchProductRecommendation(param domain.RecommendationRequest, user domain.UserSession) ([]map[string]interface{}, error)
}
