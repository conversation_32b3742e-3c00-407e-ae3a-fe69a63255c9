package http

import (
	"encoding/json"
	"strconv"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type inboxHandler struct {
	uc domain.InboxUseCase
}

func NewHttpInboxHandler(app *fasthttprouter.Router, useCase domain.InboxUseCase) {
	handler := &inboxHandler{useCase}

	app.GET("/v1/inbox", auth.ValidateToken(handler.FetchAllInbox))
	app.GET("/v1/inbox/:id", auth.ValidateToken(handler.FetchInboxByID))
}

func (h *inboxHandler) FetchAllInbox(ctx *fasthttp.RequestCtx) {
	// Get user ID from the authenticated session
	userID := domain.UserSessionFastHttp(ctx).MemberId

	// Call the use case to fetch all inbox for the user ID
	result, err := h.uc.FetchAllInbox(userID)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *inboxHandler) FetchInboxByID(ctx *fasthttp.RequestCtx) {
	// Get user ID from the authenticated session
	userID := domain.UserSessionFastHttp(ctx).MemberId

	// Get inbox ID from the URL path parameter
	inboxID, err := strconv.Atoi(string(ctx.QueryArgs().Peek("id")))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "Invalid inbox ID"})
		return
	}

	// Call the use case to fetch the inbox by ID for the user ID
	result, err := h.uc.FetchInboxByID(userID, inboxID)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
