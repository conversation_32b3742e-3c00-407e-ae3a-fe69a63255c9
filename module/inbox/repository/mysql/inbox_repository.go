package mysql

import (
	"database/sql"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type inboxRepository struct {
	db db.Repository
}

func NewMysqlInboxRepository(conn *sql.DB) domain.InboxRepository {
	return &inboxRepository{db.Repository{Conn: conn}}
}

func (r *inboxRepository) FetchAllInbox(userID int) ([]models.InboxEntity, error) {
	// Implement the logic to fetch all inbox for the user ID from the database
	sql := "SELECT * FROM inbox WHERE user_id = ?"
	var result []models.InboxEntity
	err := r.db.Prepare(sql, userID).Get(&result)
	return result, err
}

func (r *inboxRepository) FetchInboxByID(userID, inboxID int) (models.InboxEntity, error) {
	// Implement the logic to fetch the inbox by ID for the user ID from the database
	sql := "SELECT * FROM inbox WHERE user_id = ? AND inbox_id = ?"
	var result models.InboxEntity
	err := r.db.Prepare(sql, userID, inboxID).Get(&result)
	return result, err
}
