package promotion

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type PromotionRepository interface {
	FetchPromotionOutlet(promotionId ...interface{}) ([]models.PromotionOutlet, error)
	FetchPromotionProduct(promotionId ...interface{}) ([]models.PromotionProduct, error)
	FetchPromotionMember(promotionId ...interface{}) ([]map[string]interface{}, error)

	FetchPromotionType() ([]models.PromotionType, error)

	GenerateSecretCode(secretCode string, timeExpired int64, promotionBuyId int, memberId int64) error
	CheckExistingSecretCodes(secretCodes []string) ([]string, error)
	FetchPromotionRole(param ...domain.PromotionRoleParam) ([]models.PromotionRole, error)
	FetchPromotionWithDetail(param models.PromotionFilter) ([]map[string]interface{}, error)
	FetchPendingPayment(promoId int, user domain.UserSession) ([]map[string]interface{}, error)
	FetchPromotionBuyPayment(filter models.PromotionBuyPayment) ([]map[string]interface{}, error)

	UpdatePromotionBuyStatus(promotionBuyId int, status string) error
	UpdatePromotionBuyPaymentStatus(promotionPaymentId int64, status string) error

	// FetchPromotionBuy(promoBuyId int, user domain.UserSession) (models.PromotionBuyEntity, error)
	FetchPromotionBuy(filter models.PromotionBuyFilter) ([]models.PromotionBuyEntity, error)
	RemovePromotionBuy(promotionBuyId int) error

	BuyPromotion(input models.PromotionBuy) (int64, error)
	AddPromotionBuyPayment(input models.PromotionBuyPayment) (int64, error)

	RefundPromotion(promoBuyId int, user domain.UserSession) error

	AddNotifyPromotion(promoId int, user domain.UserSession) (int64, error)
	FetchNotifyPromotion(id ...int) ([]map[string]interface{}, error)
	FetchNotifyPromotionById(id int64) (map[string]interface{}, error)
	FetchNotifyPromotionFilter(filter models.NotifyPromotionFilter) ([]map[string]interface{}, error)
	FetchNotificationToken(memberDetails ...models.MemberDetail) ([]map[string]interface{}, error)
	RemoveNotifyPromotion(notifyIds ...interface{}) error

	FetchPromotion(filter models.PromotionDetailFilter) ([]map[string]interface{}, error)

	GetDynamicLinkCache(promoBuyId string) (string, error)
	SetDynamicLinkCache(promoBuyId string, link string) error
}

type BillingService interface {
	CreatePayment(models.PromotionPaymentCreate) (models.PromotionPaymentResponse, error)
}
