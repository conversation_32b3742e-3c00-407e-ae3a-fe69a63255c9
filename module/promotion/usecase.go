package promotion

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type UseCase interface {
	FetchPromotion(filter models.PromotionFilter, user domain.UserSession) ([]map[string]interface{}, error)
	FetchMyDealsSecretCode(promotionBuyId int, user domain.UserSession) (map[string]interface{}, error)
	FetchMyVoucher(param domain.MyVoucherRequest, user domain.UserSession) ([]map[string]interface{}, error)

	BuyPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error)
	ReceivePaymentUpdate(promotionBuyId int, transactionId, status string) error
	FetchPromotionBuyPayment(promoBuyId int, paymentType, phoneNumber string, user domain.UserSession) (map[string]interface{}, error)

	RefundPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error)

	RunPromotionRole(param ...domain.PromotionRoleParam)
	RunPromotionReminder(dayLeft int)
	AddNotifyPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error)
	SendNotifyPromotion()
}
