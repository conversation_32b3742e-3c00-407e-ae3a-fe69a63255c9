package usecase

import (
	"reflect"
	"testing"

	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion/mocks"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

func Test_promotionUseCase_BuyPromotion(t *testing.T) {
	repo := mocks.NewPromotionRepository(t)
	// repoCmpaign :=
	// repoCampaign
	servicebilling := mocks.NewBillingService(t)

	type fields struct {
		repo           promotion.PromotionRepository
		repoCampaign   campaign.CampaignRepository
		repoMembership membership.MembershipRepository
		helperUseCase  domain.HelperUseCase
		billingService promotion.BillingService
	}

	field := fields{
		repo:           repo,
		billingService: servicebilling,
	}

	type args struct {
		promoId int
		user    domain.UserSession
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{"money_service_billing_err", field, args{promoId: 1, user: domain.UserSession{}}, map[string]interface{}{}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := promotionUseCase{
				repo:           tt.fields.repo,
				repoCampaign:   tt.fields.repoCampaign,
				repoMembership: tt.fields.repoMembership,
				helperUseCase:  tt.fields.helperUseCase,
				billingService: tt.fields.billingService,
			}
			got, err := p.BuyPromotion(tt.args.promoId, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("promotionUseCase.BuyPromotion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("promotionUseCase.BuyPromotion() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_promotionUseCase_FetchPromotionBuyPayment(t *testing.T) {
	repo := mocks.NewPromotionRepository(t)
	// repoCampaign
	servicebilling := mocks.NewBillingService(t)

	type fields struct {
		repo           promotion.PromotionRepository
		repoCampaign   campaign.CampaignRepository
		repoMembership membership.MembershipRepository
		repoUser       user.Repository
		helperUseCase  domain.HelperUseCase
		billingService promotion.BillingService
	}
	type args struct {
		promoBuyId  int
		paymentType string
		user        domain.UserSession
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{"test1", fields{repo: repo, billingService: servicebilling}, args{1, "qris", domain.UserSession{}}, map[string]interface{}{}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := promotionUseCase{
				repo:           tt.fields.repo,
				repoCampaign:   tt.fields.repoCampaign,
				repoMembership: tt.fields.repoMembership,
				repoUser:       tt.fields.repoUser,
				helperUseCase:  tt.fields.helperUseCase,
				billingService: tt.fields.billingService,
			}
			got, err := p.FetchPromotionBuyPayment(tt.args.promoBuyId, tt.args.paymentType, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("promotionUseCase.FetchPromotionBuyPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("promotionUseCase.FetchPromotionBuyPayment() = %v, want %v", got, tt.want)
			}
		})
	}
}
