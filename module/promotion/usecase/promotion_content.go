package usecase

import (
	"math/rand"
	"strconv"
	"strings"
)

func GetRandomPromoReminder(dayLeft int, voucherList string, appName string) string {
	messages := []string{
		//#1
		`Psst... Ada voucher nganggur nih di akun kamu! ✨
Sayang banget kalau sampai hangus - tinggal $DAYS hari lagi sebelum expired lho. Jangan sampai nyesel ya!
Langsung aja cek dan pakai voucher kamu:

$VOUCHER_LIST

Makin hemat kalau pakai voucher sekarang! 🎉"`,
		//#2
		`Hai hai! 👋
Cek dulu deh voucher kamu...
Tinggal $DAYS hari lagi sebelum voucher kamu melayang! 🌪️
Yuk buruan redeem sekarang:

$VOUCHER_LIST

Jangan ditunda-tunda, nanti keburu hilang! 😉`,

		//#3
		`REMINDER! ⏰
Voucher spesial kamu masih utuh nih...
Tinggal $DAYS hari lagi sebelum expired!
Langsung gas redeem aja:

$VOUCHER_LIST

Lebih cepat, lebih hemat! 🏃‍♂️`,

		//#4
		`Halo, ada kabar penting! 📢
Voucher di akun kamu udah mau expired dalam $DAYS hari kedepan...
Masa sih mau dibiarin aja?

Klik link ini untuk pakai voucher:
$VOUCHER_LIST

Buruan dipakai sebelum hangus! 🔥`,

		//#5
		`Cek notif! ✨
Masih ada voucher yang belum kepakai di akun kamu.
$DAYS hari lagi bakal expired... Sayang kan kalau sampai hangus?

Yuk langsung redeem:
$VOUCHER_LIST

Jangan sampai kelewatan kesempatan hemat ini! 💫`,

		//#6
		`PING! 🔔
Ini reminder buat kamu yang masih punya voucher aktif!
Tinggal $DAYS hari sebelum voucher kamu expired...

Segera gunakan di sini:
$VOUCHER_LIST

Pakai sekarang, hemat banyak! 💰`,
	}

	message := messages[rand.Intn(len(messages))] + "\n\n\n" +
		"_silahkan buka aplikasi $APP jika link tidak dapat digunakan_"
	message = strings.ReplaceAll(message, "$DAYS", strconv.Itoa(dayLeft))
	message = strings.ReplaceAll(message, "$VOUCHER_LIST", voucherList)
	message = strings.ReplaceAll(message, "$APP", appName)
	return message
}

func GetRandomPromoReminderTitle() string {
	messages := []string{
		"⚡ Voucher Kamu Akan Segera Expired ",
		"🚨 REMINDER: Voucher Segera Berakhir!",
		"Psst... Voucher Kamu Belum Dipakai!",
		"Jangan Sampai Voucher Hangus!",
		"Buruan! Voucher Kamu Mau Expired",
		"🎁 PENTING: Voucher Akan Hangus Segera",
		"Hei! Masih Ada Voucher Yang Belum Dipakai",
		"💫 Jangan Lewatkan! Voucher Segera Berakhir",
		"ALERT: Voucher Kamu Akan Expired!",
		"Ayo Cek! Voucher Kamu Mau Expired",
		"⚠️ Selamatkan Voucher Kamu Sebelum Expired!",
		"Ada Voucher Yang Belum Terpakai Nih!",
		"Cek Sekarang! Voucher Hampir Expired",
	}
	return messages[rand.Intn(len(messages))]
}

func GetRandomGiftPromoTitle() string {
	messages := []string{
		"✨ Ada Hadiah Spesial Buat Kamu!",
		"🎁 Special Reward Menanti!",
		"Surprise! Ada Kejutan Nih...",
		"🌟 Ada Yang Spesial di Akunmu!",
		"Cek Sekarang! Ada Hadiah Untukmu ✨",
		"🎊 Kamu Dapat Special Treat!",
		"Hey! Ada Surprise Buat Kamu",
		"💝 Special Gift Just For You!",
		"Ada Sesuatu Yang Seru Nih!",
		"👋 Hey! Cek Hadiah Kamu...",
		"🎯 Reward Spesial Menanti!",
		"Special Surprise in Your Account! ✨",
		"Ada Yang Baru di Akunmu! 🎁",
		"Psst... Ada Hadiah Spesial!",
		"🌈 Your Special Reward is Here!",
	}
	return messages[rand.Intn(len(messages))]
}
