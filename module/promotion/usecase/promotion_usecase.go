package usecase

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/istime"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion/repository/service"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

type promotionUseCase struct {
	repo           promotion.PromotionRepository
	repoCampaign   campaign.CampaignRepository
	repoMembership membership.MembershipRepository
	repoUser       user.Repository
	repoApp        domain.AppRepository
	helperUseCase  domain.HelperUseCase
	billingService promotion.BillingService
}

func NewPromotionUseCase(repository promotion.PromotionRepository, repoCampaign campaign.CampaignRepository,
	helperUseCase domain.HelperUseCase, membershipRepo membership.MembershipRepository, userRepo user.Repository,
	repoApp domain.AppRepository) promotion.UseCase {
	billingService := service.NewBillingService()
	return &promotionUseCase{repo: repository,
		repoCampaign:   repoCampaign,
		repoMembership: membershipRepo,
		helperUseCase:  helperUseCase,
		billingService: billingService,
		repoUser:       userRepo,
		repoApp:        repoApp,
	}
}

func (p *promotionUseCase) FetchPromotion(filter models.PromotionFilter, user domain.UserSession) ([]map[string]interface{}, error) {
	//adjust filter
	filter.AdminId = int(user.AdminId)
	filter.MemberId = int(user.MemberId)
	timeZoneOffset := 27200
	// filter.PromotionTypeIds = p.convertPromotionTypeToId(filter.Types)

	logTime := log.TimeInit()
	defer logTime.Print()

	if user.MemberId > 0 {
		memberInfo, err := p.repoUser.FetchMemberType(user)
		if !log.IfError(err) {
			filter.MemberTypeId = cast.ToInt(memberInfo["type_fkid"])
		}
		logTime.AddLog("fetch member detail")
	}

	promotionTypes, err := p.repo.FetchPromotionType()
	log.IfError(err)
	logTime.AddLog("fetch promotion type")
	promotionTypeMap := make(map[int]models.PromotionType)
	promoSubTypes := []string{"discount", "free", "specialprice"}
	filter.PromotionTypeIds = make([]int, 0)
	for _, typename := range filter.Types {
		typeNames := make([]string, 0)
		if strings.HasSuffix(typename, "_all") {
			for _, subType := range promoSubTypes {
				typeNames = append(typeNames, fmt.Sprintf("%v_%v", strings.TrimRight(typename, "_all"), subType))
			}
		} else {
			typeNames = append(typeNames, typename)
		}

		for _, name := range typeNames {
			for _, promoType := range promotionTypes {
				promotionTypeMap[promoType.PromotionTypeID] = promoType
				if strings.EqualFold(name, promoType.PromotionType) {
					filter.PromotionTypeIds = append(filter.PromotionTypeIds, promoType.PromotionTypeID)
					break
				}
			}
		}
	}

	log.Info("fetch promo with filter: %v", utils.SimplyToJson(filter))
	promotions, err := p.repo.FetchPromotionWithDetail(filter)
	logTime.AddLog("fetch promotion")
	if err != nil {
		return nil, err
	}

	if len(promotions) == 0 {
		if filter.PromotionId > 0 {
			return nil, exception.WithCode{Code: 70, Message: "promotion not found or expired"}
		}
		return nil, nil
	}

	//fetch outlet available
	promotionIds := make([]interface{}, 0)
	for _, promo := range promotions {
		promotionIds = append(promotionIds, promo["promotion_id"])
	}

	var promoOutlets []models.PromotionOutlet
	var promoProducts []models.PromotionProduct
	var promoMember []map[string]interface{}

	//get detail promotion, only if request for specific id
	if filter.PromotionId > 0 {
		promoOutlets, err = p.repo.FetchPromotionOutlet(promotionIds...)
		if err != nil {
			return nil, err
		}
		logTime.AddLog("fetch promotion outlet")

		//fetch promo product
		promoProducts, err = p.repo.FetchPromotionProduct(promotionIds...)
		if err != nil {
			return nil, err
		}
		logTime.AddLog("fetch promotion product")

		promoMember, err = p.repo.FetchPromotionMember(promotionIds...)
		if err != nil {
			return nil, err
		}
		logTime.AddLog("fetch promotion member")
	}

	promoMemberMap := array.GroupBy(promoMember, "promotion_id")

	fmt.Println("promoProducts : ", len(promoProducts))
	validPromotion := make([]map[string]interface{}, 0)
	for _, promo := range promotions {
		//check if promo still active
		if !istime.After(cast.ToInt64(promo["start_promotion_date"]), cast.ToString(promo["end_promotion_time"]), timeZoneOffset) {
			// continue
			log.Info("istimeAfter: %v | %v ", promo["start_promotion_date"], promo["end_promotion_time"])
		}
		//format output
		daysActive := make([]string, 0)
		cols := []string{"sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"}
		for _, col := range cols {
			if fmt.Sprintf("%v", promo[col]) == "1" {
				daysActive = append(daysActive, strings.Replace(cast.ToString(col), "day_", "", 1))
			}
		}

		timeActive := array.TakeOnly(promo, "start_promotion_date", "end_promotion_date", "start_promotion_time", "end_promotion_time")
		timeActive["day_active"] = daysActive
		promo["time_active"] = timeActive
		promo["promotion_type_name"] = promotionTypeMap[cast.ToInt(promo["promotion_type_id"])].PromotionType

		outletDetail := make([]models.PromotionOutlet, 0)
		products := make([]models.PromotionProduct, 0)
		productsTerm := make([]models.PromotionProduct, 0)

		for _, outlet := range promoOutlets {
			if cast.ToInt(promo["promotion_id"]) == outlet.PromotionID {
				outletDetail = append(outletDetail, outlet)
			}
		}

		promo["outlet"] = outletDetail

		if len(promoProducts) > 0 {
			for _, product := range promoProducts {
				if cast.ToInt(promo["promotion_id"]) == product.PromotionID {
					// utils.RemoveField(product, "promotion_id")
					if product.Type == "order_menu" {
						productsTerm = append(productsTerm, product)
					} else {
						products = append(products, product)
					}
				}
			}

			promo["promotion_product"] = products
			if len(productsTerm) > 0 {
				promo["term_product"] = map[string]interface{}{
					"qty":      promoProducts[0].Qty,
					"products": productsTerm,
				}
			}
		}

		///member type
		memberTypes := promoMemberMap[cast.ToString(promo["promotion_id"])]
		if len(memberTypes) > 0 {
			promo["member_types"] = memberTypes
		}

		utils.RemoveField(promo, cols...)
		utils.RemoveField(promo, "start_promotion_date", "end_promotion_date", "start_promotion_time", "end_promotion_time")
		validPromotion = append(validPromotion, promo)
	}

	return validPromotion, nil
}

func (p *promotionUseCase) convertPromotionTypeToId(types []string) []int {
	promotionTypes, err := p.repo.FetchPromotionType()
	if err != nil {
		return []int{}
	}

	result := make([]int, 0)
	for _, typename := range types {
		for _, promoType := range promotionTypes {
			if strings.EqualFold(typename, promoType.PromotionType) {
				result = append(result, promoType.PromotionTypeID)
				break
			}
		}
	}

	return result
}

func (p *promotionUseCase) generateSecretCode(memberId int64) string {
	// give padding, if needed
	secretCode := utils.AddPadding(cast.ToString(memberId), 3)
	// change number
	secretCode = utils.ChangeNumber(secretCode)

	// adding milisecond
	nano := time.Now().Nanosecond()
	millisec := nano / 1000000
	if millisec > 1000 {
		millisec -= 1
	} else if millisec < 100 {
		millisec += 100
	}
	secretCode += utils.ToString(millisec)

	if len(secretCode) > 6 {
		secretCode = secretCode[:6]
	}

	return secretCode
}

func (p *promotionUseCase) FetchMyDealsSecretCode(promotionBuyId int, user domain.UserSession) (map[string]interface{}, error) {
	// Generate 5 secret codes
	secretCodes := make([]string, 5)
	for i := 0; i < 5; i++ {
		secretCodes[i] = p.generateSecretCode(user.MemberId)
	}

	// Check which codes already exist
	existingCodes, err := p.repo.CheckExistingSecretCodes(secretCodes)
	if log.IfError(err) {
		log.Info("error checking existing codes: %v", err)
		return nil, err
	}

	// Find first non-existing code
	var secretCode string
	for _, code := range secretCodes {
		exists := false
		for _, existingCode := range existingCodes {
			if code == existingCode {
				exists = true
				break
			}
		}
		if !exists {
			secretCode = code
			break
		}
	}

	// If all codes exist (very unlikely), generate one more
	if secretCode == "" {
		log.Info("all codes exist, generating one more, memberId: %v, code: %v", user.MemberId, secretCodes)
		secretCode = p.generateSecretCode(user.MemberId)
	}

	expiredDuration := "minute"
	expired := 5 //in minutes
	timeMillis := time.Now().UnixNano() / 1000000
	timeExpired := time.Now().Add(time.Minute*time.Duration(expired)).UnixNano() / 1000000

	log.Info("generating secret code for %v, promoBuyId %v, code: %v", user.MemberId, promotionBuyId, secretCode)
	err = p.repo.GenerateSecretCode(secretCode, timeExpired, promotionBuyId, user.MemberId)
	if log.IfError(err) {
		log.Info("generating secret code for %v, promoBuyId %v error: %v", user.MemberId, promotionBuyId, err)
		return nil, err
	}

	result := map[string]interface{}{
		"secret_code":            secretCode,
		"expired":                timeExpired,
		"expired_value":          expired,
		"expired_value_duration": expiredDuration,
		"time_millis":            timeMillis,
	}
	return result, nil
}

func (p *promotionUseCase) AddNotifyPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error) {
	//TODO: validate promotion (it should be unpublised, it should be valid id)

	id, err := p.repo.AddNotifyPromotion(promoId, user)
	if err != nil {
		//if error, check if its already exist
		data, _ := p.repo.FetchNotifyPromotionFilter(models.NotifyPromotionFilter{
			PromotionId: cast.ToInt64(promoId),
			MemberId:    user.MemberId,
			AdminId:     user.AdminId,
		})
		if len(data) > 0 {
			return nil, exception.WithCode{Code: 409, Message: "already created"}
		}

		return nil, err
	}

	//add to scheduled message
	go p.addToQueueMessage(id)

	return map[string]interface{}{"id": id}, nil
}

func (p *promotionUseCase) addToQueueMessage(id int64) {
	notify, err := p.repo.FetchNotifyPromotionById(id)
	if log.IfError(err) {
		return
	}

	notifTokens, err := p.repo.FetchNotificationToken(models.MemberDetail{
		AdminId:  cast.ToInt(notify["admin_fkid"]),
		MemberId: cast.ToInt(notify["member_fkid"]),
	})
	if log.IfError(err) {
		return
	}

	memberTokenMap := make(map[string][]string)
	for _, token := range notifTokens {
		key := fmt.Sprintf("%v_%v", token["member_fkid"], token["admin_fkid"])
		if _, ok := memberTokenMap[key]; !ok {
			memberTokenMap[key] = make([]string, 0)
		}

		memberTokenMap[key] = append(memberTokenMap[key], cast.ToString(token["token"]))
	}

	//build a message
	messageDetail := fmt.Sprintf("Promo <b>%s</b> sudah bisa kamu klaim sekarang, jangan sampai kelewatan!", notify["name"])

	//getting link to go to detail
	promoLink, err := p.helperUseCase.FetchShareLink("deal", cast.ToString(notify["promotion_id"]), domain.UserSession{AdminId: cast.ToInt64(notify["admin_fkid"])})
	if err == nil && len(promoLink) > 0 {
		messageDetail += fmt.Sprintf("</br></br><b><u><a href='%s'>KLAIM DISINI</a></u></b>", promoLink["shortLink"])
	}

	scheduledMessage := make([]domain.ScheduleMessage, 0)
	key := fmt.Sprintf("%v_%v", notify["member_fkid"], notify["admin_fkid"])
	for _, token := range memberTokenMap[key] {
		scheduledMessage = append(scheduledMessage, domain.ScheduleMessage{
			Title:        "Deals Alert!",
			Message:      messageDetail,
			TimeDeliver:  cast.ToInt64(notify["publish_date"]),
			DataCreated:  time.Now().Unix() * 1000,
			Media:        "push_notif",
			Receiver:     token,
			Attachments:  utils.SimplyToJson([]interface{}{notify["photo"]}),
			IdentifierID: notify["crm_notify_product_id"],
			MessageDetail: utils.SimplyToJson(map[string]interface{}{
				"notification_type": "promotion",
				"notification_data": map[string]interface{}{
					"id": notify["promotion_id"],
				},
			}),
		})
	}

	log.Info("total message: %v", len(scheduledMessage))
	p.repoCampaign.AddScheduleMessage(scheduledMessage...)
}

func (p *promotionUseCase) BuyPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error) {
	timeOffset := 25200
	log.Info("member %v buy promo: %v", user.MemberId, promoId)

	//fetch member info
	members, err := p.repoUser.FetchMember(user)
	if err != nil {
		return nil, err
	}

	if len(members) == 0 {
		return nil, exception.WithCode{Code: 50, Message: "member not found"}
	}

	member := members[0]

	//check if member status is active
	if utils.ToString(member["status"]) != "1" {
		log.Info("buy deals failed, FetchMember status is no active")
		return nil, exception.WithCode{Code: 51, Message: "member is not active"}
	}

	// fetch promotion info detail
	filter := models.PromotionFilter{
		AdminId:         int(user.AdminId),
		MemberId:        int(user.MemberId),
		MemberTypeId:    cast.ToInt(member["type_fkid"]),
		PromotionId:     promoId,
		PromotionTypeId: utils.PROMO_DEALS,
		TimeOffset:      timeOffset,
	}
	promotions, err := p.repo.FetchPromotionWithDetail(filter)
	if err != nil {
		return nil, err
	}

	//check if deal is even exist
	if len(promotions) == 0 {
		log.Info("buy deals failed, Deals not found: %v --> %v", promoId, utils.SimplyToJson(filter))
		return nil, exception.WithCode{Code: 70, Message: "deals not found or expired"}
	}

	promotion := promotions[0]
	if utils.ToInt(promotion["deals_can_buy"]) == 0 {
		log.Info("deals can not be bought")
		return nil, exception.WithCode{Code: 70, Message: "deals can not be bought"}
	}

	//if publish date set, check if it matchs the condition
	if publishDate := cast.ToInt64(promotion["publish_date"]); publishDate > 0 {
		if publishDate < time.Now().Unix() {
			log.Info("deals %v not published, publish date: %v", promoId, publishDate)
			return nil, fmt.Errorf("deals belum dapat dibeli saat ini")
		}
	}

	//if user has alrady purchase the deal, and status is pending, return the payment info instead
	if promotion["voucher_price_type"] == models.PromotionPriceTypeMoney {
		pendingPayments, err := p.repo.FetchPendingPayment(promoId, user)
		log.IfError(err)
		if len(pendingPayments) > 0 && cast.ToInt64(pendingPayments[0]["expired_at"]) > time.Now().Unix()*1000 {
			var promoInfo models.PromotionPaymentResponse
			err = json.Unmarshal([]byte(cast.ToString(pendingPayments[0]["payment_info"])), &promoInfo)
			if !log.IfError(err) {
				log.Info("promoId: %v, is pending, pending %v ", promoId, pendingPayments)
				return map[string]interface{}{
					"promotion_buy_id": pendingPayments[0]["promotion_buy_fkid"],
					"payment": map[string]interface{}{
						"payment_type": pendingPayments[0]["payment_type"],
						"expired_at":   pendingPayments[0]["expired_at"],
						"url":          promoInfo.PaymentDetail()["url"],
					},
				}, nil
			}
		}
	}

	//check total redeem
	if utils.ToInt(promotion["total_reedem"]) >= utils.ToInt(promotion["maximum_redeem"]) {
		log.Info("buy deals failed, Total redeem exceed limit")
		return nil, exception.WithCode{Code: 72, Message: "total redeem exceed the limit"}
	}

	if utils.ToInt(promotion["total_personal_reedem"]) >= utils.ToInt(promotion["member_maximum_redeem"]) {
		log.Info("buy deals failed, member '%s' have bought %d times, while max is %d", user.MemberId, promotion["total_personal_reedem"], promotion["member_maximum_redeem"])
		return nil, exception.WithCode{Code: 75, Message: "total redeem per member exceed limit"}
	}

	status := "available"
	dealsPrice := 0

	// var paymentNotif models.PaymentNotification
	// paymentData := make(map[string]interface{})
	//check nominal type
	if promotion["voucher_price_type"] == models.PromotionPriceTypePoint {
		//check point amount owned by member
		if utils.ToInt(member["total_point"]) < utils.ToInt(promotion["deals_value"]) {
			log.Info("%v buy deals failed, point is not enough (%v) price: %v", user.MemberId, member["total_point"], promotion["deals_value"])
			return nil, exception.WithCode{Code: 71, Message: "point is not enough"}
		}
	} else if promotion["voucher_price_type"] == models.PromotionPriceTypeMoney {
		status = "pending"
	}

	if promotion["voucher_price_type"] != "free" {
		dealsPrice = utils.ToInt(promotion["deals_value"])
	}

	log.Info("promotion : %s", utils.SimplyToJson(promotion))
	promotionBuyId, err := p.repo.BuyPromotion(models.PromotionBuy{
		MemberFkid:        int(user.MemberId),
		PromotionFkid:     cast.ToInt(promotion["promotion_id"]),
		PromotionTypeFkid: cast.ToInt(promotion["promotion_type_id"]),
		Price:             dealsPrice,
		PriceType:         cast.ToString(promotion["voucher_price_type"]),
		PromotionNominal:  cast.ToInt(promotion["promo_nominal"]),
		Status:            status,
	})

	if log.IfError(err) {
		return nil, err
	}

	log.Info("member %v buy promo: %v, buyId: %v", user.MemberId, promoId, promotionBuyId)
	responseData := map[string]interface{}{
		"promotion_buy_id": promotionBuyId,
	}

	if promotion["voucher_price_type"] == models.PromotionPriceTypePoint {
		log.Info("update point of %v to: %v | currentPoint: %v - price: %v", user.MemberId, utils.ToInt(member["total_point"])-dealsPrice, member["total_point"], dealsPrice)
		log.IfError(p.repoMembership.UpdatePointIncrement(dealsPrice*-1, user))
	} else if promotion["voucher_price_type"] == models.PromotionPriceTypeMoney {
		// paymentResult, err := p.billingService.CreatePayment(models.PromotionPaymentCreate{
		// 	AdminId:        int(user.AdminId),
		// 	PromotionBuyId: promotionBuyId,
		// 	Price:          dealsPrice,
		// 	PaymentType:    "qris",
		// 	Customer: models.Customer{
		// 		Name:  cast.ToString(member["name"]),
		// 		Phone: cast.ToString(member["phone"]),
		// 		Email: cast.ToString(member["email"]),
		// 	},
		// })

		// if log.IfError(err) {
		// 	//remove promo buy if failed to crate payment
		// 	log.Info("failed to create payment, delete promotion...")
		// 	log.IfError(p.repo.RemovePromotionBuy(int(promotionBuyId)))

		// 	return nil, fmt.Errorf("failed to create payment")
		// }

		// promotionBuyEntity := models.PromotionBuyPayment{
		// 	PromotionBuyId: promotionBuyId,
		// 	PaymentInfo:    utils.SimplyToJson(paymentResult),
		// 	Status:         "pending",
		// 	PaymentType:    "qris",
		// 	TransactionId:  paymentResult.TransactionID,
		// 	ExpiredAt:      time.Now().Add(5*time.Minute).Unix() * 1000,
		// }
		// p.repo.AddPromotionBuyPayment(promotionBuyEntity)

		// responseData["payment"] = map[string]interface{}{
		// 	"payment_type": promotionBuyEntity.PaymentType,
		// 	"expired_at":   promotionBuyEntity.ExpiredAt,
		// 	"qris":         paymentResult.Qris,
		// }
	}

	return responseData, nil
}

func (p *promotionUseCase) FetchPromotionBuyPayment(promoBuyId int, paymentType, phoneNumber string, user domain.UserSession) (map[string]interface{}, error) {
	if promoBuyId == 0 {
		return nil, exception.WithCode{Code: 400, Message: "id required"}
	}
	if paymentType == "" {
		return nil, exception.WithCode{Code: 400, Message: "payment_type required"}
	}

	filter := models.PromotionBuyFilter{
		PromotionBuyId: promoBuyId,
		MemberId:       user.MemberId,
		AdminId:        user.AdminId,
	}

	promoBuys, err := p.repo.FetchPromotionBuy(filter)
	if log.IfError(err) {
		return nil, err
	}

	if len(promoBuys) == 0 {
		return nil, fmt.Errorf("promotion_buy_id not found: %v", promoBuyId)
	}
	promotionBuy := promoBuys[0]

	if promotionBuy.Status != "pending" {
		return nil, fmt.Errorf("can not set payment, status: %v", promotionBuy.Status)
	}

	if paymentType == "ovo" && strings.HasPrefix(phoneNumber, "0") {
		return nil, fmt.Errorf("invalid phone_number")
	}

	//fetch member
	members, err := p.repoUser.FetchMember(user)
	if log.IfError(err) {
		return nil, err
	}
	if len(members) == 0 {
		return nil, fmt.Errorf("member not found")
	}

	//fetch app config
	appCrm, err := p.repoApp.FetchAppInfoAndConfig(user)
	log.IfError(err)

	callbackUrl := "https://uniq.id/"
	if webUrl := appCrm.ToAppInfo().Web.Url; webUrl != "" {
		callbackUrl = fmt.Sprintf("%s/voucher/%v", webUrl, promoBuyId)
	}

	member := members[0]
	if paymentType == "ovo" && phoneNumber != "" {
		member["phone"] = phoneNumber
	}

	paymentResult, err := p.billingService.CreatePayment(models.PromotionPaymentCreate{
		AdminId:     int(user.AdminId),
		Id:          cast.ToString(promoBuyId),
		Price:       promotionBuy.Price,
		PaymentType: paymentType,
		CallbackUrl: callbackUrl,
		Customer: models.Customer{
			Name:  cast.ToString(member["name"]),
			Phone: cast.ToString(member["phone"]),
			Email: cast.ToString(member["email"]),
		},
	})

	if log.IfError(err) {
		return nil, err
	}

	promotionBuyEntity := models.PromotionBuyPayment{
		PromotionBuyId: int64(promoBuyId),
		PaymentInfo:    utils.SimplyToJson(paymentResult),
		Status:         "pending",
		PaymentType:    paymentType,
		TransactionId:  paymentResult.TransactionID,
		ExpiredAt:      time.Now().Add(5*time.Minute).Unix() * 1000,
	}
	_, err = p.repo.AddPromotionBuyPayment(promotionBuyEntity)
	log.IfError(err)

	log.Info("payment, qris: %v | ewallet: %v", paymentResult.Qris, paymentResult.Ewallet)

	responseData := map[string]interface{}{
		"payment_type": promotionBuyEntity.PaymentType,
		"expired_at":   promotionBuyEntity.ExpiredAt,
		"payment":      paymentResult.PaymentDetail(),
	}

	return responseData, nil
}

func (p *promotionUseCase) RefundPromotion(promoBuyId int, user domain.UserSession) (map[string]interface{}, error) {
	log.Info("%v, refund promoBuyId: %v", user.MemberId, promoBuyId)

	filter := models.PromotionBuyFilter{
		PromotionBuyId: promoBuyId,
		MemberId:       user.MemberId,
		AdminId:        user.AdminId,
	}

	promoBuys, err := p.repo.FetchPromotionBuy(filter)
	if log.IfError(err) {
		return nil, err
	}

	if len(promoBuys) == 0 {
		return nil, exception.WithCode{Code: 404, Message: fmt.Sprintf("promotion_buy_id %v not found", promoBuyId)}
	}
	promoBuy := promoBuys[0]

	// check if status available
	if promoBuy.Status != models.PromotionBuyStatusAvailable {
		return nil, fmt.Errorf("status is %v", promoBuy.Status)
	}

	//promo bought with money, currently can not be refunded
	if promoBuy.PriceType == models.PromotionPriceTypeMoney {
		return nil, fmt.Errorf("promo bought with money can no be refund")
	}

	// do refund
	err = p.repo.RefundPromotion(promoBuyId, user)
	if log.IfError(err) {
		return nil, err
	}

	//if use point, add back the point
	if promoBuy.PriceType == models.PromotionPriceTypePoint {
		log.Info("update point of %v substracted by %v (refund promo)", user.MemberId, promoBuy.Price)
		err = p.repoMembership.UpdatePointIncrement(promoBuy.Price, user)
		if log.IfError(err) {
			return nil, err
		}
	}

	//notify member
	go p.notifyPromoRefund(promoBuy, user)
	return nil, nil
}

func (p *promotionUseCase) notifyPromoRefund(promoBuy models.PromotionBuyEntity, user domain.UserSession) {
	promos, err := p.repo.FetchPromotionWithDetail(models.PromotionFilter{PromotionId: promoBuy.PromotionFkid, AdminId: int(user.AdminId)})
	if log.IfError(err) {
		return
	}
	if len(promos) == 0 {
		log.IfError(fmt.Errorf("failed send notif, promo not found: %v", promoBuy.PromotionFkid))
		return
	}

	notifTokens, err := p.repo.FetchNotificationToken(models.MemberDetail{
		AdminId:  int(user.AdminId),
		MemberId: int(user.MemberId),
	})

	if log.IfError(err) {
		return
	}

	firebaseToken := make([]string, 0)
	for _, token := range notifTokens {
		if cast.ToInt(token["admin_fkid"]) == int(user.AdminId) {
			firebaseToken = append(firebaseToken, cast.ToString(token["token"]))
		}
	}
	fmt.Println("total token: ", len(firebaseToken))
	if len(firebaseToken) == 0 {
		return
	}

	message := fmt.Sprintf("Deals %v telah berhasil di refund!", promos[0]["name"])
	//if use point, add back the point
	if promoBuy.PriceType == models.PromotionPriceTypePoint && promoBuy.Price > 0 {
		message += fmt.Sprintf(" %v point kamu telah dikembalikan", promoBuy.Price)
	}

	scheduledMessage := make([]domain.ScheduleMessage, 0)
	for _, token := range firebaseToken {
		scheduledMessage = append(scheduledMessage, domain.ScheduleMessage{
			Title:       "Refund Promo",
			Message:     message,
			TimeDeliver: time.Now().Unix() * 1000,
			DataCreated: time.Now().Unix() * 1000,
			Media:       "push_notif",
			Receiver:    token,
		})
	}

	log.Info("total message: %v", len(scheduledMessage))
	p.repoCampaign.AddScheduleMessage(scheduledMessage...)
}

// FetchMyVoucher implements promotion.UseCase.
func (p *promotionUseCase) FetchMyVoucher(param domain.MyVoucherRequest, user domain.UserSession) ([]map[string]interface{}, error) {
	return nil, nil
}
