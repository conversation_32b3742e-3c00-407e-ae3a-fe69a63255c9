// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"

	models "gitlab.com/uniqdev/backend/api-membership/models"
)

// PromotionRepository is an autogenerated mock type for the PromotionRepository type
type PromotionRepository struct {
	mock.Mock
}

// AddNotifyPromotion provides a mock function with given fields: promoId, user
func (_m *PromotionRepository) AddNotifyPromotion(promoId int, user domain.UserSession) (int64, error) {
	ret := _m.Called(promoId, user)

	var r0 int64
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) int64); ok {
		r0 = rf(promoId, user)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(promoId, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddPromotionBuyPayment provides a mock function with given fields: input
func (_m *PromotionRepository) AddPromotionBuyPayment(input models.PromotionBuyPayment) (int64, error) {
	ret := _m.Called(input)

	var r0 int64
	if rf, ok := ret.Get(0).(func(models.PromotionBuyPayment) int64); ok {
		r0 = rf(input)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.PromotionBuyPayment) error); ok {
		r1 = rf(input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BuyPromotion provides a mock function with given fields: input
func (_m *PromotionRepository) BuyPromotion(input models.PromotionBuy) (int64, error) {
	ret := _m.Called(input)

	var r0 int64
	if rf, ok := ret.Get(0).(func(models.PromotionBuy) int64); ok {
		r0 = rf(input)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.PromotionBuy) error); ok {
		r1 = rf(input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchNotificationToken provides a mock function with given fields: memberDetails
func (_m *PromotionRepository) FetchNotificationToken(memberDetails ...models.MemberDetail) ([]map[string]interface{}, error) {
	_va := make([]interface{}, len(memberDetails))
	for _i := range memberDetails {
		_va[_i] = memberDetails[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(...models.MemberDetail) []map[string]interface{}); ok {
		r0 = rf(memberDetails...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...models.MemberDetail) error); ok {
		r1 = rf(memberDetails...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchNotifyPromotion provides a mock function with given fields: id
func (_m *PromotionRepository) FetchNotifyPromotion(id ...int) ([]map[string]interface{}, error) {
	_va := make([]interface{}, len(id))
	for _i := range id {
		_va[_i] = id[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(...int) []map[string]interface{}); ok {
		r0 = rf(id...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(id...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchNotifyPromotionById provides a mock function with given fields: id
func (_m *PromotionRepository) FetchNotifyPromotionById(id int64) (map[string]interface{}, error) {
	ret := _m.Called(id)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(int64) map[string]interface{}); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int64) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchNotifyPromotionFilter provides a mock function with given fields: filter
func (_m *PromotionRepository) FetchNotifyPromotionFilter(filter models.NotifyPromotionFilter) ([]map[string]interface{}, error) {
	ret := _m.Called(filter)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(models.NotifyPromotionFilter) []map[string]interface{}); ok {
		r0 = rf(filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.NotifyPromotionFilter) error); ok {
		r1 = rf(filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPendingPayment provides a mock function with given fields: promoId, user
func (_m *PromotionRepository) FetchPendingPayment(promoId int, user domain.UserSession) ([]map[string]interface{}, error) {
	ret := _m.Called(promoId, user)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) []map[string]interface{}); ok {
		r0 = rf(promoId, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(promoId, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotion provides a mock function with given fields: param
func (_m *PromotionRepository) FetchPromotion(param models.PromotionFilter) ([]map[string]interface{}, error) {
	ret := _m.Called(param)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(models.PromotionFilter) []map[string]interface{}); ok {
		r0 = rf(param)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.PromotionFilter) error); ok {
		r1 = rf(param)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionBuy provides a mock function with given fields: promoBuyId, user
func (_m *PromotionRepository) FetchPromotionBuy(promoBuyId int, user domain.UserSession) (models.PromotionBuyEntity, error) {
	ret := _m.Called(promoBuyId, user)

	var r0 models.PromotionBuyEntity
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) models.PromotionBuyEntity); ok {
		r0 = rf(promoBuyId, user)
	} else {
		r0 = ret.Get(0).(models.PromotionBuyEntity)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(promoBuyId, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionBuyPayment provides a mock function with given fields: filter
func (_m *PromotionRepository) FetchPromotionBuyPayment(filter models.PromotionBuyPayment) ([]map[string]interface{}, error) {
	ret := _m.Called(filter)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(models.PromotionBuyPayment) []map[string]interface{}); ok {
		r0 = rf(filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.PromotionBuyPayment) error); ok {
		r1 = rf(filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionMember provides a mock function with given fields: promotionId
func (_m *PromotionRepository) FetchPromotionMember(promotionId ...interface{}) ([]map[string]interface{}, error) {
	var _ca []interface{}
	_ca = append(_ca, promotionId...)
	ret := _m.Called(_ca...)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(...interface{}) []map[string]interface{}); ok {
		r0 = rf(promotionId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...interface{}) error); ok {
		r1 = rf(promotionId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionOutlet provides a mock function with given fields: promotionId
func (_m *PromotionRepository) FetchPromotionOutlet(promotionId ...interface{}) ([]models.PromotionOutlet, error) {
	var _ca []interface{}
	_ca = append(_ca, promotionId...)
	ret := _m.Called(_ca...)

	var r0 []models.PromotionOutlet
	if rf, ok := ret.Get(0).(func(...interface{}) []models.PromotionOutlet); ok {
		r0 = rf(promotionId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.PromotionOutlet)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...interface{}) error); ok {
		r1 = rf(promotionId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionProduct provides a mock function with given fields: promotionId
func (_m *PromotionRepository) FetchPromotionProduct(promotionId ...interface{}) ([]models.PromotionProduct, error) {
	var _ca []interface{}
	_ca = append(_ca, promotionId...)
	ret := _m.Called(_ca...)

	var r0 []models.PromotionProduct
	if rf, ok := ret.Get(0).(func(...interface{}) []models.PromotionProduct); ok {
		r0 = rf(promotionId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.PromotionProduct)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...interface{}) error); ok {
		r1 = rf(promotionId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionRole provides a mock function with given fields: param
func (_m *PromotionRepository) FetchPromotionRole(param ...domain.PromotionRoleParam) ([]models.PromotionRole, error) {
	_va := make([]interface{}, len(param))
	for _i := range param {
		_va[_i] = param[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []models.PromotionRole
	if rf, ok := ret.Get(0).(func(...domain.PromotionRoleParam) []models.PromotionRole); ok {
		r0 = rf(param...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.PromotionRole)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...domain.PromotionRoleParam) error); ok {
		r1 = rf(param...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionType provides a mock function with given fields:
func (_m *PromotionRepository) FetchPromotionType() ([]models.PromotionType, error) {
	ret := _m.Called()

	var r0 []models.PromotionType
	if rf, ok := ret.Get(0).(func() []models.PromotionType); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.PromotionType)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GenerateSecretCode provides a mock function with given fields: secretCode, timeExpired, promotionBuyId, memberId
func (_m *PromotionRepository) GenerateSecretCode(secretCode string, timeExpired int64, promotionBuyId int, memberId int64) error {
	ret := _m.Called(secretCode, timeExpired, promotionBuyId, memberId)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, int64, int, int64) error); ok {
		r0 = rf(secretCode, timeExpired, promotionBuyId, memberId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemoveNotifyPromotion provides a mock function with given fields: notifyIds
func (_m *PromotionRepository) RemoveNotifyPromotion(notifyIds ...interface{}) error {
	var _ca []interface{}
	_ca = append(_ca, notifyIds...)
	ret := _m.Called(_ca...)

	var r0 error
	if rf, ok := ret.Get(0).(func(...interface{}) error); ok {
		r0 = rf(notifyIds...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemovePromotionBuy provides a mock function with given fields: promotionBuyId
func (_m *PromotionRepository) RemovePromotionBuy(promotionBuyId int) error {
	ret := _m.Called(promotionBuyId)

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(promotionBuyId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdatePromotionBuyPaymentStatus provides a mock function with given fields: promotionPaymentId, status
func (_m *PromotionRepository) UpdatePromotionBuyPaymentStatus(promotionPaymentId int64, status string) error {
	ret := _m.Called(promotionPaymentId, status)

	var r0 error
	if rf, ok := ret.Get(0).(func(int64, string) error); ok {
		r0 = rf(promotionPaymentId, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdatePromotionBuyStatus provides a mock function with given fields: promotionBuyId, status
func (_m *PromotionRepository) UpdatePromotionBuyStatus(promotionBuyId int, status string) error {
	ret := _m.Called(promotionBuyId, status)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, string) error); ok {
		r0 = rf(promotionBuyId, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewPromotionRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewPromotionRepository creates a new instance of PromotionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPromotionRepository(t mockConstructorTestingTNewPromotionRepository) *PromotionRepository {
	mock := &PromotionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
