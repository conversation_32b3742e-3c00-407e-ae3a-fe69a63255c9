// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"

	models "gitlab.com/uniqdev/backend/api-membership/models"
)

// UseCase is an autogenerated mock type for the UseCase type
type UseCase struct {
	mock.Mock
}

// AddNotifyPromotion provides a mock function with given fields: promoId, user
func (_m *UseCase) AddNotifyPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error) {
	ret := _m.Called(promoId, user)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) map[string]interface{}); ok {
		r0 = rf(promoId, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(promoId, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BuyPromotion provides a mock function with given fields: promoId, user
func (_m *UseCase) BuyPromotion(promoId int, user domain.UserSession) (map[string]interface{}, error) {
	ret := _m.Called(promoId, user)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) map[string]interface{}); ok {
		r0 = rf(promoId, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(promoId, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchMyDealsSecretCode provides a mock function with given fields: promotionBuyId, user
func (_m *UseCase) FetchMyDealsSecretCode(promotionBuyId int, user domain.UserSession) (map[string]interface{}, error) {
	ret := _m.Called(promotionBuyId, user)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) map[string]interface{}); ok {
		r0 = rf(promotionBuyId, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(promotionBuyId, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotion provides a mock function with given fields: filter, user
func (_m *UseCase) FetchPromotion(filter models.PromotionFilter, user domain.UserSession) ([]map[string]interface{}, error) {
	ret := _m.Called(filter, user)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(models.PromotionFilter, domain.UserSession) []map[string]interface{}); ok {
		r0 = rf(filter, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.PromotionFilter, domain.UserSession) error); ok {
		r1 = rf(filter, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPromotionBuyPayment provides a mock function with given fields: promoBuyId, paymentType, user
func (_m *UseCase) FetchPromotionBuyPayment(promoBuyId int, paymentType string, user domain.UserSession) (map[string]interface{}, error) {
	ret := _m.Called(promoBuyId, paymentType, user)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, string, domain.UserSession) map[string]interface{}); ok {
		r0 = rf(promoBuyId, paymentType, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, string, domain.UserSession) error); ok {
		r1 = rf(promoBuyId, paymentType, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ReceivePaymentUpdate provides a mock function with given fields: promotionBuyId, transactionId, status
func (_m *UseCase) ReceivePaymentUpdate(promotionBuyId int, transactionId string, status string) error {
	ret := _m.Called(promotionBuyId, transactionId, status)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, string, string) error); ok {
		r0 = rf(promotionBuyId, transactionId, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RunPromotionRole provides a mock function with given fields: param
func (_m *UseCase) RunPromotionRole(param ...domain.PromotionRoleParam) {
	_va := make([]interface{}, len(param))
	for _i := range param {
		_va[_i] = param[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	_m.Called(_ca...)
}

// SendNotifyPromotion provides a mock function with given fields:
func (_m *UseCase) SendNotifyPromotion() {
	_m.Called()
}

type mockConstructorTestingTNewUseCase interface {
	mock.TestingT
	Cleanup(func())
}

// NewUseCase creates a new instance of UseCase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewUseCase(t mockConstructorTestingTNewUseCase) *UseCase {
	mock := &UseCase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
