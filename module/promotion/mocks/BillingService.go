// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	models "gitlab.com/uniqdev/backend/api-membership/models"
)

// BillingService is an autogenerated mock type for the BillingService type
type BillingService struct {
	mock.Mock
}

// CreatePayment provides a mock function with given fields: _a0
func (_m *BillingService) CreatePayment(_a0 models.PromotionPaymentCreate) (models.PromotionPaymentResponse, error) {
	ret := _m.Called(_a0)

	var r0 models.PromotionPaymentResponse
	if rf, ok := ret.Get(0).(func(models.PromotionPaymentCreate) models.PromotionPaymentResponse); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(models.PromotionPaymentResponse)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(models.PromotionPaymentCreate) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewBillingService interface {
	mock.TestingT
	Cleanup(func())
}

// NewBillingService creates a new instance of BillingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewBillingService(t mockConstructorTestingTNewBillingService) *BillingService {
	mock := &BillingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
