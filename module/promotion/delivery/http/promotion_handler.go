package http

import (
	"encoding/json"
	"fmt"
	"strings"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
)

type promotionHandler struct {
	uc promotion.UseCase
}

func NewHttpPromotionHandler(app *fasthttprouter.Router, useCase promotion.UseCase) {
	handler := &promotionHandler{useCase}

	//fetch
	app.GET("/v2/promotion", auth.ValidatePublicKey(handler.FetchPromotionDeals))
	app.GET("/v3/promotion", auth.ValidatePublicKey(handler.FetchPromotion))
	app.GET("/v2/promotion/:id", auth.ValidatePublicKey(handler.FetchPromotion))

	//promotion-deals : my deals (taken from promotion_buy)
	app.GET("/v1/promotion-deals/:id/secret-code", auth.ValidateToken(handler.FetchMyDealsSecretCode))
	app.GET("/v1/promotion/:id/link", auth.ValidatePublicKey(handler.FetchPromotionLink))
	app.POST("/v1/promotion/:id/notify", auth.ValidateToken(handler.AddNotifyPromotion))

	// router.GET("/v1/promo/deals/myvoucher/group", auth.ValidateToken(v1.GetMyVoucherGroup))
	app.GET("/v2/promo/deals/myvoucher", auth.ValidateToken(handler.FetchMyVoucher))
	// app.GET("/v2/promo/deals/myvoucher/group", auth.ValidateToken(handler.FetchMyVoucher))

	//buy
	app.POST("/v2/promotion/:id/buy", auth.ValidateToken(handler.BuyPromotion))
	app.GET("/v1/promotion/:id/payment", auth.ValidateToken(handler.FetchPromotionBuyPayment))

	app.POST("/v1/promotion/:id/refund", auth.ValidateToken(handler.RefundPromotion))
}

// only get deals, regardless the request type, use v3 to request other type
func (h *promotionHandler) FetchPromotionDeals(ctx *fasthttp.RequestCtx) {
	id := cast.ToInt(ctx.UserValue("id"))
	paramMap := parser.ParseParamFastHttp(ctx)

	paramMap["types"] = strings.Split(cast.ToString(paramMap["types"]), ",")
	fmt.Println("param map:", paramMap)

	var filter models.PromotionFilter
	err := cast.MapToStruct(paramMap, &filter)
	log.IfError(err)

	filter.OutetIds = make([]int, 0)
	for _, id := range strings.Split(cast.ToString(paramMap["outlet_ids"]), ",") {
		if id == "" {
			continue
		}
		filter.OutetIds = append(filter.OutetIds, cast.ToInt(id))
	}

	filter.PromotionId = id

	//if not getting deals, return empty
	isRequestDeals := false
	for _, promotType := range filter.Types {
		if strings.EqualFold(promotType, "voucherpromo_deals") {
			isRequestDeals = true
			break
		}
	}

	var result []map[string]interface{}
	if isRequestDeals {
		result, err = h.uc.FetchPromotion(filter, domain.UserSessionFastHttp(ctx))
	}

	if err != nil {
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}

	resp := models.ApiResponse{Status: true, Message: "success", Data: result}
	if id > 0 && len(result) > 0 {
		resp.Data = result[0]
	}
	_ = json.NewEncoder(ctx).Encode(resp)
}

func (h *promotionHandler) FetchPromotion(ctx *fasthttp.RequestCtx) {
	id := cast.ToInt(ctx.UserValue("id"))
	paramMap := parser.ParseParamFastHttp(ctx)

	paramMap["types"] = strings.Split(cast.ToString(paramMap["types"]), ",")
	fmt.Println("param map:", paramMap)

	var filter models.PromotionFilter
	err := cast.MapToStruct(paramMap, &filter)
	log.IfError(err)

	filter.OutetIds = make([]int, 0)
	for _, id := range strings.Split(cast.ToString(paramMap["outlet_ids"]), ",") {
		if id == "" {
			continue
		}
		filter.OutetIds = append(filter.OutetIds, cast.ToInt(id))
	}

	filter.PromotionId = id
	result, err := h.uc.FetchPromotion(filter, domain.UserSessionFastHttp(ctx))

	if err != nil {
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}

	resp := models.ApiResponse{Status: true, Message: "success", Data: result}
	if id > 0 && len(result) > 0 {
		resp.Data = result[0]
	}
	_ = json.NewEncoder(ctx).Encode(resp)
}

func (h *promotionHandler) FetchMyDealsSecretCode(ctx *fasthttp.RequestCtx) {
	promotionBuyId := cast.ToInt(ctx.UserValue("id"))
	result, err := h.uc.FetchMyDealsSecretCode(promotionBuyId, domain.UserSessionFastHttp(ctx))

	if err != nil {
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
			if errCode > 400 {
				ctx.SetStatusCode(code.Code)
			}
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *promotionHandler) FetchPromotionLink(ctx *fasthttp.RequestCtx) {

}

func (h *promotionHandler) AddNotifyPromotion(ctx *fasthttp.RequestCtx) {
	promoId := cast.ToInt(ctx.UserValue("id"))
	result, err := h.uc.AddNotifyPromotion(promoId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		if code, ok := err.(exception.WithCode); ok {
			ctx.SetStatusCode(code.Code)
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *promotionHandler) BuyPromotion(ctx *fasthttp.RequestCtx) {
	promoId := cast.ToInt(ctx.UserValue("id"))
	result, err := h.uc.BuyPromotion(promoId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
			if errCode > 400 {
				ctx.SetStatusCode(code.Code)
			}
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *promotionHandler) FetchPromotionBuyPayment(ctx *fasthttp.RequestCtx) {
	promoBuyId := cast.ToInt(ctx.UserValue("id"))
	paymentType := string(ctx.QueryArgs().Peek("payment_type"))
	phoneNumber := string(ctx.QueryArgs().Peek("phone_number"))

	log.Info("req payment, promoBuyId: %v | type: %v | contact: %v", promoBuyId, paymentType, phoneNumber)
	if paymentType == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	result, err := h.uc.FetchPromotionBuyPayment(promoBuyId, paymentType, phoneNumber, domain.UserSessionFastHttp(ctx))
	if err != nil {
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
			if errCode >= 400 {
				ctx.SetStatusCode(code.Code)
			}
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *promotionHandler) RefundPromotion(ctx *fasthttp.RequestCtx) {
	promoId := cast.ToInt(ctx.UserValue("id"))
	result, err := h.uc.RefundPromotion(promoId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
			if errCode > 400 {
				ctx.SetStatusCode(code.Code)
			}
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *promotionHandler) FetchMyVoucher(ctx *fasthttp.RequestCtx) {
	//also handle group
	var param domain.MyVoucherRequest
	h.uc.FetchMyVoucher(param, domain.UserSessionFastHttp(ctx))
}
