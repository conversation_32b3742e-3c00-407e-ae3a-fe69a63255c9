package mysql

import (
	"database/sql"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
)

type promotionRepository struct {
	db db.Repository
}

func NewMysqlPromotionRepository(conn *sql.DB, cache domain.CacheInterface) promotion.PromotionRepository {
	return &promotionRepository{db.Repository{Conn: conn, CacheDb: cache}}
}

func (p promotionRepository) GenerateSecretCode(secretCode string, timeExpired int64, promotionBuyId int, memberId int64) error {
	_, err := db.Update("promotion_buy", map[string]interface{}{
		"secret_code":         secretCode,
		"secret_code_expired": timeExpired,
	}, "promotion_buy_id = ? and member_fkid = ?", promotionBuyId, memberId)

	return err
}

func (p promotionRepository) CheckExistingSecretCodes(secretCodes []string) ([]string, error) {
	if len(secretCodes) == 0 {
		return []string{}, nil
	}

	sql := `SELECT secret_code from promotion_buy where secret_code in @codes`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"codes": secretCodes,
	})

	result, err := p.db.Query(sql, params...).ArrayMap()
	if err != nil {
		return nil, err
	}

	existingCodes := make([]string, 0)
	for _, row := range result {
		existingCodes = append(existingCodes, cast.ToString(row["secret_code"]))
	}

	return existingCodes, nil
}

func (p promotionRepository) FetchPromotionRole(roleParams ...domain.PromotionRoleParam) ([]models.PromotionRole, error) {
	sql := `SELECT cpr.*, p.admin_fkid from crm_promotion_role cpr 
	join promotions p on p.promotion_id=cpr.promotion_fkid
	 where p.status=1 
	 and cpr.status='active'
	 and p.active=1
	 and (p.publish_date is null or p.publish_date <= UNIX_TIMESTAMP()*1000)
	 and p.end_promotion_date > UNIX_TIMESTAMP()*1000 	  
	 `
	params := make([]interface{}, 0)
	if len(roleParams) > 0 {
		roleParam := roleParams[0]
		if roleParam.AdminId > 0 {
			sql += ` and p.admin_fkid =? `
			params = append(params, roleParam.AdminId)
		}
	}

	// return db.QueryArray(sql, params...)

	var result []models.PromotionRole
	err := p.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (p promotionRepository) AddNotifyPromotion(promoId int, user domain.UserSession) (int64, error) {
	resp, err := db.Insert("crm_notify_product", map[string]interface{}{
		"admin_fkid":     user.AdminId,
		"member_fkid":    user.MemberId,
		"promotion_fkid": promoId,
		"data_created":   time.Now().Unix() * 1000,
	})
	if err != nil {
		return 0, err
	}

	id, _ := resp.LastInsertId()
	return id, nil
}

func (p promotionRepository) FetchNotifyPromotion(id ...int) ([]map[string]interface{}, error) {
	sql := ` SELECT cnp.crm_notify_product_id,cnp.admin_fkid, cnp.member_fkid, 
	p.promotion_id, p.publish_date, p.name, p.photo
	from crm_notify_product cnp 
	join promotions p on cnp.promotion_fkid=p.promotion_id
	where (p.publish_date is not NULL and DATE(FROM_UNIXTIME(p.publish_date/1000))=DATE(NOW())0 ) `
	return db.QueryArray(sql)
}

func (p promotionRepository) FetchNotifyPromotionById(id int64) (map[string]interface{}, error) {
	sql := ` SELECT cnp.crm_notify_product_id, cnp.admin_fkid, cnp.member_fkid, 
	p.promotion_id, p.publish_date, p.name, p.photo
	from crm_notify_product cnp 
	join promotions p on cnp.promotion_fkid=p.promotion_id
	where cnp.crm_notify_product_id = ?`
	return db.Query(sql, id)
}

func (p promotionRepository) FetchNotifyPromotionFilter(filter models.NotifyPromotionFilter) ([]map[string]interface{}, error) {
	sql := ` SELECT cnp.crm_notify_product_id, cnp.admin_fkid, cnp.member_fkid, 
	p.promotion_id, p.publish_date, p.name
	from crm_notify_product cnp 
	join promotions p on cnp.promotion_fkid=p.promotion_id `

	whereSql := make([]string, 0)
	if filter.AdminId != 0 {
		whereSql = append(whereSql, "cnp.admin_fkid = @adminId ")
	}
	if filter.MemberId != 0 {
		whereSql = append(whereSql, "cnp.member_fkid = @memberId ")
	}
	if filter.PromotionId != 0 {
		whereSql = append(whereSql, "p.promotion_id = @promotionId ")
	}

	if len(whereSql) > 0 {
		sql += " WHERE " + strings.Join(whereSql, " and ")
	}
	sql = sql + ` order by publish_date desc`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":     filter.AdminId,
		"memberId":    filter.MemberId,
		"promotionId": filter.PromotionId,
	})

	return db.QueryArray(sql, params...)
}

func (p promotionRepository) FetchNotificationToken(memberDetails ...models.MemberDetail) ([]map[string]interface{}, error) {
	ids := make([]interface{}, 0)
	adminIds := make([]interface{}, 0)
	for _, member := range memberDetails {
		ids = append(ids, member.MemberId)
		if member.AdminId > 0 {
			adminIds = append(adminIds, member.AdminId)
		}
	}

	if len(ids) == 0 {
		return nil, nil
	}

	sql := `SELECT md.member_fkid, md.admin_fkid, unt.token from user_notification_token unt 
	join members_detail md on md.members_detail_id=unt.user_id
	where unt.user_type='member' and unt.app='crm'
	and md.member_fkid in @memberIds `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"memberIds": ids,
	})
	return db.QueryArray(sql, params...)
}

func (p promotionRepository) RemoveNotifyPromotion(notifyIds ...interface{}) error {
	_, err := db.Delete("crm_notify_product", "crm_notify_product_id in "+db.WhereIn(len(notifyIds)), notifyIds...)
	return err
}

func (p promotionRepository) FetchPromotionWithDetail(filter models.PromotionFilter) ([]map[string]interface{}, error) {
	sql := `
SELECT p.*, coalesce(b.reedem, 0) as total_reedem, coalesce(pr.personal_reedem, 0) as total_personal_reedem,
if(cnp.crm_notify_product_id, 'on','off') as notify
FROM promotions p
         left join (select count(*) as reedem, min(pb.promotion_fkid) as promotion_id
                    from promotion_buy pb
					where source='self_buy' 
                    group by pb.promotion_fkid) as b on b.promotion_id = p.promotion_id
         left join (
    select count(*) as personal_reedem, promotion_fkid
    from promotion_buy pb
    where member_fkid = @memberId
    group by promotion_fkid
) as pr on pr.promotion_fkid = p.promotion_id
left join crm_notify_product cnp on cnp.promotion_fkid=p.promotion_id and cnp.member_fkid = @memberId 
WHERE p.admin_fkid = @adminId
  and from_unixtime(end_promotion_date / 1000 + @timeOffset , '%Y-%m-%d') >=
      from_unixtime(unix_timestamp() + @timeOffset, '%Y-%m-%d')
	and p.active = 1 
	and p.status = 1 
   `

	if filter.PromotionId > 0 {
		sql += " and p.promotion_id = @promotionId "
	}
	if filter.PromotionTypeId > 0 {
		sql += " and promotion_type_id = @promotionTypeId "
	}
	if len(filter.PromotionTypeIds) > 0 {
		sql += " and promotion_type_id in @promotionTypeIds "
	}
	if len(filter.OutetIds) > 0 {
		sql += " and p.promotion_id in (select promotion_outlets.promotion_id from promotion_outlets where outlet_id in @outletIds )"
	}

	if filter.PromotionTypeId == utils.PROMO_DEALS || array.Contain(filter.PromotionTypeIds, utils.PROMO_DEALS) {
		filter.PromotionTypeId = utils.PROMO_DEALS
		sql += " and ((p.promotion_type_id = @promotionTypeId and p.deals_can_buy = 1) or p.promotion_type_id != @promotionTypeId )"

		if os.Getenv("server") != "production" && filter.MemberTypeId > 0 {
			sql += " and p.promotion_id in (select promotion_id from promotion_member_type where member_type_id = @memberTypeId ) "
		}
	}

	sql += " ORDER BY p.promotion_id DESC "

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":          filter.AdminId,
		"memberId":         filter.MemberId,
		"promotionTypeId":  filter.PromotionTypeId,
		"timeOffset":       filter.TimeOffset,
		"promotionId":      filter.PromotionId,
		"memberTypeId":     filter.MemberTypeId,
		"promotionTypeIds": filter.PromotionTypeIds,
		"outletIds":        filter.OutetIds,
	})

	return db.QueryArray(sql, params...)
}

func (p promotionRepository) BuyPromotion(input models.PromotionBuy) (int64, error) {
	resp, err := db.Insert("promotion_buy", map[string]interface{}{
		"member_fkid":         input.MemberFkid,
		"promotion_fkid":      input.PromotionFkid,
		"promotion_type_fkid": input.PromotionTypeFkid,
		"price":               input.Price,
		"price_type":          input.PriceType,
		"time_created":        time.Now().Unix() * 1000,
		"time_modified":       time.Now().Unix() * 1000,
		"promo_nominal":       input.PromotionNominal,
		"status":              input.Status,
	})
	if err != nil {
		return 0, err
	}

	id, _ := resp.LastInsertId()
	return id, nil
}

func (p promotionRepository) AddPromotionBuyPayment(input models.PromotionBuyPayment) (int64, error) {
	resp, err := db.Insert("promotion_buy_payment", map[string]interface{}{
		"promotion_buy_fkid": input.PromotionBuyId,
		"payment_type":       input.PaymentType,
		"transaction_id":     input.TransactionId,
		"status":             input.Status,
		"payment_info":       input.PaymentInfo,
		"expired_at":         input.ExpiredAt,
	})
	if err != nil {
		return 0, err
	}

	id, _ := resp.LastInsertId()
	return id, nil
}

func (p promotionRepository) FetchPendingPayment(promoId int, user domain.UserSession) ([]map[string]interface{}, error) {
	sql := `SELECT pbp.* from promotion_buy_payment pbp 
	join promotion_buy pb on pb.promotion_buy_id=pbp.promotion_buy_fkid
	join promotions p on p.promotion_id=pb.promotion_fkid
	where pb.promotion_fkid=? and pb.member_fkid = ? and p.admin_fkid=? 
	AND pbp.status='pending'
	and pbp.expired_at > UNIX_TIMESTAMP()*1000 `
	return db.QueryArray(sql, promoId, user.MemberId, user.AdminId)
}

func (p promotionRepository) FetchPromotionBuyPayment(filter models.PromotionBuyPayment) ([]map[string]interface{}, error) {
	sql := `SELECT pbp.*, p.promotion_id, pb.member_fkid, p.admin_fkid, p.name, 
	pb.status as promotion_buy_status
	from promotion_buy_payment pbp 
	join promotion_buy pb on pb.promotion_buy_id=pbp.promotion_buy_fkid
	join promotions p on p.promotion_id=pb.promotion_fkid `

	whereSqls := make([]string, 0)
	if filter.PromotionBuyId > 0 {
		whereSqls = append(whereSqls, `pb.promotion_buy_id=@promotionBuyId`)
	}
	if filter.PaymentType != "" {
		whereSqls = append(whereSqls, `pbp.payment_type=@paymentType`)
	}
	if filter.TransactionId != "" {
		whereSqls = append(whereSqls, `pbp.transaction_id=@transactionId`)
	}
	if filter.ExpiredAt > 0 {
		whereSqls = append(whereSqls, `pbp.expired_at <= @expiredAt`)
	}

	if len(whereSqls) > 0 {
		whereSQL := strings.Join(whereSqls, " and ")
		sql += " WHERE " + whereSQL
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"promotionBuyId": filter.PromotionBuyId,
		"paymentType":    filter.PaymentType,
		"transactionId":  filter.TransactionId,
		"expiredAt":      filter.ExpiredAt,
	})
	return db.QueryArray(sql, params...)
}

func (p promotionRepository) UpdatePromotionBuyStatus(promotionBuyId int, status string) error {
	resp, err := db.Update("promotion_buy", map[string]interface{}{"status": status}, "promotion_buy_id = ?", promotionBuyId)
	if err != nil {
		return err
	}

	fmt.Print("rows effected (promotion_buy): ")
	fmt.Println(resp.RowsAffected())
	return err
}

func (p promotionRepository) UpdatePromotionBuyPaymentStatus(promotionPaymentId int64, status string) error {
	resp, err := db.Update("promotion_buy_payment", map[string]interface{}{"status": status}, "promotion_buy_payment_id = ?", promotionPaymentId)
	if err != nil {
		return err
	}
	fmt.Print("rows effected (promotion_buy_payment): ")
	fmt.Println(resp.RowsAffected())
	return err
}

func (p promotionRepository) RemovePromotionBuy(promotionBuyId int) error {
	err := db.WithTransaction(func(t db.Transaction) error {
		t.Delete("promotion_buy_payment", "promotion_buy_fkid=?", promotionBuyId)
		t.Delete("promotion_buy", "promotion_buy_id=?", promotionBuyId)
		return nil
	})
	return err
}

func (p promotionRepository) FetchPromotionOutlet(promotionId ...interface{}) ([]models.PromotionOutlet, error) {
	sql := `select o.name, o.outlet_id, po.promotion_id 
	from promotion_outlets po 
	join outlets o on po.outlet_id = o.outlet_id 
	where o.app_show = 1 and po.promotion_id in @promotionIds `

	sql, params := db.MapParam(sql, map[string]interface{}{
		"promotionIds": promotionId,
	})
	// return p.db.Query(sql, params...).PrintSql().ArrayMap()

	var result []models.PromotionOutlet
	err := p.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (p promotionRepository) FetchPromotionProduct(promotionId ...interface{}) ([]models.PromotionProduct, error) {
	sql := `select pp.price as price_promo, pd.price_sell, p.name, pd.product_detail_id, p.product_id, 
	pp.promotion_id, pp.type, pp.qty
	from promotion_products pp 
	join products_detail pd on pp.product_detail_fkid = pd.product_detail_id 
	join products p on pd.product_fkid = p.product_id 
	where pp.promotion_id in @promotionIds `
	//and (pp.type != 'order_menu' or pp.type is null)

	sql, params := db.MapParam(sql, map[string]interface{}{
		"promotionIds": promotionId,
	})

	// return p.db.Query(sql, params...).PrintSql().ArrayMap()

	var result []models.PromotionProduct
	err := p.db.QueryCache("PromotionProduct", 8*time.Hour, sql, params...).Model(&result)
	if log.IfError(err) {
		err = p.db.Prepare(sql, params...).Get(&result)
	}
	return result, err
}

func (p promotionRepository) FetchPromotionType() ([]models.PromotionType, error) {
	sql := ` SELECT pt.promotion_type_id, pt.name as type_name, ptp.name as parent_type_name,
REPLACE(CONCAT(LOWER(ptp.name), '_', LOWER(pt.name)), ' ','') as promotion_type  
from promotion_types pt 
join promotion_types ptp on ptp.promotion_type_id=pt.parent_id
where pt.parent_id > 0`

	var result []models.PromotionType
	// err := p.db.Query(sql).Model(&result)
	// err := p.db.Prepare(sql).Get(&result)
	err := p.db.QueryCache("PromotionType", 24*7*time.Hour, sql).Model(&result)
	if log.IfError(err) {
		err = p.db.Prepare(sql).Get(&result)
	}
	return result, err
}

func (p promotionRepository) FetchPromotionMember(promotionId ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT pmt.promotion_id, pmt.member_type_id, p.name as member_type_name  
	from promotion_member_type pmt 
	join members_type mt on mt.type_id=pmt.member_type_id
	join products p on p.product_id=mt.product_fkid
	 where promotion_id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": promotionId,
	})
	result, err := p.db.QueryCache("PromotionMember", 8*time.Hour, sql, params...).ArrayMap()
	if log.IfError(err) {
		return p.db.Query(sql, params...).ArrayMap()
	}
	return result, err
}

func (p promotionRepository) FetchPromotionBuy(filter models.PromotionBuyFilter) ([]models.PromotionBuyEntity, error) {
	sql := `
	SELECT pb.* FROM promotion_buy pb 
	JOIN promotions p ON pb.promotion_fkid = p.promotion_id
	WHERE 1=1
	`
	params := make([]interface{}, 0)

	if filter.PromotionBuyId > 0 {
		sql += ` AND pb.promotion_buy_id = ? `
		params = append(params, filter.PromotionBuyId)
	}

	if filter.MemberId > 0 {
		sql += ` AND pb.member_fkid = ? `
		params = append(params, filter.MemberId)
	}

	if filter.AdminId > 0 {
		sql += ` AND p.admin_fkid = ? `
		params = append(params, filter.AdminId)
	}

	if filter.Status != "" {
		sql += ` AND pb.status = ? `
		params = append(params, filter.Status)
	}

	if filter.ExactExpireDay > 0 {
		// Add condition to check for promotions expiring in exactly X days
		sql += ` AND DATE(FROM_UNIXTIME(p.end_promotion_date/1000)) = DATE(DATE_ADD(NOW(), INTERVAL ? DAY))`
		params = append(params, filter.ExactExpireDay)
	}

	var result []models.PromotionBuyEntity
	err := p.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (p promotionRepository) RefundPromotion(promoBuyId int, user domain.UserSession) error {
	return db.WithTransaction(func(t db.Transaction) error {
		res, _ := t.Delete("promotion_buy", "promotion_buy_id = ? and member_fkid = ?", promoBuyId, user.MemberId)
		rowsDeleted, _ := res.RowsAffected()
		fmt.Println("row promotion_buy deleted: ", rowsDeleted)
		if rowsDeleted == 0 {
			return fmt.Errorf("promo buy not found")
		}
		return nil
	})
}

func (p promotionRepository) FetchPromotion(filter models.PromotionDetailFilter) ([]map[string]interface{}, error) {
	sql := `SELECT * FROM promotions WHERE 1=1`
	params := make([]interface{}, 0)

	if len(filter.PromotionIds) > 0 {
		sql += ` AND promotion_id IN ` + db.WhereIn(len(filter.PromotionIds))
		for _, id := range filter.PromotionIds {
			params = append(params, id)
		}
	}

	return p.db.Query(sql, params...).ArrayMap()
}

const (
	DYNAMIC_LINK_CACHE_PREFIX = "dynamic_link:"
	DYNAMIC_LINK_CACHE_TTL    = 24 * 15 * time.Hour // Cache for 15 days
)

func (p promotionRepository) GetDynamicLinkCache(promoBuyId string) (string, error) {
	//handle if cache not initialized
	if p.db.CacheDb == nil {
		log.Info("cache not initialized")
		return "", fmt.Errorf("cache not initialized")
	}
	key := DYNAMIC_LINK_CACHE_PREFIX + promoBuyId
	val, err := p.db.CacheDb.Get(key)
	if err != nil {
		log.Info("cache not found or error: %v", err)
		return "", err
	}
	return cast.ToString(val), nil
}

func (p promotionRepository) SetDynamicLinkCache(promoBuyId string, link string) error {
	// key := DYNAMIC_LINK_CACHE_PREFIX + promoBuyId
	// return p.db.CacheDb.Set(key, link, DYNAMIC_LINK_CACHE_TTL)
	return nil
}
