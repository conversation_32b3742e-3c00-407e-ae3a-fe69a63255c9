package service

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
)

type billingService struct {
	serviceUrl string
}

func NewBillingService() promotion.BillingService {
	return &billingService{
		serviceUrl: os.Getenv("API_BILLING"),
	}
}

func (b billingService) CreatePayment(param models.PromotionPaymentCreate) (models.PromotionPaymentResponse, error) {
	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("%s/v1/payment", b.serviceUrl),
		Header: map[string]interface{}{
			"business_id": param.AdminId,
		},
		PostRequest: utils.PostRequest{
			Body: map[string]interface{}{
				"payment_method": param.PaymentType,
				"id":             param.Id,
				"amount":         param.<PERSON>,
				"callback_url":   param.CallbackUrl,
				"customer": map[string]interface{}{
					"name":  param.Customer.Name,
					"email": param.Customer.Email,
					"phone": param.Customer.Phone,
				},
			},
		},
	}

	resp, err := req.Execute()
	if err != nil {
		return models.PromotionPaymentResponse{}, err
	}

	if resp.StatusCode != 200 {
		return models.PromotionPaymentResponse{}, fmt.Errorf(resp.Body)
	}

	var result models.PromotionPaymentResponse
	err = json.Unmarshal([]byte(resp.Body), &result)

	return result, err
}
