// Code generated by mockery v2.15.0. DO NOT EDIT.

package transaction

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

// TransactionUseCase is an autogenerated mock type for the TransactionUseCase type
type TransactionUseCase struct {
	mock.Mock
}

// CreateTransaction provides a mock function with given fields: transaction, user
func (_m *TransactionUseCase) CreateTransaction(transaction domain.Transaction, user domain.UserSession) (map[string]interface{}, error) {
	ret := _m.Called(transaction, user)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.Transaction, domain.UserSession) map[string]interface{}); ok {
		r0 = rf(transaction, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.Transaction, domain.UserSession) error); ok {
		r1 = rf(transaction, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchTransactionSetting provides a mock function with given fields: outletId, userSession
func (_m *TransactionUseCase) FetchTransactionSetting(outletId int, userSession domain.UserSession) (interface{}, error) {
	ret := _m.Called(outletId, userSession)

	var r0 interface{}
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) interface{}); ok {
		r0 = rf(outletId, userSession)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(outletId, userSession)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewTransactionUseCase interface {
	mock.TestingT
	Cleanup(func())
}

// NewTransactionUseCase creates a new instance of TransactionUseCase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewTransactionUseCase(t mockConstructorTestingTNewTransactionUseCase) *TransactionUseCase {
	mock := &TransactionUseCase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
