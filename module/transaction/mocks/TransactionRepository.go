// Code generated by mockery v2.15.0. DO NOT EDIT.

package transaction

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

// TransactionRepository is an autogenerated mock type for the TransactionRepository type
type TransactionRepository struct {
	mock.Mock
}

// AddTransasction provides a mock function with given fields: transaction, user
func (_m *TransactionRepository) AddTransasction(transaction domain.Transaction, user domain.UserSession) (int64, error) {
	ret := _m.Called(transaction, user)

	var r0 int64
	if rf, ok := ret.Get(0).(func(domain.Transaction, domain.UserSession) int64); ok {
		r0 = rf(transaction, user)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.Transaction, domain.UserSession) error); ok {
		r1 = rf(transaction, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CountTransactionToday provides a mock function with given fields: outletId
func (_m *TransactionRepository) CountTransactionToday(outletId int) (int, error) {
	ret := _m.Called(outletId)

	var r0 int
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(outletId)
	} else {
		r0 = ret.Get(0).(int)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(outletId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOrderTypeSetting provides a mock function with given fields: outletId, session
func (_m *TransactionRepository) FetchOrderTypeSetting(outletId int, session domain.UserSession) ([]map[string]interface{}, error) {
	ret := _m.Called(outletId, session)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) []map[string]interface{}); ok {
		r0 = rf(outletId, session)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.UserSession) error); ok {
		r1 = rf(outletId, session)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewTransactionRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewTransactionRepository creates a new instance of TransactionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewTransactionRepository(t mockConstructorTestingTNewTransactionRepository) *TransactionRepository {
	mock := &TransactionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
