package http

import (
	"encoding/json"

	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/models"
)


func sendResponse(ctx *fasthttp.RequestCtx, err error, result interface{}) {
	if err != nil {
		code := 0
		if witcode, ok := err.(exception.WithCode); ok {
			if witcode.Code >= 200 {
				ctx.SetStatusCode(witcode.Code)
			} else {
				code = witcode.Code
			}

		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: code, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}
