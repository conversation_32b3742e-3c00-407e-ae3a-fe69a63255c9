package mysql

import (
	"encoding/json"
	"fmt"
	"testing"

	"gitlab.com/uniqdev/backend/api-membership/models"
)

func Test_transactionModel(t *testing.T) {
	dataJson := `{"outlet_id":29,"order_list":[{"product_detail_id":683,"qty":1,"tax":[{"gratuity_id":18,"total":2190},{"gratuity_id":110,"total":2190}],"product":{"name":"Americano Hot"}},{"product_detail_id":684,"qty":1,"tax":[{"gratuity_id":18,"total":2390},{"gratuity_id":110,"total":2390}],"product":{"name":"Americano Ice"}}],"order_type":"dine_in","shipment":{},"pickup":{},"deal":{}}`
	var transaction models.TransactionCreate
	err := json.Unmarshal([]byte(dataJson), &transaction)
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}

	salesModel, err := json.Marshal(transaction.ToSalesModel())
	if err != nil {
		t.<PERSON>rror(err)
	}

	fmt.Println(">> ", string(salesModel))
}
