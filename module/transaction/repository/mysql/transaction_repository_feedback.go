package mysql

import (
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

// AddFeedbackSummary implements transaction.Repository.
func (t *transactionRepository) AddFeedbackSummary(res models.FeedbackSummaryResponse, request domain.FeedbackSummaryRequest, user domain.UserSession) error {	
	//save to cache
	key := fmt.Sprintf("feedback_summary_%s-%v-%v_%v", os.Getenv("ENV"), user.AdminId, request.StartDate, request.EndDate)
	//first delete from cache 
	t.cache.Delete(key)

	err := t.cache.Set(key, utils.SimplyToJson(res), 24*14*time.Hour)
	if err != nil {
		return err
	}
	return err
}

// FetchFeedbackSummaryHitory implements transaction.Repository.
func (t *transactionRepository) FetchFeedbackSummaryHitory(user domain.UserSession) ([]models.FeedbackSummaryHistoryResponse, error) {
	key := fmt.Sprintf("feedback_summary_%s-%v-*", os.Getenv("ENV"), user.AdminId)
	summaries, err := t.cache.GetMatching(key)
	log.Info("key: %v, size: %v | err %v", key, len(summaries), err)

	result := make([]models.FeedbackSummaryHistoryResponse, 0)
	for key, summary := range summaries {
		var summaryModel models.FeedbackSummaryResponse
		err = json.Unmarshal([]byte(summary), &summaryModel)
		if log.IfError(err){
			log.Info("failed unmarshal: |%v|", summary)
		}

		re := regexp.MustCompile(`-(\d+)_(\d+)$`)
		match := re.FindStringSubmatch(key)		

		if len(match) >= 2 {
			lastTwoNumbers := fmt.Sprintf("%s_%s", match[1], match[2])
			fmt.Println(lastTwoNumbers) // Output: 16797979775454_16797979775454
			result = append(result, models.FeedbackSummaryHistoryResponse{
				StartDate: cast.ToInt64(match[1]),
				EndDate:   cast.ToInt64(match[2]),
				Summary:   summaryModel,
			})
		} else {
			log.Info("No match found, key: '%v' | match: %v", key, match)
		}
	}
	return result, nil
}

// FetchFeedbackSummary implements transaction.Repository.
func (t *transactionRepository) FetchFeedbackSummary(request domain.FeedbackSummaryRequest, user domain.UserSession) (models.FeedbackSummaryResponse, error) {
	var result models.FeedbackSummaryResponse
	key := fmt.Sprintf("feedback_summary_%s-%v-%v_%v", os.Getenv("ENV"), user.AdminId, request.StartDate, request.EndDate)
	summary, err := t.cache.Get(key)
	if err != nil {
		return result, nil
	}

	err = json.Unmarshal([]byte(summary), &result)
	return result, err
}
