package mysql

import "gitlab.com/uniqdev/backend/api-membership/models"

// FetchOrderShipment implements transaction.Repository.
func (t *transactionRepository) FetchOrderShipment(id string) (models.TransactionShipment, error) {
	var result models.TransactionShipment
	sql := `select * from order_sales_shipments where order_sales_id = ?`
	err := t.db.Prepare(sql, id).Get(&result)
	return result, err
}
