package nlp

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

type nlpRepository struct {
	baseUrl string
}

func NewNlpRepository() transaction.RepositoryNlp {
	return &nlpRepository{
		baseUrl: os.Getenv("API_NLP"),
	}
}

func (n *nlpRepository) GetSentiment(text string) (models.SentimentResponse, error) {
	req := utils.HttpRequest{
		Method: "GET",
		Url:    n.baseUrl + "/v1/sentiment",
		Params: map[string]interface{}{
			"text": text,
		},
	}

	resp, err := req.ExecuteRequest()
	fmt.Printf("nlp GetSentiment --> %v | text: %v | err: %v \n", string(resp), text, err)
	if err != nil {
		return models.SentimentResponse{}, err
	}

	var result models.SentimentResponse
	err = json.Unmarshal(resp, &result)

	if result.Classification != "" && result.Label == "" {
		result.Label = result.Classification
	}

	return result, err
}

// GenerateSentimentReport implements transaction.RepositoryNlp.
func (n *nlpRepository) GenerateSentimentReport(sentiments []models.SentimentReportRequest) (models.SentimentReportResponse, error) {
	req := utils.HttpRequest{
		Method: "POST",
		Url:    n.baseUrl + "/v1/report/sentiment",
		PostRequest: utils.PostRequest{
			Body: sentiments,
		},
	}

	resp, err := req.ExecuteRequest()
	fmt.Println("nlp --> ", string(resp), " | err: ", err)
	if err != nil {
		return models.SentimentReportResponse{}, err
	}

	var result models.SentimentReportResponse
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return models.SentimentReportResponse{}, err
	}

	return result, err
}

// GenerateFeedbackSummary implements transaction.RepositoryNlp.
func (n *nlpRepository) GenerateFeedbackSummary(feedbacks []models.FeedbackSummaryRequest) (models.FeedbackSummaryResponse, error) {
	req := utils.HttpRequest{
		Method: "POST",
		Url:    n.baseUrl + "/v1/report/summary",
		PostRequest: utils.PostRequest{
			Body: feedbacks,
		},
	}

	resp, err := req.Execute()
	fmt.Println("nlp --> ", string(resp.Body), " | err: ", err)
	if err != nil {
		return models.FeedbackSummaryResponse{}, err
	}

	if resp.StatusCode >= 300 {
		return models.FeedbackSummaryResponse{}, fmt.Errorf("status code %v", resp.StatusCode)
	}

	var result models.FeedbackSummaryResponse
	err = json.Unmarshal([]byte(resp.Body), &result)
	if err != nil {
		return models.FeedbackSummaryResponse{}, err
	}

	return result, err
}
