package transaction

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type Repository interface {
	FetchOrderTypeSetting(outletId int, session domain.UserSession) ([]models.OrderConfigurationDetail, error)
	FetchPromotion(promotionId int) (map[string]interface{}, error)
	CountPromotionUsed(promotionId int, daysInterval int) (int, error)
	CountTransactionToday(outletId int) (int, error)
	AddTransasction(transaction models.TransactionCreate, user domain.UserSession) (int64, error)
	UpdateDeal(promotionBuyId int, status string) error
	FetchTransactionStatus(orderId string, user domain.UserSession) ([]models.OrderStatusEntity, error)

	FetchSales(salesId string) (models.SalesEntity, error)
	FetchSalesUnGivenFeedback(receiver string, dayInterval int) ([]models.SalesEntity, error)
	UpdateSales(sales models.SalesEntity) error
	FetchUserBySales(salesId string) (domain.UserSession, error)

	FetchSalesItem(salesIds []string) (*[]models.TransactionItem, error)

	FetchOrderSales(id string, user domain.UserSession) (models.OrderSalesEntity, error)
	FetchTimeoutPaymentOrder(domain.TimeoutOrderFilter) ([]domain.TimeoutOrderResponse, error)
	FetchOrderShipment(id string) (models.TransactionShipment, error)
	UpdateOrderSales(order models.OrderSalesEntity) error
	UpdateOrderSalesStatus(id int, data domain.OrderSalesUpdateData) error

	FetchSalesFeedback(salesId string, user domain.UserSession) (models.SalesFeedbackEntity, error)
	FetchSalesFeedbackById(ids ...int) ([]models.SalesFeedbackEntity, error)
	FetchSalesFeedbackWithFilter(filter models.SalesFeedbackFilter) ([]models.SalesFeedbackWithUser, error)
	AddSalesFeedback(feedback models.SalesFeedbackEntity) (int64, error)
	UpdateSalesFeedback(feedback models.SalesFeedbackEntity) error
	FetchSalesFedbackNoSentiment() ([]models.SalesFeedbackEntity, error)

	FetchFeedbackSummary(req domain.FeedbackSummaryRequest, user domain.UserSession) (models.FeedbackSummaryResponse, error)
	AddFeedbackSummary(res models.FeedbackSummaryResponse, request domain.FeedbackSummaryRequest, user domain.UserSession) error
	FetchFeedbackSummaryHitory(user domain.UserSession) ([]models.FeedbackSummaryHistoryResponse, error)

	FetchRating(outletId int, user *domain.UserSession) (*models.RatingResponse, error)
	FetchRatingCount(outletId int, user *domain.UserSession) (*[]models.RatingResponse, error)
	FetchReview(outletId int, user *domain.UserSession) (*[]models.ReviewResponse, error)

	FetchPaymentMethod(user domain.UserSession) ([]models.PaymentMethod, error)
	FetchBank(user domain.UserSession, outletId int) ([]models.PaymentMediaBankEntity, error)
	FetchOrderConfig(user domain.UserSession, configType string) ([]models.OrderConfigEntity, error)
	UpdateOrderConfig(config models.OrderConfigEntity, user domain.UserSession) error

	FetchAdmin(adminId int) (models.Admin, error)
	FetchReportRecipient(adminId int, reportTypes string) ([]models.ReportRecipient, error)

	//ai
	FetchTransactionSummary(requst domain.TransactionSummaryRequest) ([]map[string]interface{}, error)
}

type RepositoryNlp interface {
	GetSentiment(text string) (models.SentimentResponse, error)
	GenerateSentimentReport(sentiments []models.SentimentReportRequest) (models.SentimentReportResponse, error)
	GenerateFeedbackSummary(feedbacks []models.FeedbackSummaryRequest) (models.FeedbackSummaryResponse, error)
}
