package usecase

import (
	"encoding/json"
	"sync"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

var muPayment sync.Mutex

func (t *transactionUseCase) RunPaymentTimeout() {
	muPayment.Lock()
	defer muPayment.Unlock()

	//cancel transaction
	filter := domain.TimeoutOrderFilter{
		Status: []string{"accept"},
	}
	orders, err := t.repo.FetchTimeoutPaymentOrder(filter)

	log.Info("total orders: %v, by filter: %v", len(orders), utils.SimplyToJson(filter))
	if log.IfError(err) {
		return
	}

	if len(orders) == 0 {
		return
	}

	//fetch config
	configs, err := t.repo.FetchOrderConfig(domain.UserSession{}, "transaction")
	log.IfError(err)

	log.Info("configs: %v", utils.SimplyToJson(configs))
	configByAdminId := make(map[int]models.OrderConfigEntity)
	paymentTimoutByAdmin := make(map[int]int)
	for _, conf := range configs {
		configByAdminId[conf.AdminID] = conf
		var configValue map[string]interface{}
		if log.IfError(json.Unmarshal([]byte(conf.Value), &configValue)) {
			return
		}
		log.Info("configValue: %v", utils.SimplyToJson(configValue))
		paymentTimeout := 60 * 24 //in minute (default 24 hour)
		if timeout, ok := configValue["payment_timeout"]; ok {
			paymentTimeout = int(cast.ToFloat(timeout))
		}
		paymentTimoutByAdmin[conf.AdminID] = paymentTimeout
	}

	log.Info("paymentTimeout: %v", utils.SimplyToJson(paymentTimoutByAdmin))
	for _, order := range orders {
		log.Info("%v, diff: %v | timeout: %v", cast.ToFloat(order.DiffMinutes), order.DiffMinutes, paymentTimoutByAdmin[order.AdminFkid])
		if cast.ToFloat(order.DiffMinutes) > cast.ToFloat(paymentTimoutByAdmin[order.AdminFkid]) {
			log.Info("timeout for %v, diff: %v, threshold: %v", order.OrderSalesID, order.DiffMinutes, paymentTimoutByAdmin[order.AdminFkid])
			t.cancelOrder(order.ID, order.OrderSalesID, order.AdminFkid)
		}
	}

}

func (t *transactionUseCase) cancelOrder(id int, orderSalesId string, adminId int) {
	//update status
	err := t.repo.UpdateOrderSalesStatus(id, domain.OrderSalesUpdateData{
		Status:       domain.OrderStatusCancel,
		Message:      "melebihi batas waktu pembayaran",
		OrderSalesId: orderSalesId,
		AdminId:      adminId,
	})
	if log.IfError(err) {
		return
	}

	//send notification
}
