package usecase

import (
	"fmt"
)

func feedbackMessage(name string, point int, urls []string) string {
	fmt.Println("urls: ", urls)
	message := fmt.Sprintf("Hai Kak *%v*, \nTerima kasih untuk feedbacknya! Sebagai bentuk rasa terima kasih, kami telah menambahkan *%v poin* ke akun kakak.\n\n", name, point)
	message += "Tetap ingin mengumpulkan poin lebih banyak? Masih ada transaksi yang menunggu feedback dari kakak. \nKlik link di bawah ini ya:\n"

	// message += strings.Join(urls, "\n")
	for i, url := range urls {
		message += fmt.Sprintf("- %v\n", url)
		if i < len(urls)-1 {
			message += "\n"
		}
	}

	message += "\nJangan lewatkan kesempatan ini untuk memanfaatkan poinmu dan membantu kami meningkatkan layanan kami!"
	return message
}
