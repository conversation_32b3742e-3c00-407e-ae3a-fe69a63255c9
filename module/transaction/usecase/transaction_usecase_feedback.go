package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/extract"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/generate"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func (t *transactionUseCase) FetchRating(outletId int, user *domain.UserSession) (*models.RatingResponse, error) {
	ratingSummary, err := t.repo.FetchRating(outletId, user)
	if log.IfError(err) {
		return nil, err
	}

	ratingCount, err := t.repo.FetchRatingCount(outletId, user)
	if log.IfError(err) {
		return nil, err
	}

	ratingSummary.CountDetail = *ratingCount
	return ratingSummary, nil
}

func (t *transactionUseCase) FetchReview(outletId int, user *domain.UserSession) (*[]models.ReviewResponse, error) {
	reviews, err := t.repo.FetchReview(outletId, user)
	if log.IfError(err) {
		return nil, err
	}

	//fetch items
	salesIds := make([]string, 0, len(*reviews))
	for _, row := range *reviews {
		salesIds = append(salesIds, row.SalesId)
	}

	salesItems, err := t.repo.FetchSalesItem(salesIds)
	if log.IfError(err) {
		return nil, err
	}

	salesItemMap := make(map[string][]string)
	for _, sales := range *salesItems {
		if _, ok := salesItemMap[sales.SalesId]; !ok {
			salesItemMap[sales.SalesId] = make([]string, 0)
		}
		salesItemMap[sales.SalesId] = append(salesItemMap[sales.SalesId], sales.ProductName)
	}

	for i, review := range *reviews {
		//set OrderItem for current loop (i), get the item from salesItemMap
		(*reviews)[i].OrderItem = salesItemMap[review.SalesId]

		// Convert attachments from JSON string to []string
		var attachments []string
		err := json.Unmarshal([]byte(review.Attachments), &attachments)
		if err != nil {
			// Handle the error appropriately, e.g., log it or set attachments to an empty slice
			attachments = make([]string, 0)
		}
		(*reviews)[i].Attachment = attachments

		//remove unwanted data
		(*reviews)[i].SalesId = ""
		(*reviews)[i].Attachments = ""
		(*reviews)[i].Customer = cutLongName(review.Customer)
	}

	return reviews, nil
}

// handle messaging
func (t *transactionUseCase) ReceiveFeedback(salesId string) error {
	salesFeedback, err := t.repo.FetchSalesFeedback(salesId, domain.UserSession{})
	if log.IfError(err) {
		return err
	}

	if salesFeedback.SalesFeedbackID == 0 {
		log.IfError(fmt.Errorf("sales feedback not found: '%v'", salesId))
		return nil
	}

	if salesFeedback.Sentiment == "" {
		go t.UpdateFeedbackSentiment(salesFeedback.SalesFeedbackID)
	}

	if salesFeedback.PointEarned > 0 {
		log.IfError(fmt.Errorf("can not set point feedback, it already given, %v -> %v point (id: %v)", salesId, salesFeedback.PointEarned, salesFeedback.SalesFeedbackID))
		return nil
	}

	user, err := t.repo.FetchUserBySales(salesId)
	if log.IfError(err) {
		return err
	}

	if user.AdminId == 0 || user.MemberId == 0 {
		log.Info("can not get memberId, or salesId from %v", salesId)
		return nil
	}

	log.Info("running-point-collection for: %v, member: %v", salesFeedback.SalesFeedbackID, user.MemberId)
	go t.ucPointCollection.RunPointCollection(user.MemberId, user.AdminId, domain.PointCollectionActionFeedback, salesFeedback.SalesFeedbackID)
	go t.askAnotherFeedback(salesId)

	return nil
}

// SendFeedbackReport implements transaction.UseCase.
func (t *transactionUseCase) SendFeedbackReport() error {
	log.Info("start SendFeedbackReport....")

	//to wake up the server
	t.repoNlp.GetSentiment("ping")

	//fetch data for the last week
	feedbacks, err := t.repo.FetchSalesFeedbackWithFilter(models.SalesFeedbackFilter{
		HasComment:  true,
		DayInterval: 7,
	})
	log.Info("total feedback: %v | %v", len(feedbacks), err)
	if log.IfError(err) || len(feedbacks) == 0 {
		return err
	}

	feedbacksByUser := make(map[int][]models.SalesFeedbackEntity)
	for _, feedback := range feedbacks {
		if feedback.Sentiment == "" || feedback.Comment == "" {
			continue
		}
		feedbacksByUser[feedback.AdminId] = append(feedbacksByUser[feedback.AdminId], feedback.SalesFeedbackEntity)
	}

	log.Info("total feedback: %v | feedbackByUser: %v", len(feedbacks), len(feedbacksByUser))
	for adminId, feedbacks := range feedbacksByUser {
		log.Info("%v has %v feedback", adminId, len(feedbacks))
		admin, err := t.repo.FetchAdmin(adminId)
		log.IfError(err)
		report := t.generateFeedbackReport(admin, feedbacks)
		t.sendReport(admin, report)
	}

	return nil
}

func (t *transactionUseCase) sendReport(admin models.Admin, report string) error {
	//send to admin
	t.repoCampaign.AddScheduleMessage(domain.ScheduleMessage{
		Title:    "Feedback Report",
		Message:  report,
		Receiver: admin.Phone,
	})

	//send to other receiver
	recipients, err := t.repo.FetchReportRecipient(admin.AdminID, "crm_feedback")
	log.IfError(err)

	for _, user := range recipients {
		if user.Phone == "" {
			continue
		}
		t.repoCampaign.AddScheduleMessage(domain.ScheduleMessage{
			Title:    "Feedback Report",
			Message:  report,
			Receiver: user.Phone,
		})
	}
	return nil
}

func (t *transactionUseCase) generateFeedbackReport(admin models.Admin, feedbacks []models.SalesFeedbackEntity) string {
	var minDate, maxDate int64 = feedbacks[0].DateCreated, feedbacks[0].DateCreated
	var totalFeedback, positiveFeedback, negativeFeedback, neutralFeedback int

	// Calculate statistics
	for _, feedback := range feedbacks {
		totalFeedback++

		switch feedback.Sentiment {
		case "positive":
			positiveFeedback++
		case "negative":
			negativeFeedback++
		case "neutral":
			neutralFeedback++
		case "mixed":
			positiveFeedback++
			negativeFeedback++
		}

		if feedback.DateCreated < minDate {
			minDate = feedback.DateCreated
		}
		if feedback.DateCreated > maxDate {
			maxDate = feedback.DateCreated
		}
	}

	minTime := time.UnixMilli(minDate).Format("02 Jan")
	maxTime := time.UnixMilli(maxDate).Format("02 Jan")

	// Calculate percentages
	positivePercentage := float32(positiveFeedback) / float32(totalFeedback) * 100
	negativePercentage := float32(negativeFeedback) / float32(totalFeedback) * 100
	neutralPercentage := float32(neutralFeedback) / float32(totalFeedback) * 100

	// Generate report
	var report strings.Builder
	report.WriteString("#UNIQ REPORT\n")
	report.WriteString(fmt.Sprintf("Feedback Summary For The Last Week (%s - %s)\n", minTime, maxTime))
	report.WriteString(fmt.Sprintf("[%v]\n\n", strings.ToUpper(admin.BusinessName)))

	report.WriteString("*Overall Summary:*\n")
	report.WriteString(fmt.Sprintf("- Total Feedback Received: %d\n", totalFeedback))
	if totalFeedback > 0 {
		report.WriteString(fmt.Sprintf("- Average Sentiment Score: %.2f\n\n", calculateAvgSentimentScore(feedbacks)))
	} else {
		report.WriteString("- Average Sentiment Score: N/A\n\n")
	}

	report.WriteString("*Feedback Breakdown:*\n")
	report.WriteString(fmt.Sprintf("- Positive Feedback: %.2f%%\n", positivePercentage))
	report.WriteString(fmt.Sprintf("- Negative Feedback: %.2f%%\n", negativePercentage))
	report.WriteString(fmt.Sprintf("- Neutral Feedback: %.2f%%\n\n", neutralPercentage))

	//feedback analysis
	generatedReport, err := t.repoNlp.GenerateSentimentReport(ConvertToSentimentReportRequest(feedbacks))
	if !log.IfError(err) {
		report.WriteString("*Trend Insights:*\n")
		report.WriteString(generate.NumberingText(generatedReport.TrendsInsights, "*", "*"))
		report.WriteString("\n\n")

		report.WriteString("*Actionable Suggestions:*\n")
		report.WriteString(generate.NumberingText(generatedReport.ActionableSuggestions, "*", "*"))
		report.WriteString("\n\n")
	}

	if utils.GetCurrentMillis() < 1748710800000 {
		report.WriteString("💡Tips:\n Kamu bisa me-generate ringkasan _feedback_ secara manual melalui web\n\n")
	}

	report.WriteString(fmt.Sprintf("_list feedback: %v/crm/customer/complainfeedback_", os.Getenv("base_url")))
	return report.String()
}

func calculateAvgSentimentScore(feedbackList []models.SalesFeedbackEntity) float64 {
	sentimentScores := map[string]int{
		"positive": 1,
		"negative": -1,
		"neutral":  0,
	}

	totalScore := 0
	numItems := len(feedbackList)

	for _, item := range feedbackList {
		totalScore += sentimentScores[item.Sentiment]
	}

	if numItems > 0 {
		averageScore := float64(totalScore) / float64(numItems)
		return averageScore
	} else {
		return 0.0 // If there are no feedback items, return a neutral score
	}
}

func ConvertToSentimentReportRequest(feedbacks []models.SalesFeedbackEntity) []models.SentimentReportRequest {
	// Create a map to store data for each sentiment
	sentimentMap := make(map[string][]string)

	// Iterate over the feedbacks and group them by sentiment
	for _, feedback := range feedbacks {
		sentimentMap[feedback.Sentiment] = append(sentimentMap[feedback.Sentiment], feedback.Comment)
	}

	// Convert the map to a slice of SentimentReportRequest
	var result []models.SentimentReportRequest
	for sentiment, data := range sentimentMap {
		result = append(result, models.SentimentReportRequest{
			Sentiment: sentiment,
			Data:      data,
		})
	}

	return result
}

// say thanks and ask to give another feedback
// if customer has another transaction which not given feedback
func (t *transactionUseCase) askAnotherFeedback(salesId string) {
	feedback, err := t.repo.FetchSalesFeedback(salesId, domain.UserSession{})
	if log.IfError(err) || feedback.SalesFeedbackID == 0 {
		log.Info("no salesFeedback, or error, salesId: %v", salesId)
		return
	}
	//if no point, skip for now...
	if feedback.PointEarned == 0 {
		log.Info("no point earned for feedbackId: %v", feedback.SalesFeedbackID)
		return
	}

	sales, err := t.repo.FetchSales(feedback.SalesFkid)
	if log.IfError(err) {
		return
	}

	messageTitle := "Feedback Reminder"
	//check if user has got request before
	oldMessages, err := t.repoCampaign.FetchScheduleMessage(domain.ScheduleMessageFilter{
		Receiver:                sales.ReceiptReceiver,
		Title:                   messageTitle,
		TimeDeliver:             time.Now().Add(-12*time.Hour).Unix() * 1000,
		TimeDeliverOffsetMinute: 60 * 12,
	})
	log.IfError(err)
	if len(oldMessages) > 0 {
		log.Info("user %v has received request, id %v at %v", sales.ReceiptReceiver, oldMessages[0].ID, oldMessages[0].DataCreated)
		return
	}

	salesNoFeedback, err := t.repo.FetchSalesUnGivenFeedback(sales.ReceiptReceiver, 30)
	if log.IfError(err) {
		return
	}

	urls := make([]string, 0)
	for _, sale := range salesNoFeedback {
		log.Info("ungiven feedback detected... receiver: %v | id: %v", sale.ReceiptReceiver, sale.SalesID)

		schedMsgs, err := t.repoCampaign.FetchScheduleMessage(domain.ScheduleMessageFilter{
			Receiver:                sale.ReceiptReceiver,
			TimeDeliver:             sale.TimeCreated,
			TimeDeliverOffsetMinute: 5,
		})
		log.Info("FetchScheduleMessage, from %v, size: %v", sale.ReceiptReceiver, len(schedMsgs))
		if log.IfError(err) || len(schedMsgs) == 0 {
			continue
		}

		msg := schedMsgs[0].Message
		url := extract.Url(msg)
		log.Info("got %v msgs, url: %v | from message: \n%v", len(schedMsgs), url, msg)
		if len(url) > 0 {
			urls = append(urls, url...)
		}
	}

	if len(urls) == 0 {
		return
	}

	//send request
	message := feedbackMessage(sales.CustomerName, feedback.PointEarned, urls)
	t.repoCampaign.AddScheduleMessage(domain.ScheduleMessage{
		Title:    messageTitle,
		Message:  message,
		Receiver: sales.ReceiptReceiver,
	})
}

// FetchFeedbackSummary implements transaction.UseCase.
func (t *transactionUseCase) FetchFeedbackSummary(request domain.FeedbackSummaryRequest, user domain.UserSession) (models.FeedbackSummaryResponse, error) {
	//to wake up server
	go t.repoNlp.GetSentiment("saya suka membangunkan server")

	if request.StartDate <= 0 || request.EndDate <= 0 {
		log.Info("date empty... %v", utils.SimplyToJson(request))
		return models.FeedbackSummaryResponse{}, fmt.Errorf("date can't not be empty")
	}

	//trick for caching, because endDate will be using for key
	if request.EndDate > utils.GetCurrentMillis() {
		request.EndDate = utils.GetCurrentMillis()
	}

	//try to get from cache
	resultCache, err := t.repo.FetchFeedbackSummary(request, user)
	if !log.IfError(err) && len(resultCache.TrendsInsights) > 0 {
		log.Info("FetchFeedbackSummary from cache... ")
		return resultCache, err
	}

	//fetch feedback list
	filter := models.SalesFeedbackFilter{
		HasComment: true,
		StartDate:  request.StartDate,
		EndDate:    request.EndDate,
		AdminId:    (user.AdminId),
	}
	feedbacks, err := t.repo.FetchSalesFeedbackWithFilter(filter)
	log.Info("total feedback %v, with filter: %v", len(feedbacks), utils.SimplyToJson(filter))
	if log.IfError(err) || len(feedbacks) == 0 {
		return models.FeedbackSummaryResponse{}, err
	}

	feedbackMap := make(map[int]models.SalesFeedbackWithUser)

	//transform feedback data
	feedbackRequest := make([]models.FeedbackSummaryRequest, 0)
	for _, feedback := range feedbacks {
		feedbackRequest = append(feedbackRequest, models.FeedbackSummaryRequest{
			Id:   feedback.SalesFeedbackID,
			Text: feedback.Comment,
		})
		feedbackMap[feedback.SalesFeedbackID] = feedback
	}

	response, err := t.repoNlp.GenerateFeedbackSummary(feedbackRequest)
	log.IfError(err)
	for i, insight := range response.TrendsInsights {
		for j, data := range insight.Insights {
			feedbacks := make([]string, 0)
			for _, id := range data.Ids {
				feedbacks = append(feedbacks, feedbackMap[id].Comment)
			}
			response.TrendsInsights[i].Insights[j].Feedback = feedbacks
		}
	}

	go t.repo.AddFeedbackSummary(response, request, user)

	log.Info("finish, got total insights: %v", len(response.TrendsInsights))
	return response, err
}

// FetchFeedbackSummaryHitory implements transaction.UseCase.
func (t *transactionUseCase) FetchFeedbackSummaryHitory(user domain.UserSession) ([]models.FeedbackSummaryHistoryResponse, error) {
	history, err := t.repo.FetchFeedbackSummaryHitory(user)
	if log.IfError(err) {
		return history, err
	}

	timeOffset := 25200
	maxItem := 6

	logDate := make(map[string]bool) //to store added data
	result := make([]models.FeedbackSummaryHistoryResponse, 0)
	for i := range history {
		row := history[len(history)-1-i]
		key := fmt.Sprintf("%v-%v", utils.FormatTimeMillis(row.StartDate, timeOffset), utils.FormatTimeMillis(row.EndDate, timeOffset))
		if logDate[key] {
			continue
		}

		result = append(result, row)
		logDate[key] = true

		//only take max item
		if len(result) >= maxItem {
			break
		}
	}
	return result, err
}
