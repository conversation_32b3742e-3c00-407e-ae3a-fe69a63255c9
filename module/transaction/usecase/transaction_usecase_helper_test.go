package usecase

import (
	"fmt"
	"testing"
	"time"
)

func TestCutLongName(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"<PERSON>", "<PERSON>"},
		{"Soleh Adul Lutfi", "<PERSON><PERSON> A."},
		{"<PERSON>", "<PERSON>"},
		{"<PERSON>", "<PERSON>"},
		{"T<PERSON> <PERSON><PERSON>", "T<PERSON> Ka<PERSON>"},
		{"<PERSON>", "<PERSON>"},
		{"TA <PERSON>", "TA Ka <PERSON>eh A."},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			result := cutLongName(tc.input)
			if result != tc.expected {
				t.<PERSON>("cutLongName(%q) = %q; want %q", tc.input, result, tc.expected)
			}
		})
	}
}

func TestDate(t *testing.T) {
	dt := int64(1730091541347)
	minTime := time.Unix(dt, 0).Format("02 Jan")
	fmt.Println("date: ", minTime)

	minTime = time.UnixMilli(dt).Format("02 Jan")
	fmt.Println("date: ", minTime)
}
