package usecase

import (
	"testing"

	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

func Test_transactionUseCase_GenerateTransactionId(t *testing.T) {
	type fields struct {
		repo transaction.Repository
	}
	type args struct {
		outletId int
		adminId  int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{"2 digit", fields{}, args{29, 1}, "OM", false},
		{"2 digit (2)", fields{}, args{41, 1}, "HK", false},
		{"3 digit", fields{}, args{88, 39}, "NKI", false},
		{"4 digit", fields{}, args{603, 431}, "NMOH", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := transactionUseCase{
				repo: tt.fields.repo,
			}
			got, err := tr.GenerateTransactionId(tt.args.outletId, tt.args.adminId)
			if (err != nil) != tt.wantErr {
				t.Errorf("transactionUseCase.GenerateTransactionId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("transactionUseCase.GenerateTransactionId() = %v, want %v", got, tt.want)
			}
		})
	}
}
