package usecase

import (
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func cutLongName(name string) string {
	words := strings.Fields(name)
	var shortenedWords []string
	found := false

	for _, word := range words {
		// Check if the word is less than 4 characters
		if len(word) < 4 {
			// Preserve the word and any following words in the original name
			shortenedWords = append(shortenedWords, word)
			continue
		}

		// Shorten the word to its first character and add it to the result
		if !found {
			shortenedWords = append(shortenedWords, string(word))
		} else {
			shortenedWords = append(shortenedWords, string(word[0])+".")
			break
		}

		found = true
	}

	return strings.Join(shortenedWords, " ")
}

func (t *transactionUseCase) validateStock(orders []models.OrderList) (bool, map[string]interface{}) {
	// orders[0].ProductDetailID
	productDetailIds := make([]int, len(orders))
	for i, order := range orders {
		productDetailIds[i] = order.ProductDetailID
	}
	products, err := t.repoProduct.FetchProductByDetailIds(productDetailIds)
	log.IfError(err)
	log.Debug("products: %v", utils.SimplyToJson(products))

	productStockMap := make(map[int]int)
	for _, product := range products {
		productStockMap[cast.ToInt(product["product_detail_id"])] = cast.ToInt(product["stock_qty"])
	}

	insufficientStock := make(map[int]int)
	for _, order := range orders {
		if order.Qty > productStockMap[order.ProductDetailID] {
			insufficientStock[order.ProductDetailID] = productStockMap[order.ProductDetailID]
		}
	}

	if len(insufficientStock) > 0 {
		return false, map[string]interface{}{
			"error":   "Insufficient stock for some products",
			"details": insufficientStock,
		}
	}

	return true, nil
}

func (t *transactionUseCase) validatePromotion(deals models.TransactionPromo) error {
	if deals.PromotionFkid <= 0 {
		return nil
	}
	promo, err := t.repo.FetchPromotion(deals.PromotionFkid)
	log.IfError(err)

	if len(promo) == 0 {
		return fmt.Errorf("invalid promotion_fkid %v", deals.PromotionFkid)
	}

	maxRedeem := cast.ToInt(promo["maximum_redeem_period"])
	maxRedeemDay := cast.ToInt(promo["maximum_redeem_period_days"])
	log.Info("promoBuyId: %v | promoId: %v | maxRedeem: %v | maxRedeemDay: %v", deals.PromotionBuyId, deals.PromotionFkid, maxRedeem, maxRedeemDay)
	if maxRedeem > 0 && maxRedeemDay > 0 {
		totalUsed, err := t.repo.CountPromotionUsed(deals.PromotionFkid, maxRedeemDay)
		log.Info("promo: %v | totalUsed: %v", deals.PromotionFkid, totalUsed)
		log.IfError(err)

		if totalUsed >= maxRedeem {
			return fmt.Errorf("promotion reached the maximum limit period")
		}
	}

	return nil
}
