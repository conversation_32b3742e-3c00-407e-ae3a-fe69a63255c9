package transaction

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type UseCase interface {
	FetchTransactionSetting(outletId int, userSession domain.UserSession) (interface{}, error)
	FetchTransaction(orderId string, userSession domain.UserSession) (models.TransactionCreate, error)
	CreateTransaction(transaction models.TransactionCreate, user domain.UserSession) (map[string]interface{}, error)
	CreateFeedback(salesId string, feedback models.SalesFeedbackEntity, user domain.UserSession) error
	CreateTransactionPayment(param models.PaymentRequestParam, user domain.UserSession) (models.PaymentCreateResponse, error)
	FechPaymentMethod(param models.PaymentMethodRequestParam, user domain.UserSession) ([]models.PaymentMethod, error)
	FetchTransactionStatus(orderId string, user domain.UserSession) ([]models.OrderStatusEntity, error)

	FetchTransactionConfig(user domain.UserSession) (models.TransactionConfig, error)
	UpdateTransactionConfig(transactionConfig models.TransactionConfig, user domain.UserSession) error

	UpdateFeedbackSentiment(feedbackId ...int) error
	SetSentimentFeedback()
	SendFeedbackReport() error
	RunPaymentTimeout()
	FetchFeedbackSummary(request domain.FeedbackSummaryRequest, user domain.UserSession) (models.FeedbackSummaryResponse, error)
	FetchFeedbackSummaryHitory(user domain.UserSession) ([]models.FeedbackSummaryHistoryResponse, error)

	FetchRating(outletId int, user *domain.UserSession) (*models.RatingResponse, error)
	FetchReview(outletId int, user *domain.UserSession) (*[]models.ReviewResponse, error)

	//ai
	FetchTransactionSummary(domain.TransactionSummaryRequest) ([]map[string]interface{}, error)

	//handle messaging
	ReceiveFeedback(salesId string) error
}
