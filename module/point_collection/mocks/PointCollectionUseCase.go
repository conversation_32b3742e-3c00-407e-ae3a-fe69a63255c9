// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

// PointCollectionUseCase is an autogenerated mock type for the PointCollectionUseCase type
type PointCollectionUseCase struct {
	mock.Mock
}

// FetchPointCollection provides a mock function with given fields: filter, user
func (_m *PointCollectionUseCase) FetchPointCollection(filter domain.PointCollectionFilter, user domain.UserSession) ([]map[string]interface{}, error) {
	ret := _m.Called(filter, user)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.PointCollectionFilter, domain.UserSession) []map[string]interface{}); ok {
		r0 = rf(filter, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.PointCollectionFilter, domain.UserSession) error); ok {
		r1 = rf(filter, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewPointCollectionUseCase interface {
	mock.TestingT
	Cleanup(func())
}

// NewPointCollectionUseCase creates a new instance of PointCollectionUseCase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPointCollectionUseCase(t mockConstructorTestingTNewPointCollectionUseCase) *PointCollectionUseCase {
	mock := &PointCollectionUseCase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
