package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	pointcollection "gitlab.com/uniqdev/backend/api-membership/module/point_collection"
)

type pointCollectionHandler struct {
	uc pointcollection.PointCollectionUseCase
}

func NewHttpPointCollectionHandler(app *fasthttprouter.Router, useCase pointcollection.PointCollectionUseCase) {
	handler := &pointCollectionHandler{useCase}
	app.GET("/v1/point-collection", auth.<PERSON>idate<PERSON>ub<PERSON><PERSON>ey(handler.FetchPointCollection))
}

func (h pointCollectionHandler) FetchPointCollection(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	var filter domain.PointCollectionFilter

	filterMap := parser.ParseParamFastHttp(ctx)
	filterJson, err := json.Marshal(filterMap)
	log.IfError(err)
	log.IfError(json.Unmarshal(filterJson, &filter))

	result, err := h.uc.FetchPointCollection(filter, user)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}
