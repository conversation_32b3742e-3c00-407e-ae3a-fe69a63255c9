package mysql

import (
	"database/sql"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	pointcollection "gitlab.com/uniqdev/backend/api-membership/module/point_collection"
)

type pointCollectionRepository struct {
	db db.Repository
}

func NewMysqlPointCollectionRepository(conn *sql.DB) pointcollection.PointCollectionRepository {
	return &pointCollectionRepository{db.Repository{Conn: conn}}
}

func (p pointCollectionRepository) FetchPointCollection(filter domain.PointCollectionFilter, user domain.UserSession) ([]map[string]interface{}, error) {
	sql := `SELECT *, COALESCE(pc.point, pcdn.point, pcdp.point) as point, pc.member_type_fkid as member_type_id,
	COALESCE(pcdn.outlet_fkid, pcdp.outlet_fkid) as outlet_id,
	COALESCE(pcdn.repeat_point, pcdp.repeat_point) as repeat_point,
	pcdp.product_fkid as product_id, pcdp.variant_fkid as variant_id
	from point_collection pc 
	left join point_collection_detail_nominal pcdn on pcdn.point_collection_fkid=pc.point_collection_id
	left join point_collection_detail_product pcdp on pcdp.point_collection_fkid=pc.point_collection_id
	where pc.admin_fkid= @adminId
	and pc.data_status=1`

	if filter.Active == "1" {
		sql += ` and pc.date_start < UNIX_TIMESTAMP()*1000
		and pc.date_end > UNIX_TIMESTAMP()*1000
		and pc.time_start <= time(DATE_ADD(now(), interval 7 hour))
		and pc.time_end > time(DATE_ADD(now(), interval 7 hour)) `
	}
	if filter.MemberTypeId != "" {
		sql += ` and pc.member_type_fkid = @memberTypeId `
	}
	if filter.ParentPointType == "transaction" {
		sql += " and (pc.point_type = 'product' or pc.point_type='nominal') "
	}
	if filter.PointType != "" {
		sql += " and point_type = @pointType "
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":      user.AdminId,
		"memberTypeId": filter.MemberTypeId,
		"pointType":    filter.PointType,
	})

	return db.QueryArray(sql, params...)
}

func (p pointCollectionRepository) UpdateSalesFeedback(update models.SalesFeedbackUpdate) error {
	_, err := p.db.Update("sales_feedback", map[string]interface{}{
		"point_earned": update.PointEarned,
	}, "sales_feedback_id = ?", update.SalesFeedbackID)
	return err
}
