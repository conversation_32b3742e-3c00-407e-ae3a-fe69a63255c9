package usecase

import (
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
	pointcollection "gitlab.com/uniqdev/backend/api-membership/module/point_collection"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

type pointCollectionUseCase struct {
	repo         pointcollection.PointCollectionRepository
	repoUser     user.Repository
	repoCampaign campaign.CampaignRepository
}

func NewPointCollectionUseCase(repository pointcollection.PointCollectionRepository,
	repoUser user.Repository, repoCampaign campaign.CampaignRepository) pointcollection.PointCollectionUseCase {
	return &pointCollectionUseCase{repo: repository, repoUser: repoUser, repoCampaign: repoCampaign}
}

func (p pointCollectionUseCase) FetchPointCollection(filter domain.PointCollectionFilter, user domain.UserSession) ([]map[string]interface{}, error) {
	data, err := p.repo.FetchPointCollection(filter, user)
	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, 0)
	pointCollectionProduct := make([]map[string]interface{}, 0)

	for i, point := range data {

		if cast.ToString(point["point_type"]) == "product" {
			pointCollectionProduct = append(pointCollectionProduct, array.TakeOnly(point, "outlet_id", "product_id", "variant_id", "repeat_point", "point"))
		}

		if i == len(data)-1 || point["point_collection_id"] != data[i+1]["point_collection_id"] {
			daysActive := make([]string, 0)
			cols := []string{"day_sunday", "day_monday", "day_tuesday", "day_wednesday", "day_thursday", "day_friday", "day_saturday"}
			for _, col := range cols {
				if fmt.Sprintf("%v", point[col]) == "1" {
					daysActive = append(daysActive, strings.Replace(cast.ToString(col), "day_", "", 1))
				}
			}

			timeActive := array.TakeOnly(point, "date_start", "date_end", "time_start", "time_end")
			timeActive["day_active"] = daysActive

			pointRow := array.TakeOnly(point, "name", "point_type", "member_type_id", "min_transaction", "point")
			pointRow["time_active"] = timeActive
			pointRow["point_type_product"] = pointCollectionProduct

			if cast.ToString(point["point_type"]) == "nominal" {
				pointNominal := array.TakeOnly(point, "outlet_id", "transaction", "repeat_point")
				pointRow["point_type_nominal"] = pointNominal
			}

			result = append(result, pointRow)

			//reset necessary variable
			pointCollectionProduct = make([]map[string]interface{}, 0)
		}
	}

	return result, nil
}
