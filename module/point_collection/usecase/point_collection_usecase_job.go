package usecase

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func (p *pointCollectionUseCase) RunPointCollection(memberId int64, adminId int64, action string, feedbackId int) error {
	//fetch member detail
	members, err := p.repoUser.FetchMember(domain.UserSession{MemberId: memberId, AdminId: adminId})
	if log.IfError(err) {
		return err
	}

	if len(members) == 0 {
		log.Info("can not run point collection, member not found: memberId: %d, adminId: %d", memberId, adminId)
		return fmt.Errorf("member not found")
	}

	member := members[0]

	if action == domain.PointCollectionActionFeedback {
		pointCollections, err := p.repo.FetchPointCollection(domain.PointCollectionFilter{
			MemberTypeId: cast.ToString(member["type_fkid"]),
			PointType:    action,
			Active:       "1",
		}, domain.UserSession{MemberId: memberId, AdminId: adminId})
		log.IfError(err)

		if len(pointCollections) == 0 {
			log.Info("no point collection for : %v | at : %v", action, adminId)
			return nil
		}

		totalNewPoint := 0
		pointColId := make([]string, 0)
		for _, point := range pointCollections {
			totalNewPoint += cast.ToInt(point["point"])
			pointColId = append(pointColId, cast.ToString(point["point_collection_id"]))
		}

		log.Info("total new point for %v (%v) : %d --> %v", memberId, adminId, totalNewPoint, strings.Join(pointColId, ","))
		if totalNewPoint <= 0 {
			return nil
		}

		err = p.repoUser.AddUserPoint(totalNewPoint, domain.UserSession{MemberId: memberId, AdminId: adminId})
		if log.IfError(err) {
			return err
		}

		//notify member
		p.sendNotification(domain.UserSession{MemberId: memberId, AdminId: adminId})

		//update in feedback
		err = p.repo.UpdateSalesFeedback(models.SalesFeedbackUpdate{SalesFeedbackEntity: models.SalesFeedbackEntity{SalesFeedbackID: feedbackId, PointEarned: totalNewPoint}, MemberId: memberId})
		log.IfError(err)
	}
	return nil
}

func (p *pointCollectionUseCase) sendNotification(user domain.UserSession) {
	tokens, err := p.repoUser.FetchNotificationToken(user.MemberId, user.AdminId)
	log.IfError(err)
	if len(*tokens) == 0 {
		log.Info("user has no token: %v | %v", user.MemberId, user.AdminId)
		return
	}

	scheduledMessage := make([]domain.ScheduleMessage, 0)
	for _, token := range *tokens {
		scheduledMessage = append(scheduledMessage, domain.ScheduleMessage{
			Title:       "Thank You!",
			Message:     "Terima kasih untuk Feedbacknya!",
			TimeDeliver: time.Now().Unix() * 1000,
			DataCreated: time.Now().Unix() * 1000,
			Media:       "push_notif",
			Receiver:    token.Token,
			MessageDetail: utils.SimplyToJson(domain.NotificationDetail{
				NotificationType: "point",
			}),
		})
	}

	log.Info("send-notif-feedback: %v", len(scheduledMessage))
	err = p.repoCampaign.AddScheduleMessage(scheduledMessage...)
	log.IfError(err)
}
