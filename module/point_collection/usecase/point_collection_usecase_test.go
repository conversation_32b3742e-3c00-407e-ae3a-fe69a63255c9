package usecase

import (
	"encoding/json"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	pointcollection "gitlab.com/uniqdev/backend/api-membership/module/point_collection"
	"gitlab.com/uniqdev/backend/api-membership/module/point_collection/mocks"
)

func Test_pointCollectionUseCase_FetchPointCollection(t *testing.T) {
	mockRepo := new(mocks.PointCollectionRepository)

	pointCollectionJson := `[{"point_collection_id":113,"name":"ForAll","point_type":"nominal","point":null,"member_type_fkid":143,"min_transaction":0,"date_start":1618444800000,"date_end":1619049600000,"time_start":"00:00:00","time_end":"23:59:00","day_sunday":1,"day_monday":1,"day_tuesday":1,"day_wednesday":1,"day_thursday":1,"day_friday":1,"day_saturday":1,"admin_fkid":1,"data_created":1618474295608,"data_modified":1618474295608,"data_status":1,"id":301,"point_collection_fkid":113,"outlet_fkid":364,"transaction":1,"point":10,"repeat_point":1,"admin_fkid":1,"data_created":1618474295611,"data_modified":1618474295611,"is_active":1,"id":null,"point_collection_fkid":null,"outlet_fkid":null,"product_fkid":null,"variant_fkid":null,"point":null,"repeat_point":null,"admin_fkid":null,"data_created":null,"data_modified":null,"is_active":null,"point_main":null,"point_nominal":10,"point_product":null,"point":10,"outlet_id":364,"repeat_point":1},{"point_collection_id":115,"name":"Testingnominalpointcollection","point_type":"nominal","point":null,"member_type_fkid":143,"min_transaction":1,"date_start":1673827200000,"date_end":1674432000000,"time_start":"00:00:00","time_end":"23:59:00","day_sunday":1,"day_monday":1,"day_tuesday":1,"day_wednesday":1,"day_thursday":1,"day_friday":1,"day_saturday":1,"admin_fkid":1,"data_created":1673853848833,"data_modified":1673853848833,"data_status":1,"id":328,"point_collection_fkid":115,"outlet_fkid":29,"transaction":1000,"point":10,"repeat_point":1,"admin_fkid":1,"data_created":1673853848835,"data_modified":1673853848835,"is_active":1,"id":null,"point_collection_fkid":null,"outlet_fkid":null,"product_fkid":null,"variant_fkid":null,"point":null,"repeat_point":null,"admin_fkid":null,"data_created":null,"data_modified":null,"is_active":null,"point_main":null,"point_nominal":10,"point_product":null,"point":10,"outlet_id":29,"repeat_point":1},{"point_collection_id":116,"name":"Testingproductpointcollection","point_type":"product","point":null,"member_type_fkid":143,"min_transaction":10,"date_start":1673823600000,"date_end":1674428400000,"time_start":"00:00:00","time_end":"23:59:00","day_sunday":1,"day_monday":1,"day_tuesday":1,"day_wednesday":1,"day_thursday":1,"day_friday":1,"day_saturday":1,"admin_fkid":1,"data_created":1673843270128,"data_modified":1673843270128,"data_status":1,"id":null,"point_collection_fkid":null,"outlet_fkid":null,"transaction":null,"point":null,"repeat_point":null,"admin_fkid":null,"data_created":null,"data_modified":null,"is_active":null,"id":590,"point_collection_fkid":116,"outlet_fkid":29,"product_fkid":6743,"variant_fkid":null,"point":10,"repeat_point":1,"admin_fkid":1,"data_created":1673843270370,"data_modified":1673843270370,"is_active":1,"point_main":null,"point_nominal":null,"point_product":10,"point":10,"outlet_id":29,"repeat_point":1},{"point_collection_id":116,"name":"Testingproductpointcollection","point_type":"product","point":null,"member_type_fkid":143,"min_transaction":10,"date_start":1673823600000,"date_end":1674428400000,"time_start":"00:00:00","time_end":"23:59:00","day_sunday":1,"day_monday":1,"day_tuesday":1,"day_wednesday":1,"day_thursday":1,"day_friday":1,"day_saturday":1,"admin_fkid":1,"data_created":1673843270128,"data_modified":1673843270128,"data_status":1,"id":null,"point_collection_fkid":null,"outlet_fkid":null,"transaction":null,"point":null,"repeat_point":null,"admin_fkid":null,"data_created":null,"data_modified":null,"is_active":null,"id":591,"point_collection_fkid":116,"outlet_fkid":29,"product_fkid":2949,"variant_fkid":null,"point":10,"repeat_point":1,"admin_fkid":1,"data_created":1673843270370,"data_modified":1673843270370,"is_active":1,"point_main":null,"point_nominal":null,"point_product":10,"point":10,"outlet_id":29,"repeat_point":1},{"point_collection_id":117,"name":"Testingregisterpointcollection","point_type":"register","point":100,"member_type_fkid":143,"min_transaction":0,"date_start":1674518400000,"date_end":1677542400000,"time_start":"00:00:00","time_end":"23:59:00","day_sunday":1,"day_monday":1,"day_tuesday":1,"day_wednesday":1,"day_thursday":1,"day_friday":1,"day_saturday":1,"admin_fkid":1,"data_created":1674535677503,"data_modified":1674535677503,"data_status":1,"id":null,"point_collection_fkid":null,"outlet_fkid":null,"transaction":null,"point":null,"repeat_point":null,"admin_fkid":null,"data_created":null,"data_modified":null,"is_active":null,"id":null,"point_collection_fkid":null,"outlet_fkid":null,"product_fkid":null,"variant_fkid":null,"point":null,"repeat_point":null,"admin_fkid":null,"data_created":null,"data_modified":null,"is_active":null,"point_main":100,"point_nominal":null,"point_product":null,"point":100,"outlet_id":null,"repeat_point":null}]`
	var pointCollectionMap []map[string]interface{}
	err := json.Unmarshal([]byte(pointCollectionJson), &pointCollectionMap)
	assert.NoError(t, err)

	type fields struct {
		repo pointcollection.PointCollectionRepository
	}
	type args struct {
		filter domain.PointCollectionFilter
		user   domain.UserSession
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []map[string]interface{}
		wantErr bool
	}{
		{"test1", fields{repo: mockRepo}, args{user: domain.UserSession{AdminId: 1}}, []map[string]interface{}{}, false},
	}

	mockRepo.On("FetchPointCollection", mock.Anything, mock.Anything).Return(pointCollectionMap, nil)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := pointCollectionUseCase{
				repo: tt.fields.repo,
			}
			got, err := p.FetchPointCollection(tt.args.filter, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("pointCollectionUseCase.FetchPointCollection() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("pointCollectionUseCase.FetchPointCollection() = %v, want %v", got, tt.want)
			}
		})
	}
}
