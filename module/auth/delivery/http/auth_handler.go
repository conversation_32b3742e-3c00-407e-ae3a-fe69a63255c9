package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type authHandler struct {
	uc domain.AuthUseCase
}

func NewHttpAuthHandler(app *fasthttprouter.Router, useCase domain.AuthUseCase) {
	handler := &authHandler{useCase}
	app.POST("/v1/auth/token/apple", handler.LoginWithSSO)
	app.POST("/v1/auth/send-auth-link", auth.ValidatePublicKey(handler.SendAuthLink))

	app.GET("/v1/auth/logout", auth.ValidateToken(handler.Logout))
	app.POST("/v1/auth/send-otp", auth.ValidatePublicKey(handler.SendOtp))
	app.POST("/v1/auth/otp", auth.ValidatePublicKey(handler.ValidateOtp))
	app.GET("/v1/auth/oob-whatsapp", auth.ValidatePublicKey(handler.OobWhatsApp))
	app.GET("/v1/auth/oob-whatsapp/:token", auth.ValidatePublicKey(handler.CheckOobWhatsApp))
}

func (h *authHandler) LoginWithSSO(ctx *fasthttp.RequestCtx) {
	ctx.SetContentType("application/json")
	authToken := ctx.Request.Header.Peek("Authorization")
	idToken := ctx.PostArgs().Peek("id_token")
	pubKey := ctx.Request.Header.Peek("Public-Key")
	adminId := utils.ToInt(utils.Decrypt(string(pubKey), utils.UID_PUBKEY))

	h.uc.LoginWithSSO(string(authToken), string(idToken), adminId)
}

func (h *authHandler) SendAuthLink(ctx *fasthttp.RequestCtx) {
	contact := ctx.Request.PostArgs().Peek("contact")
	media := ctx.Request.PostArgs().Peek("media")

	data, err := h.uc.SendAuthLink(string(contact), string(media), domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "link sent", Data: data})
}

func (h *authHandler) Logout(ctx *fasthttp.RequestCtx) {
	err := h.uc.Logout(domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "logout successfullt"})
}

func (h *authHandler) SendOtp(ctx *fasthttp.RequestCtx) {
	param := models.OtpRequestInput{
		Contact:     string(ctx.PostArgs().Peek("contact")),
		ContactType: "whatsapp",
	}
	data, err := h.uc.SendOtp(&param, domain.UserSessionFastHttp(ctx))
	if err != nil {
		if code, ok := err.(exception.WithCode); ok {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: code.Message, Code: code.Code})
			return
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "otp sent", Data: data})
}

// handler to validate otp
func (h *authHandler) ValidateOtp(ctx *fasthttp.RequestCtx) {
	token := string(ctx.PostArgs().Peek("token"))
	code := string(ctx.PostArgs().Peek("code"))
	data, err := h.uc.ValidateOtp(token, code, domain.UserSessionFastHttp(ctx))
	if err != nil {
		if code, ok := err.(exception.WithCode); ok {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: code.Message, Code: code.Code})
			return
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func (h *authHandler) OobWhatsApp(ctx *fasthttp.RequestCtx) {
	phone := string(ctx.QueryArgs().Peek("phone"))
	result, err := h.uc.OobWhatsApp(phone, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *authHandler) CheckOobWhatsApp(ctx *fasthttp.RequestCtx) {
	token := ctx.UserValue("token").(string)
	result, err := h.uc.CheckOobWhatsApp(token, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}
