package usecase

import (
	"context"
	"fmt"

	"github.com/Timothylock/go-signin-with-apple/apple"
	"github.com/golang-jwt/jwt"
	"gitlab.com/uniqdev/backend/api-membership/config/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
)

type AuthSSOResult struct {
	Email string
}

type AuthSSO interface {
	ValidateToken(token string) (AuthSSOResult, error)
}

type AuthWithApple struct{}

func (a AuthWithApple) ValidateToken(token string) (AuthSSOResult, error) {
	cfg := auth.GetAppleConfig()
	teamId := cfg.TeamId
	clientId := cfg.ClientId
	keyId := cfg.KeyId
	keySecret := cfg.KeySecret
	fmt.Println(cast.ToString(cfg))

	secret, err := apple.GenerateClientSecret(keySecret, teamId, clientId, keyId)
	if log.IfError(err) {
		return AuthSSOResult{}, err
	}

	vReq := apple.AppValidationTokenRequest{
		ClientID:     clientId,
		ClientSecret: secret,
		Code:         token,
	}

	client := apple.New()
	var resp apple.AppValidationTokenRequest
	err = client.VerifyAppToken(context.Background(), vReq, &resp)
	if err != nil {
		fmt.Println("VerifyAppToken: ", err)
		return AuthSSOResult{}, err
	}

	tokenJwt, err := jwt.Parse(token, func(t *jwt.Token) (interface{}, error) {
		return "", nil
	})
	log.IfError(err)
	if claim, ok := tokenJwt.Claims.(jwt.MapClaims); ok {
		fmt.Println("claim", cast.ToString(claim))
		email := cast.ToString(claim["email"])
		if email == "" {
			log.IfError(fmt.Errorf("failed to get email from sign in with apple"))
		}
		return AuthSSOResult{Email: email}, nil
	}

	fmt.Println("resp: ", cast.ToString(resp))
	unique, _ := apple.GetUniqueID(resp.Code)

	// Voila!
	fmt.Println("unique: ", unique)
	return AuthSSOResult{}, nil
}
