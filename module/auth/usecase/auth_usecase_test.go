package usecase

import (
	"fmt"
	"strings"
	"testing"

	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/limiter"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func Test_generateVerificationLink(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-crm/.env") //Load .env file
	// os.Setenv("server", "staging")
	// firebase.SetupFirebaseConfig("/Users/<USER>/Documents/WORK/api-crm/config/auth/uniq-crm-production-firebase.json")
	firebase.SetupFirebaseConfig("/Users/<USER>/Documents/WORK/api-crm/config/auth/uniq-crm-firebase.json")

	type args struct {
		email string
	}

	tests := []struct {
		name    string
		arg     args
		want    string
		wantErr bool
	}{
		{"test1", args{"<EMAIL>"}, "https://", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := generateVerificationLink(tt.arg.email, domain.CrmAppInfo{
				Ios: domain.Ios{
					BundleID:   "id.uniq.crm.yamiepanda",
					AppStoreId: "",
				},
				Android: domain.Android{
					PackageName: "id.uniq.crm",
				},
				Web: domain.Web{
					Url: "https://uniq-crm-dev.web.app/",
				},
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("generateVerificationLink() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(">>", got)
			if !strings.HasPrefix(got, "https") {
				t.Errorf("generateVerificationLink() = '%v', want %v", got, tt.want)
			}
		})
	}
}

func Test_authUseCase_createFirebaseToken(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-crm/.env")
	firebase.SetupFirebaseConfig("/Users/<USER>/Documents/WORK/api-crm/config/auth/uniq-crm-firebase-adminsdk.json")

	type fields struct {
		repo        domain.AuthRepository
		repoApp     domain.AppRepository
		rateLimiter limiter.RateLimiter
	}
	type args struct {
		phone string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *models.AuthResponse
		wantErr bool
	}{
		{"exist", fields{}, args{phone: "+6285742257881"}, nil, false},
		{"not-exist", fields{}, args{phone: "085742257883"}, nil, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &authUseCase{
				repo:        tt.fields.repo,
				repoApp:     tt.fields.repoApp,
				rateLimiter: tt.fields.rateLimiter,
			}
			got, err := a.createFirebaseToken(tt.args.phone)
			if (err != nil) != tt.wantErr {
				t.Errorf("authUseCase.createFirebaseToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Token.Token == "" {
				t.Errorf("authUseCase.createFirebaseToken() = %v, want %v", got, tt.want)
			}
		})
	}
}
