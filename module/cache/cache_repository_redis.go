package cache

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
)

var ctx = context.Background()

type cacheDb struct {
	client *redis.Client
}

func NewCacheDbRedis(client *redis.Client) domain.CacheInterface {
	return &cacheDb{client: client}
}

// Delete implements domain.CacheInterface.
func (c *cacheDb) Delete(key string) error {
	return c.client.Del(ctx, key).Err()
}

// Get implements domain.CacheInterface.
func (c *cacheDb) Get(key string) (string, error) {
	timeStart := time.Now()
	ctx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()
	rcmd := c.client.Get(ctx, key)
	if rcmd.Err() != nil {
		return "", rcmd.Err()
	}
	result, err := rcmd.Result()
	if err != nil {
		fmt.Printf("GetCacheErr '%v' Err: %v\n", utils.Substr(key, 50), err)
	}
	if el := time.Since(timeStart); el.Seconds() > 10 {
		log.IfError(fmt.Errorf("get cache %v took %v", key, el))
	}
	return result, err
}

// GetBytes implements domain.CacheInterface.
func (c *cacheDb) GetBytes(key string) ([]byte, error) {
	ctx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()
	return c.client.Get(ctx, key).Bytes()
}

// GetMatching implements domain.CacheInterface.
func (c *cacheDb) GetMatching(keyMatch string) (map[string]string, error) {
	result := make(map[string]string)
	// Iterate using SCAN with a pattern
	iterator := c.client.Scan(ctx, 0, keyMatch, 10).Iterator()

	for iterator.Next(ctx) {
		key := iterator.Val()
		// Get the value for the matched key
		value, err := c.client.Get(ctx, key).Result()
		if err != nil {
			fmt.Println("Error getting value:", err)
		} else {
			result[key] = value
		}
	}

	if iterator.Err() != nil {
		fmt.Println("Error iterating:", iterator.Err())
	}
	return result, iterator.Err()
}

// Set implements domain.CacheInterface.
func (c *cacheDb) Set(key string, value interface{}, expiration time.Duration) error {
	timeStart := time.Now()
	err := c.client.Set(ctx, key, value, expiration).Err()
	if err != nil {
		fmt.Printf("set cache %v err %v\n", key, err)
		return err
	}
	if el := time.Since(timeStart); el.Seconds() > 10 {
		log.IfError(fmt.Errorf("set cache %v took %v", key, el))
	}
	return err
}
