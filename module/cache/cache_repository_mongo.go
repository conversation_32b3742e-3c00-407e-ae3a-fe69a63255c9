package cache

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
)

type cacheMongo struct {
	collection *mongo.Collection
	db         *mongo.Database
}

// NewCacheDbMongo creates a new instance of the MongoDB cache
func NewCacheDbMongo(client *mongo.Client) domain.CacheInterface {
	if client == nil {
		log.Info("cache mongo clien not init")
		return &cacheMongo{}
	}
	db := client.Database("cache")
	collection := db.Collection("cache_data")

	// Create a TTL index for the 'createdAt' field (1 hour expiration)
	indexModel := mongo.IndexModel{
		Keys:    bson.M{"expiresAt": 1},
		Options: options.Index().SetExpireAfterSeconds(0),
	}
	_, err := collection.Indexes().CreateOne(context.Background(), indexModel)
	if log.IfError(err) {
		return &cacheMongo{}
	}

	return &cacheMongo{collection: collection, db: db}
}

// getFromCache is a helper function that retrieves a value from cache by key
func (c *cacheMongo) getFromCache(key string) (bson.M, error) {
	if c.collection == nil {
		return nil, fmt.Errorf("mongo client not initialized")
	}
	var result bson.M
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	err := c.collection.FindOne(ctx, bson.M{"key": key}).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("key not found: %s", key)
		}
		return nil, fmt.Errorf("error fetching key: %w", err)
	}
	return result, nil
}

// Get retrieves a cached value by key
func (c *cacheMongo) Get(key string) (string, error) {
	result, err := c.getFromCache(key)
	if err != nil {
		return "", err
	}
	return result["value"].(string), nil
}

// GetBytes implements domain.CacheInterface.
func (c *cacheMongo) GetBytes(key string) ([]byte, error) {
	result, err := c.getFromCache(key)
	if err != nil {
		return nil, err
	}
	resultBytes, err := bson.Marshal(result["value"])
	if err != nil {
		return nil, fmt.Errorf("error marshalling value: %w", err)
	}
	return resultBytes, nil
}

// GetMatching retrieves cached values matching a key pattern
func (c *cacheMongo) GetMatching(keyMatch string) (map[string]string, error) {
	if c.collection == nil {
		return nil, fmt.Errorf("mongo client not initialized")
	}
	// var results []bson.M
	// regex := regexp.MustCompile(keyMatch)
	// bson.M{"$regex": regex.String()}
	cursor, err := c.collection.Find(context.Background(), bson.M{"key": primitive.Regex{Pattern: keyMatch, Options: "i"}})
	if err != nil {
		return nil, fmt.Errorf("error fetching matching keys: %w", err)
	}
	defer cursor.Close(context.Background())

	matchingValues := make(map[string]string)
	for cursor.Next(context.Background()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("error decoding result: %w", err)
		}
		key := result["key"].(string)
		value := result["value"].(string)
		matchingValues[key] = value
	}

	return matchingValues, nil
}

// Set stores a value in the cache with an expiration time
// func (c *cacheMongo) Set(key string, value interface{}, expiration time.Duration) error {
// 	if c.collection == nil {
// 		return fmt.Errorf("mongo client not initialized")
// 	}
// 	c.Delete(key)

// 	createdAt := time.Now()
// 	_, err := c.collection.InsertOne(context.Background(), bson.M{
// 		"key":       key,
// 		"value":     fmt.Sprintf("%v", value),
// 		"createdAt": createdAt,
// 		"expiresAt": createdAt.Add(expiration),
// 	})
// 	if err != nil {
// 		return fmt.Errorf("error setting key: %w", err)
// 	}
// 	return nil
// }

// Set stores a value in the cache with an expiration time, using a transaction
func (c *cacheMongo) Set(key string, value interface{}, expiration time.Duration) error {
	createdAt := time.Now()
	expiresAt := createdAt.Add(expiration)

	// Set the write concern and read concern for the transaction
	wc := writeconcern.New(writeconcern.WMajority())
	rc := readconcern.Snapshot()
	txnOpts := options.Transaction().SetWriteConcern(wc).SetReadConcern(rc)

	if c.db == nil {
		return fmt.Errorf("mongo client has not initialized")
	}

	// Start a transaction
	session, err := c.db.Client().StartSession()
	if err != nil {
		return fmt.Errorf("error starting transaction: %w", err)
	}
	defer session.EndSession(context.Background())

	// err = session.StartTransaction(options.Transaction().SetWriteConcern(options.WriteConcern{W: "majority"}))
	// if err != nil {
	// 	return fmt.Errorf("error starting transaction: %w", err)
	// }

	err = mongo.WithSession(context.Background(), session, func(sessContext mongo.SessionContext) error {
		if err = session.StartTransaction(txnOpts); err != nil {
			return err
		}

		// Delete any existing document with the same key
		_, err = c.collection.DeleteOne(sessContext, bson.M{"key": key})
		if err != nil {
			return fmt.Errorf("error deleting existing key: %w", err)
		}

		// Insert the new document with the updated value
		_, err = c.collection.InsertOne(sessContext, bson.M{
			"key":       key,
			"value":     fmt.Sprintf("%v", value),
			"createdAt": createdAt,
			"expiresAt": expiresAt,
		})
		if err != nil {
			return fmt.Errorf("error inserting new key: %w", err)
		}

		// Commit the transaction
		err = session.CommitTransaction(sessContext)
		if err != nil {
			return fmt.Errorf("error committing transaction: %w", err)
		}

		return nil
	})

	if err != nil {
		fmt.Println("save transaction cache mongo failed ", err)
		session.AbortTransaction(context.Background())
	}
	return err
}

// Delete removes a cached value by key
func (c *cacheMongo) Delete(key string) error {
	if c.collection == nil {
		return fmt.Errorf("mongo client not initialized")
	}
	_, err := c.collection.DeleteOne(context.Background(), bson.M{"key": key})
	if err != nil {
		return fmt.Errorf("error deleting key: %w", err)
	}
	return nil
}
