package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strings"

	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
)

type paymentUseCase struct {
	repo             domain.PaymentRepository
	usecasePromotion promotion.UseCase
}

func NewPaymentUseCase(repo domain.PaymentRepository, usecasePromotion promotion.UseCase) domain.PaymentUseCase {
	uc := &paymentUseCase{
		repo:             repo,
		usecasePromotion: usecasePromotion,
	}
	go google.Subscribe(fmt.Sprintf("billing_webhook_%s-api-crm-pull", os.Getenv("ENV")), uc.SubscribePayment)
	return uc
}

func (p *paymentUseCase) SubscribePayment(data []byte) bool {
	log.Info("Received billing webhook: %s\n", string(data))

	//handle from pubsub
	// var pubsub models.PubSub
	// err := json.NewDecoder(bytes.NewReader(data)).Decode(&pubsub)
	// if log.IfError(err) {
	// 	return false
	// }

	// dataDecoded, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	// if log.IfError(err) {
	// 	return false
	// }

	// log.Info("pubsub receive data: %s", string(dataDecoded))
	var payload map[string]interface{}
	if log.IfError(json.Unmarshal(data, &payload)) {
		return false
	}

	log.Info("receive from pubsub, payload: %v", utils.SimplyToJson(payload))
	fmt.Println("payload data type: ", reflect.TypeOf(payload["payload"]))

	paymentData := getTransactionDetail(payload)
	log.Info("order id: '%s' | transId: %v | status: %v", paymentData.OrderId, paymentData.TransactionId, paymentData.Status)

	if paymentData.OrderId != "" {
		go v1.HandlePaymentChange(paymentData.OrderId)
	}

	if utils.IsNumber(paymentData.OrderId) {
		go p.usecasePromotion.ReceivePaymentUpdate(cast.ToInt(paymentData.OrderId), paymentData.TransactionId, paymentData.Status)
	}
	return true
}

func getTransactionDetail(payload map[string]interface{}) models.PaymentWebhookPayload {
	if cast.ToString(payload["payment_gateway"]) == "xendit" {
		var paymentXendit models.PaymentWebhookXendit
		payloadData, err := json.Marshal(payload["payload"])
		log.IfError(err)
		fmt.Println("payload data --> ", string(payloadData))
		log.IfError(json.Unmarshal([]byte(payloadData), &paymentXendit))
		return paymentXendit.ToPayload()
	}

	if payloadData, ok := payload["payload"].(map[string]interface{}); ok {
		return models.PaymentWebhookPayload{
			TransactionId: strings.TrimSpace(cast.ToString(payloadData["transaction_id"])),
			OrderId:       strings.TrimSpace(cast.ToString(payloadData["order_id"])),
			Status:        cast.ToString(payloadData["transaction_status"]),
		}
	}
	return models.PaymentWebhookPayload{}
}
