package http

import (
	"bytes"
	"encoding/base64"
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type paymentHandler struct {
	usecase domain.PaymentUseCase
}

func NewHttpPaymentHandler(app *fasthttprouter.Router,
	useCase domain.PaymentUseCase) {
	handler := &paymentHandler{
		usecase: useCase,
	}

	//webhook
	app.POST("/payment/notification", handler.WebhookPayment)
}

func (h paymentHandler) WebhookPayment(ctx *fasthttp.RequestCtx) {
	log.Info("receive payment pubsub (webhook): %s", string(ctx.PostBody()))
	//handle from pubsub
	var pubsub models.PubSub
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&pubsub)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	dataDecoded, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	if h.usecase.SubscribePayment([]byte(dataDecoded)) {
		log.Info("pubsub receive data: %s", string(dataDecoded))
		log.Info("payment pubsub success")
		ctx.SetStatusCode(fasthttp.StatusOK)
		return
	}
	log.Error("payment pubsub failed")
	ctx.SetStatusCode(fasthttp.StatusInternalServerError)
}
