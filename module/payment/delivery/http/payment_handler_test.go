package http

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	"gitlab.com/uniqdev/backend/api-membership/models"
)

func Test_getTransactionDetail(t *testing.T) {
	payloadXenditJson := `{"payload":{"api_version":null,"business_id":"5fe30568fff3745477572973","created":"2023-04-11T04:33:19.287Z","data":{"actions":{"desktop_web_checkout_url":null,"mobile_deeplink_checkout_url":"https://ewallet-mock-connector.xendit.co/v1/ewallet_connector/checkouts?token=cgqe62di5chbk3dlifcg","mobile_web_checkout_url":null,"qr_checkout_string":"test-qr-string"},"basket":null,"callback_url":"https://api-billing.uniqdev.xyz/v1/webhook/payment/xendit","capture_amount":5000,"capture_now":true,"channel_code":"ID_SHOPEEPAY","channel_properties":{"success_redirect_url":"https://uniq.id"},"charge_amount":5000,"checkout_method":"ONE_TIME_PAYMENT","created":"2023-04-11T04:33:13.555764Z","currency":"IDR","customer":null,"customer_id":null,"failure_code":null,"id":"ewc_09b2b425-cd0f-4a42-844a-bced9d407a03","is_redirect_required":true,"metadata":null,"payer_charged_amount":null,"payer_charged_currency":null,"payment_method_id":null,"reference_id":"157029","refunded_amount":null,"shipping_information":null,"status":"SUCCEEDED","updated":"2023-04-11T04:33:19.096207Z","void_status":null,"voided_at":null},"event":"ewallet.capture"},"payment_gateway":"xendit"}`
	var payload map[string]interface{}
	err := json.Unmarshal([]byte(payloadXenditJson), &payload)
	if err != nil {
		t.Error(err)
	}

	xenditResult := models.PaymentWebhookPayload{
		TransactionId: "ewc_09b2b425-cd0f-4a42-844a-bced9d407a03",
		OrderId:       "157029",
		Status:        "SUCCEEDED",
	}

	type args struct {
		payload map[string]interface{}
	}
	tests := []struct {
		name string
		args args
		want models.PaymentWebhookPayload
	}{
		{"xendit", args{payload: payload}, xenditResult},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getTransactionDetail(tt.args.payload)
			fmt.Println("got --> ", got)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getTransactionDetail() = %v, want %v", got, tt.want)
			}

		})
	}
}
