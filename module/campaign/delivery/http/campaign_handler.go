package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
)

type campaignHandler struct {
	uc campaign.CampaignUseCase
}

func NewHttpCampaignHandler(app *fasthttprouter.Router, useCase campaign.CampaignUseCase) {
	handler := &campaignHandler{useCase}
	app.POST("/v1/campaign", auth.ValidateToken(handler.AddCampaign))
	app.DELETE("/v1/campaign/:id", auth.ValidateToken(handler.RemoveCampaign))
}

func (h campaignHandler) AddCampaign(ctx *fasthttp.RequestCtx) {
	var campaign domain.Campaign
	if err := json.Unmarshal(ctx.Request.Body(), &campaign); err != nil {
		log.Info("invalid campagin request: %v | %v", err.Error(), string(ctx.Request.Body()))
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	result, err := h.uc.AddCampaign(campaign, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "campaign saved successfully", Data: result})
}

func (h campaignHandler) RemoveCampaign(ctx *fasthttp.RequestCtx) {
	id := cast.ToInt(ctx.UserValue("id"))
	err := h.uc.RemoveCampaign(id, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "campaign removed successfully"})
}
