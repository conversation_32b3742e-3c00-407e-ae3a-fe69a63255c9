// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

// CampaignUseCase is an autogenerated mock type for the CampaignUseCase type
type CampaignUseCase struct {
	mock.Mock
}

// AddCampaign provides a mock function with given fields: _a0, user
func (_m *CampaignUseCase) AddCampaign(_a0 domain.Campaign, user domain.UserSession) (map[string]interface{}, error) {
	ret := _m.Called(_a0, user)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.Campaign, domain.UserSession) map[string]interface{}); ok {
		r0 = rf(_a0, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.Campaign, domain.UserSession) error); ok {
		r1 = rf(_a0, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RemoveCampaign provides a mock function with given fields: id, user
func (_m *CampaignUseCase) RemoveCampaign(id int, user domain.UserSession) error {
	ret := _m.Called(id, user)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) error); ok {
		r0 = rf(id, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Test provides a mock function with given fields:
func (_m *CampaignUseCase) Test() {
	_m.Called()
}

type mockConstructorTestingTNewCampaignUseCase interface {
	mock.TestingT
	Cleanup(func())
}

// NewCampaignUseCase creates a new instance of CampaignUseCase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCampaignUseCase(t mockConstructorTestingTNewCampaignUseCase) *CampaignUseCase {
	mock := &CampaignUseCase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
