// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

// CampaignRepository is an autogenerated mock type for the CampaignRepository type
type CampaignRepository struct {
	mock.Mock
}

// AddCampaign provides a mock function with given fields: _a0, user
func (_m *CampaignRepository) AddCampaign(_a0 domain.Campaign, user domain.UserSession) (int64, error) {
	ret := _m.Called(_a0, user)

	var r0 int64
	if rf, ok := ret.Get(0).(func(domain.Campaign, domain.UserSession) int64); ok {
		r0 = rf(_a0, user)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.Campaign, domain.UserSession) error); ok {
		r1 = rf(_a0, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddScheduleMessage provides a mock function with given fields: message
func (_m *CampaignRepository) AddScheduleMessage(message ...domain.ScheduleMessage) error {
	_va := make([]interface{}, len(message))
	for _i := range message {
		_va[_i] = message[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 error
	if rf, ok := ret.Get(0).(func(...domain.ScheduleMessage) error); ok {
		r0 = rf(message...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FetchMember provides a mock function with given fields: adminId, memberId
func (_m *CampaignRepository) FetchMember(adminId int, memberId ...int) ([]map[string]interface{}, error) {
	_va := make([]interface{}, len(memberId))
	for _i := range memberId {
		_va[_i] = memberId[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, adminId)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, ...int) []map[string]interface{}); ok {
		r0 = rf(adminId, memberId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, ...int) error); ok {
		r1 = rf(adminId, memberId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchMemberNotifToken provides a mock function with given fields: memberDetailId
func (_m *CampaignRepository) FetchMemberNotifToken(memberDetailId ...int64) ([]map[string]interface{}, error) {
	_va := make([]interface{}, len(memberDetailId))
	for _i := range memberDetailId {
		_va[_i] = memberDetailId[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(...int64) []map[string]interface{}); ok {
		r0 = rf(memberDetailId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(...int64) error); ok {
		r1 = rf(memberDetailId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RemoveCampaign provides a mock function with given fields: id, user
func (_m *CampaignRepository) RemoveCampaign(id int, user domain.UserSession) error {
	ret := _m.Called(id, user)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.UserSession) error); ok {
		r0 = rf(id, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemoveScheduledMessageByIdentifier provides a mock function with given fields: identifierId
func (_m *CampaignRepository) RemoveScheduledMessageByIdentifier(identifierId interface{}) error {
	ret := _m.Called(identifierId)

	var r0 error
	if rf, ok := ret.Get(0).(func(interface{}) error); ok {
		r0 = rf(identifierId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewCampaignRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewCampaignRepository creates a new instance of CampaignRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCampaignRepository(t mockConstructorTestingTNewCampaignRepository) *CampaignRepository {
	mock := &CampaignRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
