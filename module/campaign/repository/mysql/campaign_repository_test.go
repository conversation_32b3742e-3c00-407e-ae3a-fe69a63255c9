package mysql

import (
	"fmt"
	"testing"
	"time"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
)

func Test_campaignRepository_AddScheduleMessage(t *testing.T) {
	msgs := make([]domain.ScheduleMessage, 0)
	msgs = append(msgs, domain.ScheduleMessage{
		Title:        "Deals Alert!",
		Message:      "Promo Gratis sudah bisa kamu klaim sekarang, jangan sampai kelewatan!",
		TimeDeliver:  time.Now().Unix() * 1000,
		DataCreated:  time.Now().Unix() * 1000,
		Media:        "push_notif",
		Receiver:     "fuU_RECwSaGoSQKHY_TslH:APA91bGaPM1_kU21FRHt6FbRJirDqhQEU5WkkKx-tQSlg7lYXHtFibgNPM2gxLa9vQpnEwWECYgYFc0TkcvwLuUxYRFwiSUhFo5C1osxITxa6miyTPfpGl7WXwerBQ4FR_A-JCk7-KA9",
		IdentifierID: 1,
		MessageDetail: utils.SimplyToJson(map[string]interface{}{
			"notification_type": "promotion",
			"notification_data": map[string]interface{}{
				"id": 1398,
			},
		}),
	})
	type fields struct {
		db db.Repository
	}
	type args struct {
		message []domain.ScheduleMessage
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{"test1", fields{}, args{msgs}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := campaignRepository{
				db: tt.fields.db,
			}
			if err := c.AddScheduleMessage(tt.args.message...); (err != nil) != tt.wantErr {
				t.Errorf("campaignRepository.AddScheduleMessage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_chunkArray(t *testing.T) {
	data := make([]int, 0)
	for i := 0; i <= 10001; i++ {
		data = append(data, i)
	}

	totalAll := 0
	for i := 0; i < len(data); i += 5000 {
		total := 0
		for j := i; j < i+5000 && j < len(data); j++ {
			total += 1
		}
		totalAll += total
		fmt.Println("will insert: ", total)
	}

	if totalAll != len(data) {
		t.Errorf("total not same, want: '%v' got: '%v'", len(data), totalAll)
	}

	totalAll = 0
	for i := 0; i <= len(data); i += 5000 {
		j := i + 5000
		if j > len(data) {
			j = len(data)
		}
		insr := data[i:j]
		totalAll += len(insr)
		fmt.Println(">> will insert: ", len(insr))
		fmt.Println("-> ", insr[0], " | ", insr[len(insr)-1])
	}

	if totalAll != len(data) {
		t.Errorf("total not same, want: '%v' got: '%v'", len(data), totalAll)
	}
}
