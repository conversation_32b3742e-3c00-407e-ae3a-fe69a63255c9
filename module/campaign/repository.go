package campaign

import "gitlab.com/uniqdev/backend/api-membership/domain"

type CampaignRepository interface {
	AddCampaign(campaign domain.Campaign, user domain.UserSession) (int64, error)
	FetchMember(adminId int, memberId ...int) ([]map[string]interface{}, error)
	FetchMemberNotifToken(memberDetailId ...int64) ([]map[string]interface{}, error)
	FetchScheduledMessagesByCampaignID(campaignID int) ([]map[string]interface{}, error) // Added

	AddScheduleMessage(message ...domain.ScheduleMessage) error
	BulkUpdateCampaignMemberSendScheduleID(updates []map[string]interface{}) error // Added
	FetchScheduleMessage(filter domain.ScheduleMessageFilter) ([]domain.ScheduleMessage, error)
	RemoveCampaign(id int, user domain.UserSession) error
	RemoveScheduledMessageByIdentifier(identifierId interface{}) error
}
