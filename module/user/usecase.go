package user

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type UseCase interface {
	Register(request domain.RegisterRequest, user domain.UserSession) (interface{}, error)
	FetchUserAccess(userSession domain.UserSession) ([]map[string]interface{}, error)
	FetchMember(param domain.UserRequestParam, userSession domain.UserSession) ([]map[string]interface{}, error)
	FetchUserAddress(includeProfileAddress bool, userSession domain.UserSession) ([]map[string]interface{}, error)
	FetchPointHistory(user domain.UserSession) ([]map[string]interface{}, error)
	FetchTypeHistory(user domain.UserSession) ([]models.TypeHistoryEntity, error)

	AddUserAddress(address domain.UserAddres, userSession domain.UserSession) error

	UpdateUserAccess(memberId int, access string, user domain.UserSession) error
	UpdateUser<PERSON>ddress(id int, address domain.UserAddres, userSession domain.UserSession) error

	RemoveUserAddress(addressId int, userSession domain.UserSession) error
	RequestRemoveAccount(user domain.UserSession) (map[string]interface{}, error)
	RemoveAccount(code, key string, user domain.UserSession) error
}
