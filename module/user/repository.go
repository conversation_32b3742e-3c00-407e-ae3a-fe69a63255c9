package user

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type Repository interface {
	FetchUserAccess(userSession domain.UserSession) ([]map[string]interface{}, error)
	RemoveUserAccess(memberId int, userSession domain.UserSession) error
	UpdateUserAccess(memberId int, access string, userSession domain.UserSession) error

	AddMember(member domain.RegisterRequest, user domain.UserSession) (int64, error)
	FetchMember(userSession domain.UserSession) ([]map[string]interface{}, error)
	FetchMemberByIds(memberIds ...int) ([]map[string]interface{}, error)
	FetchMemberFilter(filer domain.MemberFilter) (models.MemberEntity, error)
	FetchUserAddress(userSession domain.UserSession) ([]map[string]interface{}, error)
	FetchUserProfile(user domain.UserSession) (map[string]interface{}, error)
	FetchPointHistory(user domain.UserSession) ([]map[string]interface{}, error)
	FetchTypeHistory(user domain.UserSession) ([]models.TypeHistoryEntity, error)
	AddUserPoint(addPoint int, user domain.UserSession) error

	AddUserAddress(address domain.UserAddres, userSession domain.UserSession) error
	UpdateUserAddress(id int, address domain.UserAddres, userSession domain.UserSession) error
	RemoveUserAddress(addressId int, userSession domain.UserSession) error
	RemoveAccount(user domain.UserSession) error
	FetchMemberDetail(user domain.UserSession) (map[string]interface{}, error)
	FetchMemberType(user domain.UserSession) (map[string]interface{}, error)
	AddScheduleMessage(data ...domain.ScheduleMessage) error

	FetchDefaultMemberType(adminId int64) (models.MemberTypeEntity, error)
	FetchPointCollection(event string, adminId int64) ([]models.PointCollectionEntity, error)

	FetchNotificationToken(memberId, adminId int64) (*[]models.NotificationToken, error)
	FetchMemberNotificationToken(memberId ...int) ([]models.NotificationToken, error)

	FetchAdmin(adminId int64) (map[string]interface{}, error)
}
