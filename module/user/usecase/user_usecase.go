package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/language"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/messaging"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

type userUseCase struct {
	repo    user.Repository
	ucAuth  domain.AuthUseCase
	ucPromo promotion.UseCase
}

func NewUserUseCase(repository user.Repository, authUseCase domain.AuthUseCase, promo promotion.UseCase) user.UseCase {
	return &userUseCase{repository, authUseCase, promo}
}

func (u userUseCase) Register(request domain.RegisterRequest, user domain.UserSession) (interface{}, error) {
	//validation
	if request.Email == "" || request.Name == "" {
		return nil, fmt.Errorf("invalid input")
	}

	if request.Gender != 0 && request.Gender != 1 {
		return nil, fmt.Errorf("gender should be 0 or 1")
	}

	if request.IdToken == "" && request.Phone == "" && request.Email == "" {
		return nil, fmt.Errorf("invalid input, phone or email can not be empty")
	}

	var addDataMap map[string]interface{}
	if request.AdditionalData != "" {
		err := json.Unmarshal([]byte(request.AdditionalData), &addDataMap)
		if log.IfError(err) {
			return nil, err
		}
	}

	if !utils.IsValidEmailAddress(request.Email) {
		return nil, fmt.Errorf("invalid email address format")
	}

	request.PhoneVerified = 0
	if request.IdToken != "" {
		// check id token from firebase
		ctxBack := context.Background()
		client, err := firebase.GetFirebaseApp().Auth(ctxBack)
		if err != nil {
			fmt.Printf("error getting Auth client: %v\n", err)
		}

		token, err := client.VerifyIDToken(ctxBack, request.IdToken)
		if err != nil {
			log.Info("error verifying ID token: %v\n", err)
			return nil, fmt.Errorf("invalid id_token")
		}

		if token.Claims["phone_number"] == nil {
			log.Warn("backend can not get phone number from the token : %v", token)
		} else {
			request.Phone = utils.ToString(token.Claims["phone_number"])
			request.PhoneVerified = 1
		}

		if token.Claims["email"] != nil {
			request.Email = utils.ToString(token.Claims["email"])
		}
	}

	request.Phone = utils.FormatPhoneNumber(request.Phone)

	//check existing user
	member, err := u.repo.FetchMemberFilter(domain.MemberFilter{Phone: request.Phone, Email: request.Email})
	if log.IfError(err) {
		return nil, err
	}

	if member.MemberID > 0 {
		if member.Phone == request.Phone && member.Email == request.Email {
			// adminFkIds := strings.Split(utils.ToString(data["admin_fkids"]), ",")
			// isRegisteredAtThisAdmin := false
			// for _, id := range adminFkIds {
			// 	if id == utils.ToString(user.AdminId) {
			// 		isRegisteredAtThisAdmin = true
			// 		break
			// 	}
			// }

			// if isRegisteredAtThisAdmin {
			// 	log.Info("'%s' is registered at this business", phone)
			// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 30, Message: "You're already registered at this business!"})
			// 	return
			// } else {
			// 	log.Info("'%s' is registered at : %s", phone, data["business"])
			// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 31, Message: fmt.Sprintf("You are already registered at : %s", data["business"]), Data: map[string]interface{}{
			// 		"business": data["business"],
			// 	}})
			// 	return
			// }
		}
		msg := "Email already used"
		code := 32
		if member.Phone == request.Phone {
			msg = "Phone number already used"
			code = 33
		}

		fmt.Println(code)
		return nil, fmt.Errorf(msg)
	}

	//look for default member type
	memberType, err := u.repo.FetchDefaultMemberType(user.AdminId)
	if log.IfError(err) {
		return nil, err
	}

	if memberType.TypeID == 0 {
		log.Info("admin : %s has no default member type", user.AdminId)
		log.IfError(fmt.Errorf("adminId %v has no default member type", user.AdminId))
		return nil, fmt.Errorf("this business has no default member type")
	}

	memberId, err := u.repo.AddMember(request, user)
	if log.IfError(err) {
		return nil, err
	}

	// go sendEmailConfirmation(adminFkid, utils.ToString(memberId), email, name)
	// v1.AutoEarnPoint("", 1, 1)
	// go autoEarnPoint(adminFkid, memberId, memberDetailId)
	// go job.RunPromotionRole()

	//if id_token not sent, send verification through whatsapp
	if request.IdToken == "" {
		go u.ucAuth.SendAuthLink(request.Phone, "whatsapp", user)
	}

	go u.RunPointCollection("register", memberId, user.AdminId)
	go u.ucPromo.RunPromotionRole(domain.PromotionRoleParam{AdminId: int(user.AdminId)})

	return memberId, nil
}

// event: register
func (u userUseCase) RunPointCollection(event string, memberId, adminId int64) error {
	// check from database first
	pointCollections, err := u.repo.FetchPointCollection(event, adminId)
	log.IfError(err)

	totalPoint := 0
	if len(pointCollections) > 0 {
		totalPoint = pointCollections[0].Point
		log.Info("point collection register from db: (%v) %v --> %d", pointCollections[0].PointCollectionID, pointCollections[0].Name, totalPoint)
	}

	if len(pointCollections) == 0 && (os.Getenv("server") == "development" || (os.Getenv("server") == "staging" && (adminId == 10 || adminId == 7))) {
		totalPoint = 10
		log.Info("point collection hardcoded to: %v | adminId %v", totalPoint, adminId)
	}

	if totalPoint == 0 {
		log.Info("adminId : '%s' no need to generate auto point", adminId)
		return nil
	}

	_, err = db.Update("members_detail", map[string]interface{}{
		"total_point": totalPoint,
	}, "admin_fkid = ? and member_fkid = ?", adminId, memberId)

	if !log.IfError(err) {
		time.Sleep(15 * time.Second) // wait, in order to be able to get push notif token
		messaging.PushNotification(messaging.Notification{
			Title:    "Kamu dapat bonus " + language.Point(utils.ToInt(adminId)),
			Message:  fmt.Sprintf("Selamat kamu dapat %d %s gratis", totalPoint, language.Point(utils.ToInt(adminId))),
			MemberId: memberId,
			AdminId:  adminId,
		})
	}
	return nil
}

func (u userUseCase) FetchUserAccess(userSession domain.UserSession) ([]map[string]interface{}, error) {
	if userSession.MemberAccess != domain.AccessAdmin {
		log.Info("can not FetchUserAccess, current access: ", userSession.MemberAccess)
		return nil, fmt.Errorf("only admin allowed to fetch the request")
	}

	return u.repo.FetchUserAccess(userSession)
}

func (u userUseCase) UpdateUserAccess(memberId int, access string, userSession domain.UserSession) error {
	if userSession.MemberAccess != domain.AccessAdmin {
		log.Info("can not UpdateUserAccess, current access: ", userSession.MemberAccess)
		return fmt.Errorf("only admin allowed to fetch the request")
	}

	if access != "member" && access != "admin" {
		return fmt.Errorf("invalid access value")
	}

	if memberId == int(userSession.MemberId) {
		return fmt.Errorf("you can not change your own access")
	}

	if access == "member" {
		return u.repo.RemoveUserAccess(memberId, userSession)
	}

	return u.repo.UpdateUserAccess(memberId, access, userSession)
}

func (u userUseCase) FetchMember(param domain.UserRequestParam, userSession domain.UserSession) ([]map[string]interface{}, error) {
	if userSession.MemberAccess != domain.AccessAdmin {
		log.Info("can not UpdateUserAccess, current access: ", userSession.MemberAccess)
		return nil, fmt.Errorf("only admin allowed to fetch the request")
	}

	members, err := u.repo.FetchMember(domain.UserSession{AdminId: userSession.AdminId})
	if err != nil {
		return nil, err
	}

	memberAccess, err := u.repo.FetchUserAccess(userSession)
	if err != nil {
		return nil, err
	}

	memberAccessMap := make(map[int][]string)
	for _, access := range memberAccess {
		if memberAccessMap[cast.ToInt(access["member_id"])] == nil {
			memberAccessMap[cast.ToInt(access["member_id"])] = make([]string, 0)
		}
		memberAccessMap[cast.ToInt(access["member_id"])] = append(memberAccessMap[cast.ToInt(access["member_id"])], cast.ToString(access["access_type"]))
	}

	log.Info("access filer size: %d", len(param.AccessType))
	result := make([]map[string]interface{}, 0)
	for i, m := range members {
		if memberAccessMap[cast.ToInt(m["member_id"])] == nil {
			members[i]["access_types"] = []string{"member"}
		} else {
			members[i]["access_types"] = memberAccessMap[cast.ToInt(m["member_id"])]
		}

		shouldAdd := true
		if len(param.AccessType) > 0 {
			shouldAdd = false
			for _, accessFilter := range param.AccessType {
				if userAccess, ok := members[i]["access_types"].([]string); ok {
					for _, access := range userAccess {
						if access == accessFilter {
							shouldAdd = true
							break
						}
					}
				}
			}
		}

		if shouldAdd {
			result = append(result, members[i])
		}
	}

	return result, nil
}

func (u userUseCase) FetchUserAddress(includeProfileAddress bool, userSession domain.UserSession) ([]map[string]interface{}, error) {
	result, err := u.repo.FetchUserAddress(userSession)
	if err != nil {
		return nil, err
	}
	if includeProfileAddress {
		userProfile, err := u.repo.FetchUserProfile(userSession)
		if err == nil {
			result = append(result, utils.TakesOnly(userProfile, "province", "city", "address", "postal_code", "phone"))
		}
	}
	return result, err
}

func (u userUseCase) AddUserAddress(address domain.UserAddres, userSession domain.UserSession) error {
	//validate
	if address.Address == "" || address.Label == "" {
		return fmt.Errorf("address and label can not be empty")
	}

	return u.repo.AddUserAddress(address, userSession)
}

func (u userUseCase) UpdateUserAddress(id int, address domain.UserAddres, userSession domain.UserSession) error {
	return u.repo.UpdateUserAddress(id, address, userSession)
}

func (u userUseCase) RemoveUserAddress(addressId int, userSession domain.UserSession) error {
	return u.repo.RemoveUserAddress(addressId, userSession)
}

func (u userUseCase) RequestRemoveAccount(user domain.UserSession) (map[string]interface{}, error) {
	//validate account
	member, err := u.repo.FetchMemberDetail(user)
	if err != nil {
		return nil, err
	}

	if len(member) == 0 {
		return nil, fmt.Errorf("account not found")
	}

	code := utils.RandomNumber(100, 999999)
	if strings.HasPrefix(utils.DemoAccountPhone, utils.FormatPhoneNumber(member["phone"])) {
		code = utils.DemoAccountRemoveCode
	}

	key := fmt.Sprintf("%06d:%v", code, time.Now().Unix()*1000)
	key = utils.Encrypt(key, utils.KEY_REMOVE_ACCOUNT)
	log.Info("code for %s is: %s", member["phone"], code)

	err = u.repo.AddScheduleMessage(domain.ScheduleMessage{
		Title:       "Request Remove Account",
		Message:     fmt.Sprintf("use the code below to remove your account: </br></br><h4> %d </h4>", code),
		TimeDeliver: time.Now().Unix() * 1000,
		Media:       "email",
		Receiver:    cast.ToString(member["email"]),
	})

	return map[string]interface{}{
		"key": key,
	}, err
}

func (u userUseCase) RemoveAccount(code string, key string, user domain.UserSession) error {
	key = utils.Decrypt(key, utils.KEY_REMOVE_ACCOUNT)
	keyInfo := strings.Split(key, ":")
	log.Info("key dect. %v | %v", key, keyInfo)
	if len(keyInfo) != 2 {
		return fmt.Errorf("invalid key")
	}

	if code != keyInfo[0] {
		return fmt.Errorf("invalid code")
	}

	if os.Getenv("ENV") != "development" {
		return fmt.Errorf("code is invalid or expired")
	}

	member, err := u.repo.FetchMemberDetail(user)
	if err != nil {
		return err
	}

	err = u.repo.RemoveAccount(user)
	if err != nil {
		return err
	}

	//remove from firebase
	go RemoveUseAccountFromFirebase(cast.ToString(member["email"]))

	err = u.repo.AddScheduleMessage(domain.ScheduleMessage{
		Title:       "Account Removed",
		Message:     fmt.Sprintf("Hi, %s \nYour account has been successfully removed.", member["name"]),
		TimeDeliver: time.Now().Unix() * 1000,
		Media:       "email",
		Receiver:    cast.ToString(member["email"]),
	})
	return err
}

func (u userUseCase) FetchPointHistory(user domain.UserSession) ([]map[string]interface{}, error) {
	return u.repo.FetchPointHistory(user)
}

func (u userUseCase) FetchTypeHistory(user domain.UserSession) ([]models.TypeHistoryEntity, error) {
	return u.repo.FetchTypeHistory(user)
}

func FetchEmailVerificationStatusFromFirebase(email string) (bool, error) {
	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		fmt.Printf("error getting Auth client: %v\n", err)
		return false, err
	}

	user, err := client.GetUserByEmail(ctxBack, email)
	if err != nil {
		fmt.Println("err getting user by email: ", err)
		return false, err
	}

	fmt.Println(email, " - email verified: ", user.EmailVerified)
	return user.EmailVerified, nil
}

func RemoveUseAccountFromFirebase(email string) error {
	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		fmt.Printf("error getting Auth client: %v\n", err)
		return err
	}

	user, err := client.GetUserByEmail(ctxBack, email)
	if err != nil {
		fmt.Println("err getting user by email: ", err)
		return err
	}

	log.Info("delete user from firebase, '%s' -> '%s'", email, user.UID)
	err = client.DeleteUser(ctxBack, user.UID)
	log.IfError(err)
	return err
}
