package usecase

import (
	"testing"

	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

func Test_userUseCase_RemoveAccount(t *testing.T) {
	type fields struct {
		repo domain.UserRepository
	}
	type args struct {
		code string
		key  string
		user domain.UserSession
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{"test valid", fields{}, args{
			code: "088031",
			key:  "Azvp5aCagaM8CLLM7ggBCCN24Sk4-j2K1qyKUbiDjo106NdR3pft_Mkb",
		}, false},
		{"test not valid", fields{}, args{
			code: "000000",
			key:  "Azvp5aCagaM8CLLM7ggBCCN24Sk4-j2K1qyKUbiDjo106NdR3pft_Mkb",
		}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u := userUseCase{
				repo: tt.fields.repo,
			}
			if err := u.RemoveAccount(tt.args.code, tt.args.key, tt.args.user); (err != nil) != tt.wantErr {
				t.Errorf("userUseCase.RemoveAccount() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_userUseCase_FetchEmailVerificationStatus(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-crm/.env") //Load .env file
	firebase.SetupFirebaseConfig("/Users/<USER>/Documents/WORK/api-crm/config/auth/uniq-crm-production-firebase.json")

	type fields struct {
		repo domain.UserRepository
	}
	type args struct {
		email string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{"test1", fields{}, args{"<EMAIL>"}},
		{"test2", fields{}, args{"<EMAIL>"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// u := userUseCase{
			// 	repo: tt.fields.repo,
			// }
			// u.FetchEmailVerificationStatus(tt.args.email)
		})
	}
}
