package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

type userRepository struct {
	db db.Repository
}

func NewMysqlUserRepository(conn *sql.DB, cache domain.CacheInterface) user.Repository {
	return &userRepository{db.Repository{Conn: conn, CacheDb: cache}}
}

func (u userRepository) FetchUserAccess(userSession domain.UserSession) ([]map[string]interface{}, error) {
	sql := `
SELECT
	m.member_id,
	m.email,
	m.name,
	m.phone,
	m.date_of_birth,
	m.city,
	m.address,
	m.postal_code,
	ma.access_type
FROM
	members_access ma
	JOIN members m ON m.member_id = ma.member_fkid
WHERE
	ma.admin_fkid = ?
ORDER BY
	access_type,
	m.name `
	return db.QueryArray(sql, userSession.AdminId)
}

func (u userRepository) RemoveUserAccess(memberId int, userSession domain.UserSession) error {
	_, err := db.Delete("members_access", "member_fkid = ? and admin_fkid = ?", memberId, userSession.AdminId)
	return err
}

func (u userRepository) UpdateUserAccess(memberId int, access string, userSession domain.UserSession) error {
	resp, err := db.Update("members_access", map[string]interface{}{
		"access_type": access,
	}, "member_fkid = ? and admin_fkid = ?", memberId, userSession.AdminId)

	totalRow, _ := resp.RowsAffected()
	if totalRow == 0 {
		log.Info("update not affected any rows, insert...")
		_, err = db.Insert("members_access", map[string]interface{}{
			"member_fkid":  memberId,
			"admin_fkid":   userSession.AdminId,
			"access_type":  access,
			"time_created": time.Now().Unix() * 1000,
		})
	}
	return err
}

func (u userRepository) FetchMember(userSession domain.UserSession) ([]map[string]interface{}, error) {
	sql := `
SELECT
	m.member_id,
	m.email,
	m.name,
	m.phone,
	m.date_of_birth,
	m.city,
	m.address,
	m.postal_code,
	md.total_point,
	md.type_fkid,
	md.status,
	p.name AS member_type
FROM
	members_detail md
	JOIN members m ON m.member_id = md.member_fkid
	JOIN members_type mt ON mt.type_id = md.type_fkid
	JOIN products p ON p.product_id = mt.product_fkid
WHERE
	md.admin_fkid = @adminId
	AND md.status = 1
	$WHERE
ORDER BY
	m.name `

	whereSql := ""
	if userSession.MemberId > 0 {
		whereSql = " AND md.member_fkid = @memberId "
	}

	sql = strings.Replace(sql, "$WHERE", whereSql, 1)

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":  userSession.AdminId,
		"memberId": userSession.MemberId,
	})

	return db.QueryArray(sql, params...)
}

func (u userRepository) FetchMemberByIds(memberIds ...int) ([]map[string]interface{}, error) {
	sql := `select * from members where member_id in @ids and status = 1`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": memberIds,
	})
	return db.QueryArray(sql, params...)
}

func (u userRepository) FetchUserAddress(userSession domain.UserSession) ([]map[string]interface{}, error) {
	sql := `select * from members_address where member_fkid = ?`
	return db.QueryArray(sql, userSession.MemberId)
}

func (u userRepository) AddUserAddress(address domain.UserAddres, user domain.UserSession) error {
	_, err := db.Insert("members_address", map[string]interface{}{
		"label":       address.Label,
		"province":    address.Province,
		"city":        address.City,
		"address":     address.Address,
		"phone":       address.Phone,
		"member_fkid": user.MemberId,
		"latitude":    address.Latitute,
		"longitude":   address.Longitude,
	})
	return err
}

func (u userRepository) UpdateUserAddress(id int, address domain.UserAddres, userSession domain.UserSession) error {
	_, err := db.Update("members_address", map[string]interface{}{
		"label":     address.Label,
		"province":  address.Province,
		"city":      address.City,
		"address":   address.Address,
		"phone":     address.Phone,
		"latitude":  address.Latitute,
		"longitude": address.Longitude,
	}, "members_address_id = ? and member_fkid = ?", id, userSession.MemberId)
	return err
}

func (u userRepository) RemoveUserAddress(addressId int, userSession domain.UserSession) error {
	_, err := db.Delete("members_address", "members_address_id = ? and member_fkid = ?", addressId, userSession.MemberId)
	return err
}

func (u userRepository) FetchUserProfile(user domain.UserSession) (map[string]interface{}, error) {
	sql := `SELECT
	m.*
FROM
	members m
	JOIN members_detail md ON m.member_id = md.member_fkid
WHERE
	m.member_id = ?
	AND md.admin_fkid = ? `
	return db.Query(sql, user.MemberId, user.AdminId)
}

func (u userRepository) RemoveAccount(user domain.UserSession) error {
	member, err := db.Query("select phone, email from members where member_id =?", user.MemberId)
	if err != nil {
		return err
	}

	_, err = db.Update("members", map[string]interface{}{
		"status": 0,
		"phone":  fmt.Sprintf("%v%d", member["phone"], utils.RandomNumber(100, 9999)),
		"email":  fmt.Sprintf("noactive_%d_%v", utils.RandomNumber(10, 999), member["email"]),
	}, "member_id = ? ", user.MemberId)
	return err
}

func (u userRepository) FetchMemberDetail(user domain.UserSession) (map[string]interface{}, error) {
	sql := `select member_id,
       email,
       email_verified,
       m.name,
       gender,
       phone,
       date_of_birth,
       address,
       city,
       province,
       postal_code,
       register_date,
       total_point,
	   m.data_created,
       p.name as member_type,
		 md.members_detail_id,
		 ma.access_type, md.type_fkid
from members m
         join members_detail md on md.member_fkid = m.member_id
         join members_type mt on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
		 left join (select access_type,member_fkid from members_access where admin_fkid = ?) ma on ma.member_fkid=m.member_id
WHERE member_id = ?
  and md.admin_fkid = ?
  and m.status != 0
LIMIT 1`

	return db.Query(sql, user.AdminId, user.MemberId, user.AdminId)
}

// FetchMemberType implements user.Repository.
func (u *userRepository) FetchMemberType(user domain.UserSession) (map[string]interface{}, error) {
	sql := `select member_id, md.type_fkid
from members m
         join members_detail md on md.member_fkid = m.member_id
WHERE member_id = ?
  and md.admin_fkid = ?
  and m.status != 0
LIMIT 1`

	// return db.Query(sql, user.MemberId, user.AdminId)
	var result []map[string]interface{}
	err := u.db.QueryCache("memberType", 10*time.Minute, sql, user.MemberId, user.AdminId).Model(&result)
	if len(result) > 0 {
		return result[0], err
	}
	return nil, err
}

func (a userRepository) AddScheduleMessage(data ...domain.ScheduleMessage) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		for _, row := range data {
			row.DataCreated = time.Now().Unix() * 1000
			tx.Insert("scheduled_message", row.ToMap())
		}
		return nil
	})
	return err
}

func (a userRepository) FetchPointHistory(user domain.UserSession) ([]map[string]interface{}, error) {
	sql := `SELECT 'sales' as source, s.sales_id as id, s.display_nota as source_desc, s.point_earned as point, 
	s.time_created, 'd' as type
	from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where s.point_earned > 0 and s.member_fkid=@memberId 
	and o.admin_fkid=@adminId
	UNION 
	SELECT 'promotion', pb.promotion_buy_id,p.name, price*-1, pb.time_created, 'k'  
	from promotion_buy pb 
	join promotions p on p.promotion_id=pb.promotion_fkid
	where pb.price > 0 and pb.price_type='point' 
	and p.admin_fkid=@adminId
	and pb.member_fkid=@memberId
	order by time_created`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":  user.AdminId,
		"memberId": user.MemberId,
	})
	return db.QueryArray(sql, params...)
}

func (u userRepository) FetchTypeHistory(user domain.UserSession) ([]models.TypeHistoryEntity, error) {
	var result []models.TypeHistoryEntity
	err := u.db.Prepare("select * from members_type_history where member_fkid = ? and admin_fkid = ?", user.MemberId, user.AdminId).Get(&result)
	return result, err
}

func (a userRepository) FetchDefaultMemberType(adminId int64) (models.MemberTypeEntity, error) {
	sql := `SELECT type_id FROM members_type where point_target=0 and spent_target=0 
	and price=0 and admin_fkid = ? `

	var result models.MemberTypeEntity
	err := a.db.Prepare(sql, adminId).Get(&result)
	return result, err
}

func (a userRepository) AddMember(member domain.RegisterRequest, user domain.UserSession) (int64, error) {
	memberDefault, _ := a.FetchDefaultMemberType(user.AdminId)
	var memberId, memberDetailId int64
	err := db.WithTransaction(func(tx db.Transaction) error {
		dataMember := map[string]interface{}{
			"email":          member.Email,
			"name":           member.Name,
			"gender":         member.Gender,
			"phone":          member.Phone,
			"date_of_birth":  member.DateOfBirth,
			"city":           member.City,
			"province":       member.Province,
			"address":        member.Address,
			"phone_verified": member.PhoneVerified,
			"expired_date":   time.Now().Unix() * 1000,
			"data_created":   time.Now().Unix() * 1000,
		}

		res, err := tx.Insert("members", utils.RemvoveEmptyFields(dataMember))

		if err != nil {
			return err
		}

		memberId, _ = res.LastInsertId()
		res, err = tx.Insert("members_detail", map[string]interface{}{
			"member_fkid":   memberId,
			"admin_fkid":    user.AdminId,
			"type_fkid":     memberDefault.TypeID,
			"register_date": time.Now().Unix() * 1000,
		})
		if err != nil {
			return err
		}

		memberDetailId, _ = res.LastInsertId()

		if member.AdditionalData != "" {
			var additionalDataMap map[string]interface{}
			err := json.Unmarshal([]byte(member.AdditionalData), &additionalDataMap)
			log.IfError(err)

			for dataKey, dataVal := range additionalDataMap {
				if strings.TrimSpace(utils.ToString(dataVal)) == "" {
					log.Warn("value for data '%s' is empty", dataKey)
					continue
				}
				_, err = db.Insert("members_detail_data", map[string]interface{}{
					"member_detail_fkid": memberDetailId,
					"data_key":           dataKey,
					"data_value":         dataVal,
				})
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	return memberId, err
}

func (a userRepository) FetchMemberFilter(filter domain.MemberFilter) (models.MemberEntity, error) {
	sql := `select member_id, phone, email from members where phone = ? or email = ?`

	var result models.MemberEntity
	err := a.db.Prepare(sql, filter.Phone, filter.Email).Get(&result)
	return result, err
}

func (a userRepository) FetchPointCollection(event string, adminId int64) ([]models.PointCollectionEntity, error) {
	sql := `SELECT point_collection_id, name, point from point_collection 
where point_type = @event and admin_fkid = @adminId
and date_start <= UNIX_TIMESTAMP()*1000 
and date_end >= UNIX_TIMESTAMP()*1000
and time_start <= time(FROM_UNIXTIME(UNIX_TIMESTAMP()+@timeOffset))
and time_end >= time(FROM_UNIXTIME(UNIX_TIMESTAMP()+@timeOffset))
and @dayName=1
and data_status=1
limit 1 `

	timeOffset := 25200
	day := "day_" + time.Now().Add(time.Duration(timeOffset)*time.Second).Weekday().String()
	day = strings.ToLower(day)
	log.Info("day: %v", day)
	sql = strings.Replace(sql, "@dayName", day, 1)

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    adminId,
		"timeOffset": timeOffset,
		"event":      event,
	})

	var result []models.PointCollectionEntity
	err := a.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (a userRepository) AddUserPoint(addPoint int, user domain.UserSession) error {
	_, err := a.db.Conn.Exec("update members_detail set total_point = total_point +? where member_fkid =? and admin_fkid = ?", addPoint, user.MemberId, user.AdminId)
	return err
}

func (a userRepository) FetchNotificationToken(memberId, adminId int64) (*[]models.NotificationToken, error) {
	sql := `SELECT md.member_fkid, md.admin_fkid, unt.token from user_notification_token unt 
	join members_detail md on md.members_detail_id=unt.user_id
	where unt.user_type='member' and unt.app='crm'
	and md.member_fkid = ? and md.admin_fkid = ? `

	var result []models.NotificationToken
	err := a.db.Prepare(sql, memberId, adminId).Get(&result)
	return &result, err
}

func (a userRepository) FetchMemberNotificationToken(memberId ...int) ([]models.NotificationToken, error) {
	sql := `SELECT md.member_fkid, md.admin_fkid, unt.token from user_notification_token unt 
	join members_detail md on md.members_detail_id=unt.user_id
	where unt.user_type='member' and unt.app='crm'
	and md.member_fkid in @ids `

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": memberId,
	})
	var result []models.NotificationToken
	err := a.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (a userRepository) FetchAdmin(adminId int64) (map[string]interface{}, error) {
	sql := `select * from admin where admin_id = ?`
	// var result map[string]interface{}
	// err := a.db.Prepare(sql, adminId).Get(&result)
	// return result, err
	return db.Query(sql, adminId)
}
