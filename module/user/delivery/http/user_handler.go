package http

import (
	"encoding/json"
	"strings"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

type userHandler struct {
	uc user.UseCase
}

func NewHttpUserHandler(app *fasthttprouter.Router, useCase user.UseCase) {
	handler := &userHandler{useCase}

	app.GET("/v1/user", auth.ValidateToken(handler.FetchMember))
	app.DELETE("/v1/user", auth.ValidateToken(handler.RemoveAccount))
	app.POST("/v1/user/remove-account", auth.ValidateToken(handler.RequestRemoveAccount))

	app.POST("/v2/user/register", auth.ValidatePublicKey(handler.Register))

	//user access
	app.GET("/v1/user-access", auth.ValidateToken(handler.FetchUserAccess))
	app.PUT("/v1/user-access", auth.ValidateToken(handler.UpdateUserAccess))
	app.DELETE("/v1/user-access", auth.ValidateToken(handler.RemoveUserAccess))

	//multi address
	app.GET("/v1/user/address", auth.ValidateToken(handler.FetchUserAddress))
	app.POST("/v1/user/address", auth.ValidateToken(handler.AddUserAddress))
	app.PUT("/v1/user/address/:id", auth.ValidateToken(handler.UpdateUserAddress))
	app.DELETE("/v1/user/address/:id", auth.ValidateToken(handler.RemoveUserAddress))

	//customer (customer: buyer who is not yet become member)
	// app.GET("v1/customer")

	app.GET("/v1/user/point-history", auth.ValidateToken(handler.FetchPointHistory))

	//inbox
	app.GET("/v1/user/inbox", auth.ValidateToken(handler.FetchInbox))

	app.GET("/v1/user/type-history", auth.ValidateToken(handler.FetchTypeHistory))
}

func (h *userHandler) Register(ctx *fasthttp.RequestCtx) {
	ctx.SetContentType("application/json")

	// email := string(ctx.PostArgs().Peek("email"))
	// name := string(ctx.PostArgs().Peek("name"))
	// gender := string(ctx.PostArgs().Peek("gender"))
	// dateOfBirth := string(ctx.PostArgs().Peek("date_of_birth"))
	// province := string(ctx.PostArgs().Peek("province"))
	// city := string(ctx.PostArgs().Peek("city"))
	// address := string(ctx.PostArgs().Peek("address"))
	// additionalData := ctx.PostArgs().Peek("additional_data")
	// idToken := ctx.PostArgs().Peek("id_token")
	// pubKey := string(ctx.Request.Header.Peek("Public-Key"))
	// adminFkid := utils.Decrypt(pubKey, utils.UID_PUBKEY)
	// phone := string(ctx.PostArgs().Peek("phone"))

	var request domain.RegisterRequest
	cast.ParseRequestFastHttp(ctx, &request)
	log.Info("register: %v", request)

	_, err := h.uc.Register(request, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *userHandler) FetchUserAccess(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchUserAccess(domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("FetchUserAccess err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result, Message: "success"})
}

func (h *userHandler) UpdateUserAccess(ctx *fasthttp.RequestCtx) {
	memberId := cast.ToInt(ctx.PostArgs().Peek("memberId"))
	access := string(ctx.PostArgs().Peek("access"))

	err := h.uc.UpdateUserAccess(memberId, access, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("UpdateUserAccess err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *userHandler) RemoveUserAccess(ctx *fasthttp.RequestCtx) {
	memberId := cast.ToInt(ctx.PostArgs().Peek("memberId"))

	err := h.uc.UpdateUserAccess(memberId, "member", domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("RemoveUserAccess err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *userHandler) FetchMember(ctx *fasthttp.RequestCtx) {
	accessType := string(ctx.QueryArgs().Peek("access_type"))
	param := domain.UserRequestParam{}

	if strings.TrimSpace(accessType) != "" {
		param.AccessType = strings.Split(accessType, ",")
	}

	result, err := h.uc.FetchMember(param, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("FetchMember err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result, Message: "success"})
}

func (h *userHandler) FetchUserAddress(ctx *fasthttp.RequestCtx) {
	notIncludeProfileAddress := string(ctx.QueryArgs().Peek("include_profile")) == "false"
	result, err := h.uc.FetchUserAddress(!notIncludeProfileAddress, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("FetchUserAddress err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result, Message: "success"})
}

func (h *userHandler) AddUserAddress(ctx *fasthttp.RequestCtx) {
	address := domain.UserAddres{
		Label:     string(ctx.PostArgs().Peek("label")),
		Province:  string(ctx.PostArgs().Peek("province")),
		City:      string(ctx.PostArgs().Peek("city")),
		Address:   string(ctx.PostArgs().Peek("address")),
		Phone:     string(ctx.PostArgs().Peek("phone")),
		Latitute:  cast.ToFloat(ctx.PostArgs().Peek("latitude")),
		Longitude: cast.ToFloat(ctx.PostArgs().Peek("longitude")),
	}

	err := h.uc.AddUserAddress(address, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("AddUserAddress err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *userHandler) UpdateUserAddress(ctx *fasthttp.RequestCtx) {
	id := utils.ToInt(ctx.UserValue("id"))
	address := domain.UserAddres{
		Label:     string(ctx.PostArgs().Peek("label")),
		Province:  string(ctx.PostArgs().Peek("province")),
		City:      string(ctx.PostArgs().Peek("city")),
		Address:   string(ctx.PostArgs().Peek("address")),
		Phone:     string(ctx.PostArgs().Peek("phone")),
		Latitute:  cast.ToFloat(ctx.PostArgs().Peek("latitude")),
		Longitude: cast.ToFloat(ctx.PostArgs().Peek("longitude")),
	}
	err := h.uc.UpdateUserAddress(id, address, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("UpdateUserAddress err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *userHandler) RemoveUserAddress(ctx *fasthttp.RequestCtx) {
	id := utils.ToInt(ctx.UserValue("id"))
	err := h.uc.RemoveUserAddress(id, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("RemoveUserAddress err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *userHandler) RequestRemoveAccount(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.RequestRemoveAccount(domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *userHandler) RemoveAccount(ctx *fasthttp.RequestCtx) {
	code := ctx.PostArgs().Peek("code")
	key := ctx.PostArgs().Peek("key")
	err := h.uc.RemoveAccount(string(code), string(key), domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

// FetchPointHistory
func (h *userHandler) FetchPointHistory(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchPointHistory(domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h *userHandler) FetchInbox(ctx *fasthttp.RequestCtx) {

}

func (h *userHandler) FetchTypeHistory(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchTypeHistory(domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}
