package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type appHandler struct {
	uc domain.AppUseCase
}

func NewHttpAppHandler(app *fasthttprouter.Router, useCase domain.AppUseCase) {
	handler := &appHandler{useCase}

	//app config
	app.GET("/v1/app-config", auth.ValidatePublic<PERSON>ey(handler.FetchAppConfig))

	//faq
	app.GET("/v1/app/faqs", auth.ValidatePublicKey(handler.FetchFAQs))

	//banner
	app.POST("/v1/app/banner-click/:id", auth.ValidatePublicKey(handler.UpdateBannerClick))
	app.GET("/v1/app/banner-click/:id", handler.UpdateBannerClick)
}

func (h appHandler) FetchAppConfig(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	result := map[string]interface{}{
		"message": "success",
		"data":    nil,
	}

	// headers := make(map[string]string)
	// ctx.Request.Header.VisitAll(func(key, value []byte) {
	// 	headers[string(key)] = string(value)
	// })
	// log.Info("appHandler.FetchAppConfig, headers: %v", headers)

	data, err := h.uc.FetchAppConfig(user)
	if err != nil {
		log.Info("appHandler.FetchAppConfig, err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		result["message"] = err.Error()
	}

	result["data"] = data
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h appHandler) FetchFAQs(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	result := map[string]interface{}{
		"message": "success",
		"data":    nil,
	}

	data, err := h.uc.FetchFAQs(user)
	if err != nil {
		result["message"] = err.Error()
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	}

	result["data"] = data
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h appHandler) UpdateBannerClick(ctx *fasthttp.RequestCtx) {
	// user := domain.UserSessionFastHttp(ctx)
	bannerId := ctx.UserValue("id")
	log.Info("update banner click of ids: %v", bannerId)

	ctx.Redirect("https://instagram.com/yamiepanda", 200)
}
