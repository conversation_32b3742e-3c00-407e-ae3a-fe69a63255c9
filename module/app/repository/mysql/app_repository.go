package mysql

import (
	"database/sql"
	"time"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type appRepository struct {
	db db.Repository
}

func NewAppRepository(conn *sql.DB, cache domain.CacheInterface) domain.AppRepository {
	return &appRepository{db: db.Repository{Conn: conn, CacheDb: cache}}
}

func (a appRepository) FetchAppInfoAndConfig(user domain.UserSession) (domain.CrmApp, error) {
	var result domain.CrmApp

	query := `SELECT crm_app_id, admin_fkid, app_info, app_config, privacy_policy, term_condition, data_created, data_modified FROM crm_app WHERE admin_fkid=?`
	err := a.db.Prepare(query, user.AdminId).Get(&result)
	// err := a.db.QueryRow(query, user.AdminId).Scan(&result.CrmAppID, &result.AdminFkid, &result.AppInfo, &result.AppConfig, &result.PrivacyPolicy, &result.TermCondition, &result.DataCreated, &result.DataModified)
	if err != nil {
		return result, err
	}

	return result, nil
}

func (a appRepository) FetchFAQs(adminID int) ([]map[string]interface{}, error) {
	query := `SELECT crm_faq_id, title, answer, status, category, admin_fkid, data_created, data_modified 
			 FROM crm_faq 
			 WHERE admin_fkid=? AND status='published'`

	result, err := a.db.QueryCache("faq", 1*time.Minute, query, adminID).ArrayMap()
	return result, err
}
