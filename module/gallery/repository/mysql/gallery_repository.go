package mysql

import (
	"database/sql"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
)

type galleryRepository struct {
	db     db.Repository
	filter domain.GalleryRepositoryFilter
}

func NewMysqlGalleryRepository(conn *sql.DB) domain.GalleryRepository {
	return &galleryRepository{db: db.Repository{Conn: conn}}
}

func (g galleryRepository) Filter(filter domain.GalleryRepositoryFilter) domain.GalleryRepository {
	return &galleryRepository{db: g.db, filter: filter}
}

func (g galleryRepository) FetchGalleryCategory(adminId int64) ([]map[string]interface{}, error) {
	args := make([]interface{}, 0)
	sql := "select id, name from crm_gallery_category where admin_fkid = ? "
	args = append(args, adminId)

	//adding filter
	if len(g.filter.Categories) > 0 {
		sql += " and name in " + db.WhereIn(len(g.filter.Categories))
		args = append(args, utils.ToInterfaceArray(g.filter.Categories)...)
	}

	if g.filter.Active {
		sql += " and id in (select distinct crm_gallery_category_fkid from crm_gallery_detail_category where crm_gallery_fkid is not null) "
	}

	sql += " order by name "

	return db.QueryArray(sql, args...)
}

func (g galleryRepository) AddGalleryCategory(categoryName string, adminId int64) (int64, error) {
	resp, err := db.Insert("crm_gallery_category", map[string]interface{}{
		"name":       categoryName,
		"admin_fkid": adminId,
	})
	if err != nil {
		return 0, err
	}
	id, err := resp.LastInsertId()
	return id, err
}

func (g galleryRepository) FetchGallery(galleryId int, adminId int64, page int, search string, categoryIds []int) ([]map[string]interface{}, error) {
	query := `SELECT id from crm_gallery WHERE $where order by id desc LIMIT ? OFFSET ?`
	limit := 20
	if page <= 1 {
		page = 0
	} else {
		page = limit * (page - 1)
	}

	params := make([]interface{}, 0)
	whereQuery := make([]string, 0)

	whereQuery = append(whereQuery, "admin_fkid = ?")
	params = append(params, adminId)

	if galleryId > 0 {
		whereQuery = append(whereQuery, "id = ?")
		params = append(params, galleryId)
	}

	if strings.TrimSpace(search) != "" {
		whereQuery = append(whereQuery, "caption like ?")
		params = append(params, "%"+search+"%")
	}

	if len(categoryIds) > 0 {
		whereQuery = append(whereQuery, "id in (select crm_gallery_fkid from crm_gallery_detail_category where crm_gallery_category_fkid in "+db.WhereIn(len(categoryIds))+" ) ")
		params = append(params, utils.ToInterfaceArray(categoryIds)...)
	}

	query = strings.Replace(query, "$where", strings.Join(whereQuery, " AND "), -1)

	//this should put last
	params = append(params, limit, page)

	galleries, err := db.QueryArray(query, params...)
	if err != nil {
		return nil, err
	}

	ids := make([]int, 0)
	for _, gallery := range galleries {
		ids = append(ids, cast.ToInt(gallery["id"]))
	}

	if len(ids) == 0 {
		return []map[string]interface{}{}, nil
	}

	return g.FetchGalleryDetail(ids)
}

func (g galleryRepository) AddGallery(input domain.Gallery, session domain.UserSession) error {
	return db.WithTransaction(func(tx db.Transaction) error {
		resp, err := tx.Insert("crm_gallery", map[string]interface{}{
			"admin_fkid":   session.AdminId,
			"member_fkid":  session.MemberId,
			"caption":      input.Caption,
			"time_created": time.Now().Unix() * 1000,
		})
		if err != nil {
			return err
		}
		crmGalleryId, _ := resp.LastInsertId()

		for _, detail := range input.Detail {
			resp, err = tx.Insert("crm_gallery_detail", map[string]interface{}{
				"crm_gallery_id": crmGalleryId,
				"image_url":      detail.ImageURL,
				"time_created":   time.Now().Unix() * 1000,
			})
			galleryDetailId, _ := resp.LastInsertId()

			for _, tag := range detail.Tags {
				_, err = tx.Insert("crm_gallery_detail_tags", map[string]interface{}{
					"crm_gallery_detail_id": galleryDetailId,
					"type":                  "product",
					"value":                 tag.Value,
					"position_x":            tag.PositionX,
					"position_y":            tag.PositionY,
					"time_created":          time.Now().Unix() * 1000,
					"time_updated":          time.Now().Unix() * 1000,
				})
			}
		}

		for _, categoryId := range input.CategoryIds {
			tx.Insert("crm_gallery_detail_category", map[string]interface{}{
				"crm_gallery_fkid":          crmGalleryId,
				"crm_gallery_category_fkid": categoryId,
			})
		}

		log.Info("add gallery success, id: %v | caption: %v", crmGalleryId, input.Caption)
		return err
	})
}

func (g galleryRepository) AddBookmark(galleryId int, userSession domain.UserSession) error {
	_, err := db.Insert("crm_gallery_bookmark", map[string]interface{}{
		"crm_gallery_fkid": galleryId,
		"member_fkid":      userSession.MemberId,
		"time_created":     time.Now().Unix(),
	})
	return err
}

func (g galleryRepository) RemoveBookmark(galleryId int, session domain.UserSession) error {
	_, err := db.Delete("crm_gallery_bookmark", "crm_gallery_fkid = ? and member_fkid = ?", galleryId, session.MemberId)
	return err
}

func (g galleryRepository) FetchProductByIds(productIds []int, user domain.UserSession) ([]map[string]interface{}, error) {
	if len(productIds) == 0 {
		return []map[string]interface{}{}, nil
	}

	whereIn := strings.Repeat("?,", len(productIds))
	whereIn = strings.TrimRight(whereIn, ",")

	param := make([]interface{}, 0)
	param = append(param, user.AdminId)
	param = append(param, utils.ToInterfaceArray(productIds)...)

	sql := `SELECT
	product_id,
	name,
	photo,
	description,
	min(pd.price_sell) AS price,
	min(pd.price_sell) AS price_start,
	max(pd.price_sell) AS price_end
FROM
	products p
	JOIN products_detail pd ON pd.product_fkid = p.product_id
WHERE
	admin_fkid = ? 
	and p.data_status='on' and p.product_id in (` + whereIn + `) 
	and p.app_show = 1
GROUP BY
	p.product_id
ORDER BY
	p.name`
	return db.QueryArray(sql, param...)
}

func (g galleryRepository) FetchBookmark(userSession domain.UserSession) ([]map[string]interface{}, error) {
	query := `
SELECT
	caption, crm_gallery_id, cgd.thumbnail, cg.time_created
FROM
	crm_gallery_bookmark cgm
	JOIN crm_gallery cg ON cg.id = cgm.crm_gallery_fkid
	JOIN (
		SELECT
			crm_gallery_id,
			ANY_VALUE(image_url) AS thumbnail
		FROM
			crm_gallery_detail
		GROUP BY
			crm_gallery_id) cgd ON cgd.crm_gallery_id = cg.id
WHERE
	cgm.member_fkid = ?
	AND cg.admin_fkid = ? 
ORDER BY cg.time_created desc `
	return db.QueryArray(query, userSession.MemberId, userSession.AdminId)
}

func (g galleryRepository) FetchGalleryDetail(galleryIds []int) ([]map[string]interface{}, error) {
	query := `
SELECT
    cg.*, cgd.*, cgdt.*, m.name, cgd.id as cgd_id, cgd.id as crm_gallery_detail_id, cg.time_created, gc.categories
FROM
    crm_gallery cg
JOIN crm_gallery_detail cgd ON
    cgd.crm_gallery_id = cg.id
LEFT JOIN crm_gallery_detail_tags cgdt ON
    cgdt.crm_gallery_detail_id = cgd.id 
left join members m on m.member_id=cg.member_fkid
left join (
	SELECT
	GROUP_CONCAT(cgc.name separator '[UNIQ]') AS categories,
	crm_gallery_fkid
FROM
	crm_gallery_detail_category cgdc
	JOIN crm_gallery_category cgc ON cgc.id = cgdc.crm_gallery_category_fkid
GROUP BY
	crm_gallery_fkid
) gc on gc.crm_gallery_fkid=cg.id
WHERE cg.id in ($whereIn) 
ORDER BY cg.time_created desc, cg.id, cgdt.crm_gallery_detail_id `

	whereIn := strings.Repeat("?,", len(galleryIds))
	whereIn = strings.TrimRight(whereIn, ",")
	query = strings.Replace(query, "$whereIn", whereIn, 1)

	return db.QueryArray(query, utils.ToInterfaceArray(galleryIds)...)
}

func (g galleryRepository) FetchMemberAccess(userSession domain.UserSession) (map[string]interface{}, error) {
	query := `
SELECT
	*
FROM
	members_access
WHERE
	member_fkid = ?
	AND admin_fkid = ?
ORDER BY
	members_access_id DESC
LIMIT 1`
	return db.Query(query, userSession.MemberId, userSession.AdminId)
}

func (g galleryRepository) FetchBookmarkTopItem(user domain.UserSession) ([]map[string]interface{}, error) {
	sql := `SELECT
	caption,
	cg.id AS crm_gallery_id,
	cgd.image_url AS thumbnail,
	COUNT(*) AS total
FROM
	crm_gallery_bookmark cgb
	JOIN crm_gallery cg ON cgb.crm_gallery_fkid = cg.id
	JOIN (
		SELECT
			ANY_VALUE(image_url) AS image_url,
			crm_gallery_id
		FROM
			crm_gallery_detail
		GROUP BY
			crm_gallery_id) cgd ON cgd.crm_gallery_id = cg.id
WHERE cg.admin_fkid = ? 
GROUP BY
	crm_gallery_fkid 
ORDER BY total desc `
	return db.QueryArray(sql, user.AdminId)
}

func (g galleryRepository) FetchBookmarkByGalleryIds(galleryIds []int, userSession domain.UserSession) ([]map[string]interface{}, error) {
	if len(galleryIds) == 0 {
		return []map[string]interface{}{}, nil
	}
	whereIn := strings.Repeat("?,", len(galleryIds))
	whereIn = strings.TrimRight(whereIn, ",")

	params := make([]interface{}, 0)
	params = append(params, userSession.MemberId)
	params = append(params, utils.ToInterfaceArray(galleryIds)...)

	sql := "SELECT * from crm_gallery_bookmark where member_fkid = ? and crm_gallery_fkid in (" + whereIn + ")"
	return db.QueryArray(sql, params...)
}

func (g galleryRepository) RemoveGallery(galleryId int, adminId int64) error {
	_, err := db.Delete("crm_gallery", "id = ? and admin_fkid = ?", galleryId, adminId)
	return err
}

func (g galleryRepository) UpdateGallery(galleryId int, input domain.Gallery, adminId int64) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		resp, err := tx.Update("crm_gallery", map[string]interface{}{
			"caption": input.Caption,
		}, "id = ? and admin_fkid = ?", galleryId, adminId)
		if err != nil {
			return err
		}

		rowsUpdated, _ := resp.RowsAffected()
		log.Info("total row updated: %v", rowsUpdated)

		//TODO: temporary solution, because older app version not sending detail data
		//this is to avoid deleting detail data
		if len(input.CategoryIds) == 0 {
			return nil
		}

		tx.Delete("crm_gallery_detail_category", "crm_gallery_fkid = ?", galleryId)
		if len(input.CategoryIds) > 0 {
			for _, id := range input.CategoryIds {
				tx.Insert("crm_gallery_detail_category", map[string]interface{}{
					"crm_gallery_fkid":          galleryId,
					"crm_gallery_category_fkid": id,
				})
			}
		}

		tx.Delete("crm_gallery_detail", "crm_gallery_id = ?", galleryId)
		for _, detail := range input.Detail {
			resp, err = tx.Insert("crm_gallery_detail", map[string]interface{}{
				"crm_gallery_id": galleryId,
				"image_url":      detail.ImageURL,
				"time_created":   time.Now().Unix() * 1000,
			})
			if err != nil {
				return err
			}
			galleryDetailId, _ := resp.LastInsertId()

			for _, tag := range detail.Tags {
				_, err = tx.Insert("crm_gallery_detail_tags", map[string]interface{}{
					"crm_gallery_detail_id": galleryDetailId,
					"type":                  "product",
					"value":                 tag.Value,
					"position_x":            tag.PositionX,
					"position_y":            tag.PositionY,
					"time_created":          time.Now().Unix() * 1000,
					"time_updated":          time.Now().Unix() * 1000,
				})
				if err != nil {
					return err
				}
			}
		}

		return nil
	})
	return err
}

func (g galleryRepository) UpdateGalleryCategory(id int, name string, user domain.UserSession) error {
	_, err := db.Update("crm_gallery_category", map[string]interface{}{
		"name": name,
	}, "id = ? and admin_fkid = ?", id, user.AdminId)
	return err
}

func (g galleryRepository) DeleteGalleryCategory(id int, user domain.UserSession) error {
	_, err := db.Delete("crm_gallery_category", "id = ? and admin_fkid = ?", id, user.AdminId)
	return err
}
