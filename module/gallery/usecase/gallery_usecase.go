package usecase

import (
	"fmt"
	"os"
	"sort"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type galleryUseCase struct {
	repo domain.GalleryRepository
}

func NewGalleryUseCase(repository domain.GalleryRepository) domain.GalleryUseCase {
	return &galleryUseCase{repository}
}

const (
	TmpGalleryPath = "temp/gallery-images"
	GalleryEncKey  = "Nd4KW9JA5Y8NYDP2n2VwXuK9UPmgVSzS"
)

func (g galleryUseCase) FetchGallery(galleryId int, user domain.UserSession, page int, search string, categoryIds []int) ([]map[string]interface{}, error) {
	//fetch gallery
	data, err := g.repo.FetchGallery(galleryId, user.AdminId, page, search, categoryIds)
	if err != nil {
		return nil, err
	}

	if len(data) == 0 && galleryId == 0 {
		return []map[string]interface{}{}, nil
	}

	//fetch detail

	productIds := make([]int, 0)
	galleryIds := make([]int, 0)
	for _, raw := range data {
		if raw["type"] != nil && cast.ToString(raw["type"]) == "product" {
			productIds = append(productIds, cast.ToInt(raw["value"]))
		}
		galleryIds = append(galleryIds, cast.ToInt(raw["crm_gallery_id"]))
	}

	products, err := g.repo.FetchProductByIds(productIds, user)
	if err != nil {
		return nil, err
	}

	log.Info("galleryIds: %v", galleryIds)
	galleryBookmarks, err := g.repo.FetchBookmarkByGalleryIds(galleryIds, user)
	log.IfError(err)

	galleryBookmarked := make(map[int]bool)
	for _, bookmark := range galleryBookmarks {
		galleryBookmarked[cast.ToInt(bookmark["crm_gallery_fkid"])] = true
	}
	log.Info("bookmarked: %v", galleryBookmarked)

	productsMap := make(map[int]map[string]interface{})
	for _, product := range products {
		productsMap[cast.ToInt(product["product_id"])] = product
	}

	result := make([]map[string]interface{}, 0)
	galleryTags := make([]map[string]interface{}, 0)
	galleryDetails := make([]map[string]interface{}, 0)

	for i, raw := range data {
		if raw["type"] != nil && cast.ToString(raw["type"]) == "product" {
			raw["value_detail"] = productsMap[cast.ToInt(raw["value"])]
		}

		//not all image has tag, only add tag that is not nil
		if raw["position_x"] != nil {
			galleryTags = append(galleryTags, utils.TakesOnly(raw, "type", "value", "position_x", "position_y", "value_detail"))
		}

		if i == len(data)-1 || raw["crm_gallery_detail_id"] != data[i+1]["crm_gallery_detail_id"] {
			raw["tags"] = galleryTags
			galleryDetails = append(galleryDetails, utils.TakesOnly(raw, "image_url", "tags", "crm_gallery_detail_id"))
			galleryTags = make([]map[string]interface{}, 0)
		}

		if i == len(data)-1 || raw["crm_gallery_id"] != data[i+1]["crm_gallery_id"] {
			sort.Slice(galleryDetails, func(i, j int) bool {
				return cast.ToInt(galleryDetails[i]["crm_gallery_detail_id"]) < cast.ToInt(galleryDetails[j]["crm_gallery_detail_id"])
			})
			raw["detail"] = galleryDetails
			raw["id"] = raw["crm_gallery_id"]
			raw["bookmarked"] = galleryBookmarked[cast.ToInt(raw["crm_gallery_id"])]
			if raw["categories"] != nil && cast.ToString(raw["categories"]) != "" {
				raw["categories"] = strings.Split(cast.ToString(raw["categories"]), "[UNIQ]") //[UNIQ] -> separator
			}
			result = append(result, utils.TakesOnly(raw, "caption", "time_created", "name", "detail", "id", "bookmarked", "categories"))
			galleryDetails = make([]map[string]interface{}, 0)
		}
	}

	return result, nil
}

func (g galleryUseCase) AddGallery(input domain.Gallery, userSession domain.UserSession) error {
	//validate input
	if len(input.Detail) == 0 {
		return fmt.Errorf("detail is required, at least one should be sent")
	}

	timeStart := time.Now()

	detail, err := g.uploadGalleryImages(input.Detail, userSession)
	if err != nil {
		return err
	}

	input.Detail = detail
	input.Caption = utils.ReplaceEmoji(input.Caption)

	if len(input.Categories) > 0 {
		//get category ids
		categories, _ := g.repo.Filter(domain.GalleryRepositoryFilter{
			Categories: input.Categories,
		}).FetchGalleryCategory(userSession.AdminId)

		categoryIds := make([]int64, 0)
		for _, name := range input.Categories {
			isExist := false
			for _, category := range categories {
				if name == cast.ToString(category["name"]) {
					categoryIds = append(categoryIds, utils.ToInt64(category["id"]))
					isExist = true
					break
				}
			}

			if !isExist {
				id, err := g.repo.AddGalleryCategory(name, userSession.AdminId)
				if err == nil {
					categoryIds = append(categoryIds, id)
				}
			}
		}
		input.CategoryIds = categoryIds
	}

	log.Info("upload %d images took : %v", len(input.Detail), time.Since(timeStart))
	return g.repo.AddGallery(input, userSession)
}

func (g galleryUseCase) AddBookmark(galleryId int, userSession domain.UserSession) error {
	return g.repo.AddBookmark(galleryId, userSession)
}

func (g galleryUseCase) RemoveBookmark(galleryId int, userSession domain.UserSession) error {
	return g.repo.RemoveBookmark(galleryId, userSession)
}

func (g galleryUseCase) FetchBookmark(userSession domain.UserSession) ([]map[string]interface{}, error) {
	return g.repo.FetchBookmark(userSession)
}

func (g galleryUseCase) FetchBookmarkTopItem(userSession domain.UserSession) ([]map[string]interface{}, error) {
	//only admin can fetch this data
	memberAccess, err := g.repo.FetchMemberAccess(userSession)
	if log.IfError(err) {
		return nil, err
	}

	if cast.ToString(memberAccess["access_type"]) != domain.AccessAdmin {
		return nil, fmt.Errorf("only admin allowed to fetch the request")
	}

	return g.repo.FetchBookmarkTopItem(userSession)
}

func (g galleryUseCase) RemoveGallery(galleryId int, userSession domain.UserSession) error {
	//only admin can fetch this data
	memberAccess, err := g.repo.FetchMemberAccess(userSession)
	if log.IfError(err) {
		return err
	}

	if cast.ToString(memberAccess["access_type"]) != domain.AccessAdmin {
		return fmt.Errorf("only admin allowed to make the request")
	}

	return g.repo.RemoveGallery(galleryId, userSession.AdminId)
}

func (g galleryUseCase) UpdateGallery(galleryId int, input domain.Gallery, userSession domain.UserSession) error {
	//only admin can make this request
	memberAccess, err := g.repo.FetchMemberAccess(userSession)
	if log.IfError(err) {
		return err
	}

	if cast.ToString(memberAccess["access_type"]) != domain.AccessAdmin {
		return fmt.Errorf("only admin allowed to make the request")
	}

	if len(input.Categories) > 0 {
		categories, _ := g.repo.Filter(domain.GalleryRepositoryFilter{Categories: input.Categories}).FetchGalleryCategory(userSession.AdminId)
		input.CategoryIds = make([]int64, 0)
		categoryIds := make(map[int64]bool)
		for _, category := range categories {
			categoryIds[utils.ToInt64(category["id"])] = true

			//remove from list
			for i, catName := range input.Categories {
				if catName == cast.ToString(category["name"]) {
					input.Categories[i] = ""
					break
				}
			}
		}

		for key := range categoryIds {
			input.CategoryIds = append(input.CategoryIds, key)
		}

		//check what remains from list
		for _, catName := range input.Categories {
			if catName != "" {
				categoryId, err := g.repo.AddGalleryCategory(catName, userSession.AdminId)
				if err != nil {
					continue
				}
				input.CategoryIds = append(input.CategoryIds, categoryId)
			}
		}
	}

	//upload image
	detail, err := g.uploadGalleryImages(input.Detail, userSession)
	if err != nil {
		return err
	}

	input.Detail = detail
	input.Caption = utils.ReplaceEmoji(input.Caption)

	return g.repo.UpdateGallery(galleryId, input, userSession.AdminId)
}

func (g galleryUseCase) uploadGalleryImages(detailInput []domain.Detail, user domain.UserSession) ([]domain.Detail, error) {
	for _, detail := range detailInput {
		if detail.ImageId == "" && strings.HasPrefix(detail.ImageURL, "https://") {
			continue
		}
		fileName := utils.Decrypt(detail.ImageId, GalleryEncKey)
		file := fmt.Sprintf("%s/%s", TmpGalleryPath, fileName)
		if _, err := os.Stat(file); err != nil {
			return detailInput, fmt.Errorf("image_id not found: %s", file)
		}
	}

	for i, detail := range detailInput {
		if detail.ImageId == "" && strings.HasPrefix(detail.ImageURL, "https://") {
			continue
		}

		fileName := utils.Decrypt(detail.ImageId, GalleryEncKey)
		fileNameFull := fmt.Sprintf("%s/%s", TmpGalleryPath, fileName)
		fileTmp, err := os.Open(fileNameFull)
		log.IfError(err)

		url, err := google.UploadFile(fileTmp, fmt.Sprintf("crm/gallery/%d/%d/%s", user.AdminId, user.MemberId, fileName), true)
		if log.IfError(err) {
			return detailInput, err
		}

		detailInput[i].ImageURL = url
		log.IfError(fileTmp.Close())
		log.IfError(os.Remove(fileNameFull))
	}

	return detailInput, nil
}

func (g galleryUseCase) FetchGalleryCategory(query domain.GalleryRepositoryFilter, user domain.UserSession) ([]map[string]interface{}, error) {
	return g.repo.Filter(domain.GalleryRepositoryFilter{Active: query.Active}).FetchGalleryCategory(user.AdminId)
}

func (g galleryUseCase) UpdateGalleryCategory(id int, name string, user domain.UserSession) error {
	//only admin can make this request
	memberAccess, err := g.repo.FetchMemberAccess(user)
	if log.IfError(err) {
		return err
	}

	if cast.ToString(memberAccess["access_type"]) != domain.AccessAdmin {
		return fmt.Errorf("only admin allowed to make the request")
	}

	return g.repo.UpdateGalleryCategory(id, name, user)
}

func (g galleryUseCase) DeleteGalleryCategory(id int, user domain.UserSession) error {
	//only admin can make this request
	memberAccess, err := g.repo.FetchMemberAccess(user)
	if log.IfError(err) {
		return err
	}

	if cast.ToString(memberAccess["access_type"]) != domain.AccessAdmin {
		return fmt.Errorf("only admin allowed to make the request")
	}

	return g.repo.DeleteGalleryCategory(id, user)
}

func (g galleryUseCase) AddGalleryCategory(categoryName string, user domain.UserSession) error {
	//only admin can make this request
	memberAccess, err := g.repo.FetchMemberAccess(user)
	if log.IfError(err) {
		return err
	}

	if cast.ToString(memberAccess["access_type"]) != domain.AccessAdmin {
		return fmt.Errorf("only admin allowed to make the request")
	}

	_, err = g.repo.AddGalleryCategory(categoryName, user.AdminId)
	return err
}
