package http

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/gallery/usecase"
)

type galleryHandler struct {
	uc domain.GalleryUseCase
}

func NewHttpGalleryHandler(app *fasthttprouter.Router, useCase domain.GalleryUseCase) {
	handler := &galleryHandler{useCase}
	app.GET("/v1/gallery", auth.ValidatePublicKey(handler.FetchGallery))
	app.GET("/v1/gallery/:id", auth.ValidatePublicKey(handler.FetchGallery))
	app.POST("/v1/gallery", auth.ValidateToken(handler.AddGallery))
	app.PUT("/v1/gallery/:id", auth.ValidateToken(handler.UpdateGallery))
	app.DELETE("/v1/gallery/:id", auth.ValidateToken(handler.RemoveGallery))

	app.POST("/v1/gallery-upload", auth.ValidateToken(handler.UploadGallery))

	//bookmark
	app.POST("/v1/gallery/:id/bookmark", auth.ValidateToken(handler.AddBookmark))
	app.DELETE("/v1/gallery/:id/bookmark", auth.ValidateToken(handler.RemoveBookmark))
	app.GET("/v1/gallery-bookmark", auth.ValidateToken(handler.FetchBookmark))

	app.GET("/v1/gallery-bookmark-statistic", auth.ValidateToken(handler.FetchBookmarkStatistic))

	//category
	app.GET("/v1/gallery-category", auth.ValidatePublicKey(handler.FetchGalleryCategory))
	app.POST("/v1/gallery-category", auth.ValidateToken(handler.AddGalleryCategory))
	app.PUT("/v1/gallery-category/:id", auth.ValidateToken(handler.UpdateGalleryCategory))
	app.DELETE("/v1/gallery-category/:id", auth.ValidateToken(handler.DeleteGalleryCategory))
}

func (h galleryHandler) FetchGallery(ctx *fasthttp.RequestCtx) {
	galleryId := cast.ToInt(ctx.UserValue("id"))
	user := domain.UserSessionFastHttp(ctx)
	page := string(ctx.QueryArgs().Peek("page"))
	search := strings.TrimSpace(string(ctx.QueryArgs().Peek("search")))
	category := strings.TrimSpace(string(ctx.QueryArgs().Peek("categories")))

	categoryIds := make([]int, 0)
	if category != "" {
		for _, id := range strings.Split(category, ",") {
			categoryIds = append(categoryIds, cast.ToInt(id))
		}
	}

	log.Info("FetchGallery: %v | id: %v", ctx.QueryArgs().String(), galleryId)
	result, err := h.uc.FetchGallery(galleryId, user, cast.ToInt(page), search, categoryIds)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	if galleryId > 0 {
		if len(result) > 0 {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result[0]})
		} else {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
		}
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h galleryHandler) AddGallery(ctx *fasthttp.RequestCtx) {
	var input domain.Gallery
	err := json.Unmarshal(ctx.PostBody(), &input)
	log.IfError(err)
	log.Info("add gallery: %s", utils.SimplyToJson(input))

	user := domain.UserSessionFastHttp(ctx)
	err = h.uc.AddGallery(input, user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}

func (h galleryHandler) AddBookmark(ctx *fasthttp.RequestCtx) {
	galleryId := cast.ToInt(ctx.UserValue("id"))
	err := h.uc.AddBookmark(galleryId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h galleryHandler) UpdateGallery(ctx *fasthttp.RequestCtx) {
	galleryId := cast.ToInt(ctx.UserValue("id"))
	var input domain.Gallery
	err := json.Unmarshal(ctx.PostBody(), &input)
	log.IfError(err)
	log.Info("update gallery (%v) -> %s", galleryId, utils.SimplyToJson(input))

	err = h.uc.UpdateGallery(galleryId, input, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h galleryHandler) RemoveGallery(ctx *fasthttp.RequestCtx) {
	galleryId := cast.ToInt(ctx.UserValue("id"))
	err := h.uc.RemoveGallery(galleryId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}

func (h galleryHandler) RemoveBookmark(ctx *fasthttp.RequestCtx) {
	galleryId := cast.ToInt(ctx.UserValue("id"))
	err := h.uc.RemoveBookmark(galleryId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}

func (h galleryHandler) UploadGallery(ctx *fasthttp.RequestCtx) {
	result := make([]map[string]interface{}, 0)

	formFile, _ := ctx.MultipartForm()
	for _, file := range formFile.File["image"] {
		log.Info("%s - %v KB", file.Filename, int(file.Size/1000))
		_, err := ctx.FormFile("image")
		log.IfError(err)

		basePath := usecase.TmpGalleryPath
		_, err = os.Stat(basePath)
		if os.IsNotExist(err) {
			err = os.MkdirAll(basePath, os.ModePerm)
			log.IfError(err)
		}

		fileName := fmt.Sprintf("%v_%s", time.Now().UnixNano(), file.Filename)
		id := utils.Encrypt(fileName, usecase.GalleryEncKey)

		tmpFileName := fmt.Sprintf("%s/%s", basePath, fileName)
		err = fasthttp.SaveMultipartFile(file, tmpFileName)
		log.IfError(err)

		result = append(result, map[string]interface{}{
			"image": file.Filename,
			"id":    id,
		})
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h galleryHandler) FetchBookmark(ctx *fasthttp.RequestCtx) {
	topItem := string(ctx.QueryArgs().Peek("format")) == "top-item"
	user := domain.UserSessionFastHttp(ctx)

	var result []map[string]interface{}
	var err error
	if topItem {
		result, err = h.uc.FetchBookmarkTopItem(user)
	} else {
		result, err = h.uc.FetchBookmark(user)
	}

	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h galleryHandler) FetchBookmarkStatistic(ctx *fasthttp.RequestCtx) {

}

func (h galleryHandler) AddGalleryCategory(ctx *fasthttp.RequestCtx) {
	categoryName := string(ctx.PostArgs().Peek("name"))
	err := h.uc.AddGalleryCategory(categoryName, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h galleryHandler) FetchGalleryCategory(ctx *fasthttp.RequestCtx) {
	active := string(ctx.QueryArgs().Peek("active"))
	filter := domain.GalleryRepositoryFilter{Active: active == "true"}

	result, err := h.uc.FetchGalleryCategory(filter, domain.UserSessionFastHttp(ctx))

	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h galleryHandler) UpdateGalleryCategory(ctx *fasthttp.RequestCtx) {
	id := cast.ToInt(ctx.UserValue("id"))
	categoryName := string(ctx.FormValue("name"))

	if id == 0 || categoryName == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	err := h.uc.UpdateGalleryCategory(id, categoryName, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}

func (h galleryHandler) DeleteGalleryCategory(ctx *fasthttp.RequestCtx) {
	id := cast.ToInt(ctx.UserValue("id"))
	if id == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	err := h.uc.DeleteGalleryCategory(id, domain.UserSessionFastHttp(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true})
}
