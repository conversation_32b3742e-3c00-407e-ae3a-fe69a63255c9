package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

type outletUseCase struct {
	repo            domain.OutletRepository
	repoTransaction transaction.Repository
}

func NewOutletUseCase(repository domain.OutletRepository, repoTransaction transaction.Repository) domain.OutletUseCase {
	return &outletUseCase{repository, repoTransaction}
}

func (o *outletUseCase) FetchOutlet(user domain.UserSession, latLng string) ([]map[string]interface{}, error) {
	logTime := log.TimeInit()
	defer logTime.Print()

	outlets, err := o.repo.FetchOutlet(cast.ToInt(user.AdminId))
	logTime.AddLog(fmt.Sprintf("fetch outlet, size %v", len(outlets)))
	if err != nil || len(outlets) == 0 {
		return outlets, err
	}

	outletIds := make([]int, 0)
	for _, outlet := range outlets {
		outletIds = append(outletIds, cast.ToInt(outlet["outlet_id"]))
	}

	workingHours, err := o.repo.FetchWorkingHours(outletIds...)
	if err != nil {
		return outlets, err
	}

	logTime.AddLog("fetch working hour")
	orderTypes, err := o.repo.FetchOrderTypes(outletIds...)
	log.IfError(err)
	log.Info("orderTypes size: %v, %v", len(orderTypes), utils.SimplyToJson(orderTypes))

	// orderTypeMap := array.FlatMapArray(orderTypes, "outlet_fkid")
	orderTypeMap := make(map[int]map[string]interface{})
	defaultOrderType := map[string]interface{}{
		"self_order": "off",
		"delivery":   "off",
		"pickup":     "off",
	}

	for _, orderType := range orderTypes {
		if _, ok := orderTypeMap[cast.ToInt(orderType["outlet_fkid"])]; !ok {
			orderTypeMap[cast.ToInt(orderType["outlet_fkid"])] = array.Copy(defaultOrderType)
		}
		orderTypeMap[cast.ToInt(orderType["outlet_fkid"])][cast.ToString(orderType["order_type"])] = "on"
	}

	//try to get the orderType of first outlet, if not exist, log all orderType
	if orderType, ok := orderTypeMap[cast.ToInt(outlets[0]["outlet_id"])]; !ok {
		log.Info("orderType of first outlet not found, orderTypeMap: %v", utils.SimplyToJson(orderTypeMap))
	} else {
		log.Info("#orderType of first outlet: %v", utils.SimplyToJson(orderType))
	}

	workingHourByOutlet := make(map[int][]models.WorkingHourEntity)
	for _, hour := range *workingHours {
		if _, ok := workingHourByOutlet[hour.OutletFkid]; !ok {
			workingHourByOutlet[hour.OutletFkid] = make([]models.WorkingHourEntity, 0)
		}
		workingHourByOutlet[hour.OutletFkid] = append(workingHourByOutlet[hour.OutletFkid], hour)
	}

	latLangs := make([]string, 0)
	latLangsOutletId := make([]int, 0)
	for i, outlet := range outlets {
		if times, ok := workingHourByOutlet[cast.ToInt(outlet["outlet_id"])]; ok {
			outlets[i]["business_hour"] = times
		} else {
			outlets[i]["business_hour"] = getDefaultBusinessHours(cast.ToInt(outlet["outlet_id"]))
		}
		outlets[i]["receipt_logo"] = outlet["outlet_logo"]

		orderType := orderTypeMap[cast.ToInt(outlet["outlet_id"])]
		// log.Info("#orderType '%v' (%v) -> %v", outlet["name"], cast.ToString(outlet["outlet_id"]), orderType)
		//use default order type if not set
		if len(orderType) == 0 {
			orderType = map[string]interface{}{
				"self_order": "on",
			}
		}

		outlets[i]["order_type"] = orderType
		// outlets[i]["order_type"] = map[string]interface{}{
		// 	"self_order": "on",
		// 	"delivery":   orderType["delivery"],
		// 	"pickup":     orderType["pickup"],
		// }

		if lat, lng := outlet["latitude"], outlet["longitude"]; lat != nil && lng != nil && lat != "0.00" {
			latLangs = append(latLangs, strings.Join([]string{cast.ToString(lat), cast.ToString(lng)}, ","))
			latLangsOutletId = append(latLangsOutletId, cast.ToInt(outlet["outlet_id"]))
		}

		if feature := cast.ToString(outlet["feature"]); feature != "" {
			var outletFeature models.OutletFeature
			log.IfError(json.Unmarshal([]byte(feature), &outletFeature))
			outlets[i]["feature"] = outletFeature
		}
	}

	if strings.TrimSpace(latLng) == "" || len(latLangs) == 0 {
		return outlets, nil
	}

	//calculate distance
	distances, err := o.calculateDistance(latLng, latLangs...)
	if err != nil || len(distances.Rows) == 0 || len(distances.Rows[0].Elements) == 0 {
		return outlets, nil
	}

	logTime.AddLog("calculate distance")
	for i, raw := range distances.Rows[0].Elements {
		location := strings.Split(latLangs[i], ",")
		if len(location) != 2 {
			continue
		}
		for j, outlet := range outlets {
			if cast.ToString(outlet["latitude"]) == location[0] && cast.ToString(outlet["longitude"]) == location[1] && outlets[j]["distance"] == nil {
				outlets[j]["distance"] = raw.Distance.Text
				outlets[j]["distance_value"] = raw.Distance.Value
				break
			}
		}
	}

	logTime.AddLog("set distance")
	sort.SliceStable(outlets, func(i, j int) bool {
		return cast.ToInt(outlets[i]["distance_value"]) < cast.ToInt(outlets[j]["distance_value"])
	})

	return outlets, nil
}

func (o *outletUseCase) calculateDistance(origin string, destinations ...string) (models.DistanceMatrix, error) {
	API_KEY := os.Getenv("MAP_DISTANCE_MATRIX_KEY")
	if API_KEY == "" {
		// return models.DistanceMatrix{}, nil
		return o.calculateDistanceManual(origin, destinations...)
	}

	if os.Getenv("DISBALE_DISTANCE_MATRIX_API") == "true" {
		return o.calculateDistanceManual(origin, destinations...)
	}

	log.Info("calculate distance with Google Map Distance Matrix API")

	//validation
	latLngDestinations := make([]string, 0)
	for _, destination := range destinations {
		latLng := strings.Split(destination, ",")
		if len(latLng) != 2 {
			log.Info("invalid latLang: %v", destination)
			continue
		}

		if cast.ToFloat(latLng[0]) == 0.0 || cast.ToFloat(latLng[1]) == 0.0 {
			log.Info("invalid latLng: %v", destination)
			continue
		}
		latLngDestinations = append(latLngDestinations, destination)
	}

	log.Info("destination: %v", strings.Join(latLngDestinations, ","))
	req := utils.HttpRequest{
		Method: "GET",
		Url:    "https://maps.googleapis.com/maps/api/distancematrix/json",
		Params: map[string]interface{}{
			"origins":      origin,
			"destinations": strings.Join(latLngDestinations, "|"),
			"key":          API_KEY,
		},
	}
	response, err := req.ExecuteRequest()
	if err != nil {
		return models.DistanceMatrix{}, err
	}

	var result models.DistanceMatrix
	err = json.Unmarshal(response, &result)
	return result, err
}

func (i outletUseCase) calculateDistanceManual(origin string, destinations ...string) (models.DistanceMatrix, error) {
	log.Info("calculate distance manually... %v destinations, from %v", len(destinations), origin)
	latLngOrigin := strings.Split(origin, ",")
	if len(latLngOrigin) != 2 {
		log.IfError(fmt.Errorf("failed to parse origin latLng: %v", origin))
		return models.DistanceMatrix{}, nil
	}

	resultElement := make([]models.Elements, 0)

	latOrigin, lngOrigin := cast.ToFloat(latLngOrigin[0]), cast.ToFloat(latLngOrigin[1])
	for _, destination := range destinations {
		latLng := strings.Split(destination, ",")
		if len(latLng) != 2 {
			log.Info("invalid latLang: %v", destination)
			continue
		}

		distance := utils.Distance(latOrigin, lngOrigin, cast.ToFloat(latLng[0]), cast.ToFloat(latLng[1]), "K")
		resultElement = append(resultElement, models.Elements{Distance: models.Distance{Text: fmt.Sprintf("%.1f km", distance), Value: cast.ToInt(distance)}})
	}

	result := models.DistanceMatrix{Rows: []models.Rows{
		{Elements: resultElement},
	}}

	return result, nil
}

func (o *outletUseCase) FetchOutletByIds(outletIds ...int) ([]map[string]interface{}, error) {
	outletData, err := o.repo.FetchOutletByIds(outletIds...)
	if err != nil {
		return nil, err
	}

	outlets := make([]map[string]interface{}, 0)
	for _, outlet := range outletData {
		outlets = append(outlets, outlet.ToMap())
	}

	workingOurs, err := o.repo.FetchWorkingHours(outletIds...)
	if err != nil {
		return outlets, err
	}

	if len(*workingOurs) == 0 {
		for i := range outlets {
			outlets[i]["enable_order"] = map[string]interface{}{
				"status": "enable",
			}
		}
	} else {
		dateNow := time.Now().Add(7 * time.Hour)
		today := strings.ToLower(dateNow.Weekday().String())
		tNow, err := time.Parse("15:4:5", fmt.Sprintf("%d:%d:%d", dateNow.Hour(), dateNow.Minute(), dateNow.Day()))
		log.IfError(err)

		workingHourByOutlet := make(map[int]models.WorkingHourEntity)
		for _, hour := range *workingOurs {
			if hour.Day == today {
				workingHourByOutlet[hour.OutletFkid] = hour
			}
		}

		for i, outlet := range outlets {
			// workingHour := make(map[string]interface{})
			// for _, hour := range *workingOurs {
			// 	if hour.OutletFkid == cast.ToInt(outlet["outlet_id"]) && hour.Day == today {
			// 		workingHour = hour
			// 		break
			// 	}
			// }

			workingHour := workingHourByOutlet[cast.ToInt(outlet["outlet_id"])]
			if workingHour.OutletFkid > 0 {
				tOpen, err := time.Parse("15:04:05", workingHour.TimeOpen)
				log.IfError(err)

				tClose, err := time.Parse("15:04:05", workingHour.TimeClose)
				log.IfError(err)

				if tNow.Before(tOpen) || tNow.After(tClose) {
					outlets[i]["enable_order"] = map[string]interface{}{
						"status": "disable",
						"reason": "closed",
					}
				} else {
					outlets[i]["enable_order"] = map[string]interface{}{
						"status": "enable",
					}
				}
			} else {
				outlets[i]["enable_order"] = map[string]interface{}{
					"status": "disable",
					"reason": "close",
				}
			}
		}
	}

	return outlets, nil
}

// GetOutletDistance implements domain.OutletUseCase.
func (o *outletUseCase) GetOutletDistance(request domain.OutletDistanceRequest, user domain.UserSession) (map[string]interface{}, error) {
	//validate data
	if request.Latitude == 0 || request.Longitude == 0 {
		return nil, fmt.Errorf("latitude or longitude must be sent")
	}

	outlet, err := o.repo.FetchOutletByIds(request.OutletId)
	if log.IfError(err) || len(outlet) == 0 {
		log.Info("outletSize: %v,  request: %v", len(outlet), utils.SimplyToJson(request))
		return nil, err
	}

	//check if latLong outlet is set
	lat := outlet[0].Latitude
	lng := outlet[0].Longitude
	if lat == 0 || lng == 0 {
		log.Info("outlet LatLng not set: %v", utils.SimplyToJson(outlet))
		return map[string]interface{}{
			"distance": 0.0,
			"unit":     "km",
			"info":     "lat lng outlet not set",
		}, nil
	}

	distance := utils.Distance(lat, lng, request.Latitude, request.Longitude, "K")
	return map[string]interface{}{
		"distance": distance,
		"unit":     "km",
	}, nil
}

// GetDeliveryPrice implements domain.OutletUseCase.
func (o *outletUseCase) GetDeliveryPrice(request domain.OutletDeliveryPriceRequest, user domain.UserSession) (map[string]interface{}, error) {
	log.Info("get GetDeliveryPrice: %v", utils.SimplyToJson(request))
	//validate data
	if request.Latitude == 0 || request.Longitude == 0 {
		return nil, fmt.Errorf("latitude or longitude must be sent")
	}

	outlet, err := o.repo.FetchOutletByIds(request.OutletId)
	if log.IfError(err) || len(outlet) == 0 {
		log.Info("outletSize: %v,  request: %v", len(outlet), utils.SimplyToJson(request))
		return nil, err
	}

	//check if latLong outlet is set
	lat := outlet[0].Latitude
	lng := outlet[0].Longitude
	if lat == 0 || lng == 0 {
		log.Info("outlet LatLng not set for outlet: %v", utils.SimplyToJson(outlet))
		return nil, fmt.Errorf("lat lng outlet not set")
	}

	configs, err := o.repoTransaction.FetchOrderConfig(user, domain.OrderConfigInternalDelivery)
	if log.IfError(err) {
		return nil, err
	}
	if len(configs) == 0 {
		return nil, fmt.Errorf("config not set")
	}

	var deliveryConf map[string]interface{}
	err = json.Unmarshal([]byte(configs[0].Value), &deliveryConf)
	if log.IfError(err) {
		return nil, err
	}

	distance := utils.Distance(lat, lng, request.Latitude, request.Longitude, "K")
	//if below free threshold
	if distance <= cast.ToFloat(deliveryConf["threshold"]) {
		return map[string]interface{}{
			"price":    0,
			"currency": "IDR",
			"distance": map[string]interface{}{
				"distance": distance,
				"unit":     "km",
			},
			"breakdown": deliveryConf,
		}, nil
	}

	priceTotal := (distance - cast.ToFloat(deliveryConf["threshold"])) * cast.ToFloat(deliveryConf["price"])
	return map[string]interface{}{
		"price":    int(priceTotal),
		"currency": "IDR",
		"distance": map[string]interface{}{
			"distance": distance,
			"unit":     "km",
		},
		"breakdown": deliveryConf,
	}, nil
}

// FetchBanner implements domain.OutletUseCase.
func (o *outletUseCase) FetchBanner(user domain.UserSession) ([]map[string]interface{}, error) {
	result, err := o.repo.FetchBanner(int(user.AdminId))
	if len(result) > 0 || log.IfError(err) {
		return result, err
	}

	//use deals as default banner
	return o.repo.FetchDealsAsBanner(user.AdminId)
}
