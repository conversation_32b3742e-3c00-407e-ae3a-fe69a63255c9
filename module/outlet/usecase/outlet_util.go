package usecase

func getDefaultBusinessHours(outletId int) []map[string]interface{} {
	days := []string{"sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"}

	hours := make([]map[string]interface{}, len(days))
	for i, d := range days {
		hours[i] = map[string]interface{}{
			"outlet_fkid": outletId,
			"day":         d,
			"time_open":   "09:00:00",
			"time_close":  "21:30:00",
		}
	}

	return hours
}
