package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type outletHandler struct {
	uc domain.OutletUseCase
}

func NewHttpOutletHandler(app *fasthttprouter.Router, useCase domain.OutletUseCase) {
	handler := &outletHandler{useCase}

	// app.OPTIONS("/v1/outlet", auth.EnableCors)
	// app.GET("/v1/outlet", auth.MultiMiddleware(handler.FetchOutlet, auth.CORS, auth.ValidatePublicKey))
	app.GET("/v1/outlet", auth.ValidatePublicKey(handler.FetchOutlet))

	//----- OLD ROUTING -----

	// app.OPTIONS("/v1/outlet", auth.EnableCors)
	// app.GET("/v1/outlet", auth.MultiMiddleware(v1.GetOutlet, auth.CORS, auth.ValidatePublicKey))

	//router.GET("/v1/outlet/detail/:id", auth.ValidatePublicKey(v1.GetDetailOutlet))
	app.GET("/v1/outlet/detail/:id", auth.MultiMiddleware(v1.GetDetailOutlet, auth.CORS, auth.ValidatePublicKey))
	app.OPTIONS("/v1/outlet/detail/:id", auth.EnableCors)

	//utils
	app.GET("/v2/outlet/:id/distance", auth.ValidatePublicKey(handler.GetOutletDistance))
	app.GET("/v2/outlet/:id/delivery/price", auth.ValidatePublicKey(handler.GetDeliveryPrice))

	//banner
	app.GET("/v1/app/banner", auth.ValidatePublicKey(handler.FetchBanner))
}

func (h *outletHandler) FetchOutlet(ctx *fasthttp.RequestCtx) {
	latlngUser := string(ctx.QueryArgs().Peek("latlng"))
	result, err := h.uc.FetchOutlet(domain.UserSessionFastHttp(ctx), latlngUser)

	if err != nil {
		log.Info("FetchOutlet err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *outletHandler) GetOutletDistance(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.GetOutletDistance(domain.OutletDistanceRequest{
		Latitude:  cast.ToFloat(ctx.QueryArgs().Peek("latitude")),
		Longitude: cast.ToFloat(ctx.QueryArgs().Peek("longitude")),
		OutletId:  cast.ToInt(ctx.UserValue("id")),
	}, domain.UserSessionFastHttp(ctx))

	if err != nil {
		log.Info("GetOutletDistance err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *outletHandler) GetDeliveryPrice(ctx *fasthttp.RequestCtx) {
	request := domain.OutletDeliveryPriceRequest{
		OutletDistanceRequest: domain.OutletDistanceRequest{
			OutletId:  cast.ToInt(ctx.UserValue("id")),
			Latitude:  cast.ToFloat(ctx.QueryArgs().Peek("latitude")),
			Longitude: cast.ToFloat(ctx.QueryArgs().Peek("longitude")),
		},
	}

	result, err := h.uc.GetDeliveryPrice(request, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("GetDeliveryPrice err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h *outletHandler) FetchBanner(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchBanner(domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("FetchBanner err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
