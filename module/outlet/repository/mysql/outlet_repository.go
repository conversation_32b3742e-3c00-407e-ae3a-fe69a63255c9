package mysql

import (
	"database/sql"
	"fmt"
	"time"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type outletRepository struct {
	db db.Repository
}

func NewMysqlOutletRepository(conn *sql.DB, cache domain.CacheInterface) domain.OutletRepository {
	return &outletRepository{db.Repository{Conn: conn, CacheDb: cache}}
}

func (o outletRepository) FetchOutlet(adminId int) ([]map[string]interface{}, error) {
	sql := `SELECT outlet_id, name, address, phone, country, province, city, postal_code, 
	receipt_logo, outlet_logo, latitude, longitude, feature  FROM outlets WHERE admin_fkid = ? and data_status = 'on' and app_show = 1 
	order by name`

	return o.db.QueryCache("outlet", 8*time.Hour, sql, adminId).ArrayMap()
	// return db.QueryArray(sql, adminId)
}

func (o outletRepository) FetchOutletByIds(outletIds ...int) ([]domain.OutletResponse, error) {
	var result []domain.OutletResponse
	if len(outletIds) == 0 {
		fmt.Println("no outletIds given...")
		return result, nil
	}
	// sql := "select name,address,city,country,outlet_id,phone,province from outlets where app_show = 1 and outlet_id in " + db.WhereIn(len(outletIds))
	sql := `select name,address,city,country,outlet_id,phone,province,latitude, longitude 
	from outlets 
	where app_show = 1 and outlet_id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": outletIds,
	})

	err := o.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (o outletRepository) FetchWorkingHours(outletIds ...int) (*[]models.WorkingHourEntity, error) {
	var result []models.WorkingHourEntity
	if len(outletIds) == 0 {
		return &result, nil
	}

	sql := "select * from outlets_workinghour where outlet_fkid in @outletIds "
	sql, params := db.MapParam(sql, map[string]interface{}{
		"outletIds": outletIds,
	})

	err := o.db.QueryCache("working_hours", 9*time.Hour, sql, params...).Model(&result)
	if err == nil {
		return &result, nil
	}

	// fallback to old method
	err = o.db.Prepare(sql, utils.ToInterfaceArray(outletIds)...).Get(&result)
	return &result, err
}

func (o outletRepository) FetchOrderTypes(outletIds ...int) ([]map[string]interface{}, error) {
	sql := `select order_type,outlet_fkid  from order_configuration_detail_ordertype_availability`
	if len(outletIds) > 0 {
		sql += " where outlet_fkid in @outletIds "
	}
	sql, params := db.MapParam(sql, map[string]interface{}{
		"outletIds": outletIds,
	})
	return db.QueryArray(sql, params...)
}

func (o outletRepository) FetchOutletName(ids []int) (*[]models.OutletEntity, error) {
	sql := `select name, outlet_id from outlets where outlet_id in @ids`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})
	var result []models.OutletEntity
	err := o.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

// FetchBanner implements domain.OutletRepository.
func (o *outletRepository) FetchBanner(adminId int) ([]map[string]interface{}, error) {
	sql := `SELECT * FROM crm_banner 
	WHERE admin_fkid = ? and status=1 and data_status='on' 
	order by COALESCE(position, data_created) `
	return o.db.QueryCache("banner", 8*time.Hour, sql, adminId).ArrayMap()
}

// FetchDealsAsBanner implements domain.OutletRepository.
func (o *outletRepository) FetchDealsAsBanner(adminId int64) ([]map[string]interface{}, error) {
	query := `SELECT photo from promotions 
		where admin_fkid=? 
		and end_promotion_date > UNIX_TIMESTAMP()*1000 
		and promotion_type_id=15 
		and (photo is not null and photo != '')`
	return o.db.QueryCache("dealsBanner", 8*time.Hour, query, adminId).ArrayMap()
}
