package mysql

import (
	"database/sql"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/module/helper"
)

type helperRepository struct {
	db db.Repository
}

func NewMysqlHelperRepository(conn *sql.DB) helper.HelperRepository {
	return &helperRepository{db.Repository{Conn: conn}}
}

func (h helperRepository) FetchAppInfo(adminId int64) (map[string]interface{}, error) {
	sql := "select * from crm_app where admin_fkid = ?"
	return db.Query(sql, adminId)
}
