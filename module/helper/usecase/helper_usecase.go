package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/helper"
)

type helperUseCase struct {
	repo helper.HelperRepository
}

func NewHelperUseCase(repository helper.HelperRepository) domain.HelperUseCase {
	return &helperUseCase{repository}
}

func (h helperUseCase) FetchLocation(latLng string) (map[string]string, error) {
	if locs := strings.Split(latLng, ","); len(locs) != 2 {
		return nil, fmt.Errorf("invalid latlng param")
	}

	req := utils.HttpRequest{
		Method: "GET",
		Url:    "https://maps.googleapis.com/maps/api/geocode/json",
		Params: map[string]interface{}{
			"latlng": latLng,
			"key":    os.Getenv("GEOLOCATION_KEY"),
		},
	}

	response, err := req.ExecuteRequest()
	if err != nil {
		return nil, err
	}

	var geocoding models.Geocoding
	err = json.Unmarshal(response, &geocoding)
	if err != nil || len(geocoding.Results) == 0 {
		return nil, err
	}

	result := make(map[string]string)
	result["address"] = geocoding.Results[0].FormattedAddress
	result["district"] = ""
	for _, address := range geocoding.Results[0].AddressComponents {
		if strings.Contains(strings.Join(address.Types, ","), "administrative_area_level_1") {
			result["province"] = address.LongName
		} else if strings.Contains(strings.Join(address.Types, ","), "administrative_area_level_2") && result["district"] == "" {
			result["district"] = address.LongName
		}
	}

	// result["district"] = strings.Replace(result["district"], "Kecamatan", "", 1)

	return result, err
}

func (h helperUseCase) FetchShareLink(dataType string, dataId string, user domain.UserSession) (map[string]interface{}, error) {
	crmApp, err := h.repo.FetchAppInfo(user.AdminId)
	if err != nil {
		return map[string]interface{}{}, err
	}

	var crmAppInfo domain.CrmAppInfo
	if log.IfError(json.Unmarshal([]byte(cast.ToString(crmApp["app_info"])), &crmAppInfo)) {
		log.Info("error unmarshal crmAppInfo: %v", crmAppInfo)
	}

	bodyRequest := domain.DynamicLinkRequest{
		DynamicLinkInfo: domain.DynamicLinkInfo{
			AndroidInfo: domain.AndroidInfo{
				AndroidPackageName: crmAppInfo.Android.PackageName,
			},
			DomainURIPrefix: os.Getenv("FIREBASE_DYNAMIC_LINK"),
			IosInfo: domain.IosInfo{
				IosBundleID: crmAppInfo.Ios.BundleID,
			},
		},
		Suffix: domain.Suffix{
			Option: "SHORT",
		},
	}

	// bodyRequest := map[string]interface{}{
	// 	"dynamicLinkInfo": map[string]interface{}{
	// 		"domainUriPrefix": os.Getenv("FIREBASE_DYNAMIC_LINK"),
	// 		"androidInfo": map[string]interface{}{
	// 			"androidPackageName": crmAppInfo.Android.PackageName,
	// 		},
	// 		"iosInfo": map[string]interface{}{
	// 			"iosBundleId": crmAppInfo.Ios.BundleID,
	// 		},
	// 	},
	// 	"suffix": map[string]interface{}{
	// 		"option": "SHORT",
	// 	},
	// }

	if strings.TrimSpace(crmAppInfo.Web.Url) != "" {
		bodyRequest.DynamicLinkInfo.Link = fmt.Sprintf("%s/%s/%s", crmAppInfo.Web.Url, dataType, dataId)
		bodyRequest.DynamicLinkInfo.AndroidInfo.AndroidFallbackLink = bodyRequest.DynamicLinkInfo.Link
	}

	req := utils.HttpRequest{
		Method:      "POST",
		Url:         fmt.Sprintf("https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=%s", os.Getenv("FIREBASE_CRM_KEY")),
		PostRequest: utils.PostRequest{Body: bodyRequest},
	}
	resp, err := req.Execute()

	log.Info("status: %v | %v", resp.StatusCode, resp.Body)

	var result map[string]interface{}
	log.IfError(json.Unmarshal([]byte(resp.Body), &result))
	if err == nil && resp.StatusCode == 200 && result["shortLink"] != nil {
		return result, err
	}

	//manual
	//https://example.page.link/?link=https://www.example.com/someresource&apn=com.example.android&amv=3&ibi=com.example.ios&isi=1234567&ius=exampleapp

	linkParams := map[string]interface{}{
		"link": fmt.Sprintf("%s/%s/%v", crmAppInfo.Web.Url, dataType, dataId),
		"apn":  crmAppInfo.Android.PackageName,
		"ibi":  crmAppInfo.Ios.BundleID,
		"isi":  crmAppInfo.Ios.AppStoreId,
	}

	shortLink := utils.BuildUrl(os.Getenv("FIREBASE_DYNAMIC_LINK"), linkParams)
	log.Info("shortLink manual: %s", shortLink)

	result = make(map[string]interface{})
	result["shortLink"] = shortLink

	return result, err
}
