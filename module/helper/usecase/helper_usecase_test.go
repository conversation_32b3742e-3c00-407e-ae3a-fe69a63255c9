package usecase

import (
	"encoding/json"
	"reflect"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/helper"
	"gitlab.com/uniqdev/backend/api-membership/module/helper/mocks"
)

func Test_helperUseCase_FetchShareLink(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-crm/.env")

	crmAppStr := ` {
		"crm_app_id": 1,
		"admin_fkid": 1,
		"app_info": "{\"ios\": {\"bundle_id\": \"com.uniq.uniqmembership.yamiepanda\"}, \"android\": {\"package_name\": \"id.uniq.crm.yamiepanda\"}, \"web\": {\"url\": \"https://yamiepanda-crm.web.app\"}}",
		"app_config": "{\"asset\": {\"color\": {\"accent\": \"#D81B60\", \"primary\": \"#8C1115\", \"secondary\": \"#f6ce2b\", \"primary_dark\": \"#600207\"}, \"app_icon\": \"https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/app_icon.png\", \"app_background\": \"https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/app_background.png\", \"toolbar_background\": \"https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/toolbar_back_heigh.png\", \"profile_card_background\": \"https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/background_profile_card.png\"}, \"language\": {\"point\": \"Baby Panda\"}}",
		"data_created": 1671526130674,
		"data_modified": 1671526130674
	  }`

	journalRepo := new(mocks.HelperRepository)

	var appInfo map[string]interface{}
	json.Unmarshal([]byte(crmAppStr), &appInfo)
	journalRepo.On("FetchAppInfo", mock.Anything).Return(appInfo, nil)

	type fields struct {
		HelperRepository helper.HelperRepository
	}
	type args struct {
		dataType string
		dataId   string
		user     domain.UserSession
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{"test1", fields{journalRepo}, args{"deal", "1", domain.UserSession{}}, map[string]interface{}{}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := helperUseCase{
				repo: tt.fields.HelperRepository,
			}
			got, err := h.FetchShareLink(tt.args.dataType, tt.args.dataId, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("helperUseCase.FetchShareLink() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("helperUseCase.FetchShareLink() = %v, want %v", got, tt.want)
			}
		})
	}
}
