// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// HelperRepository is an autogenerated mock type for the HelperRepository type
type HelperRepository struct {
	mock.Mock
}

// FetchAppInfo provides a mock function with given fields: adminId
func (_m *HelperRepository) FetchAppInfo(adminId int64) (map[string]interface{}, error) {
	ret := _m.Called(adminId)

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(int64) map[string]interface{}); ok {
		r0 = rf(adminId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int64) error); ok {
		r1 = rf(adminId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewHelperRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewHelperRepository creates a new instance of HelperRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewHelperRepository(t mockConstructorTestingTNewHelperRepository) *HelperRepository {
	mock := &HelperRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
