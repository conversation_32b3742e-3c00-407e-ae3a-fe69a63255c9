package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type helperHandler struct {
	uc domain.HelperUseCase
}

func NewHttpHelperHandler(app *fasthttprouter.Router, useCase domain.HelperUseCase) {
	handler := &helperHandler{useCase}
	app.GET("/v1/geocoding", auth.ValidatePublicKey(handler.FetchLocation))
	app.GET("/v1/share", auth.ValidatePublicKey(handler.FetchShareLink))

	// ---- OLD ROUTING ----
	app.GET("/v1/region/:country", auth.ValidatePublicKey(v1.GetProvince))
	app.GET("/v1/region/:country/:provinceId", auth.ValidatePublicKey(v1.GetKabupaten))
}
func (h helperHandler) FetchLocation(ctx *fasthttp.RequestCtx) {
	latLng := ctx.QueryArgs().Peek("latlng")
	if latLng == nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	result, err := h.uc.FetchLocation(string(latLng))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

// fetchSahreLink
func (h helperHandler) FetchShareLink(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	dataType := ctx.QueryArgs().Peek("data_type")
	dataId := ctx.QueryArgs().Peek("data_id")
	result, err := h.uc.FetchShareLink(string(dataType), string(dataId), user)

	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
