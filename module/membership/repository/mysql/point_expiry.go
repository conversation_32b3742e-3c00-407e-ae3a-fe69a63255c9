package mysql

import (
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func (m membershipRepository) GetMembersByTypeWithCalendarExpiry(date string) ([]models.MemberDetailEntity, error) {
	query := `
SELECT md.* 
FROM members_detail md
JOIN members_type_expiry_config mtec ON md.type_fkid = mtec.type_fkid
WHERE mtec.expiry_enabled = 1 
AND mtec.expiry_model = 'calendar'
AND DATE(mtec.expiry_date) = DATE(?)
AND md.total_point > 0`

	result := []models.MemberDetailEntity{}
	rows, err := db.QueryArray(query, date)
	if err != nil {
		log.Error("Error getting members for calendar expiry: %v", err)
		return nil, err
	}

	for _, row := range rows {
		member := models.MemberDetailEntity{}
		member.MembersDetailID = int(row["members_detail_id"].(int64))
		member.MemberFkID = row["member_fkid"].(int64)
		member.AdminFkID = int(row["admin_fkid"].(int64))
		member.TypeFkID = int(row["type_fkid"].(int64))
		member.TotalPoint = int(row["total_point"].(int64))
		result = append(result, member)
	}

	return result, nil
}

func (m membershipRepository) GetInactiveMembersForExpiry() ([]models.MemberInactivityInfo, error) {
	query := `
SELECT md.*, ANY_VALUE(mtec.inactivity_period) AS inactivity_period,
COALESCE(MAX(s.time_created/1000), 0) as last_activity
FROM members_detail md
JOIN members_type_expiry_config mtec ON md.type_fkid = mtec.type_fkid
LEFT JOIN sales s ON s.member_fkid = md.member_fkid
WHERE mtec.expiry_enabled = 1 
AND mtec.expiry_model = 'activity'
AND md.total_point > 0
GROUP BY md.members_detail_id
HAVING last_activity = 0 OR 
(UNIX_TIMESTAMP() - last_activity) >= (inactivity_period * 24 * 60 * 60)`

	rows, err := db.QueryArray(query)
	if err != nil {
		log.Error("Error getting inactive members: %v", err)
		return nil, err
	}

	result := []models.MemberInactivityInfo{}
	for _, row := range rows {
		member := models.MemberDetailEntity{}
		member.MembersDetailID = int(row["members_detail_id"].(int64))
		member.MemberFkID = row["member_fkid"].(int64)
		member.AdminFkID = int(row["admin_fkid"].(int64))
		member.TypeFkID = int(row["type_fkid"].(int64))
		member.TotalPoint = int(row["total_point"].(int64))

		info := models.MemberInactivityInfo{
			Member:           member,
			InactivityPeriod: (cast.ToInt(row["inactivity_period"])),
			LastActivity:     cast.ToInt64(row["last_activity"]),
		}
		result = append(result, info)
	}

	return result, nil
}

func (m membershipRepository) ResetMemberPoints(memberID int64, adminID int, points int) error {
	// Begin transaction
	tx, err := m.db.Conn.Begin()
	if err != nil {
		log.Error("Error starting transaction: %v", err)
		return err
	}

	// Reset points to zero
	updateQuery := `
		UPDATE members_detail 
		SET total_point = 0 
		WHERE member_fkid = ? AND admin_fkid = ?`

	res, err := tx.Exec(updateQuery, memberID, adminID)
	if err != nil {
		tx.Rollback()
		log.Error("Error resetting points: %v", err)
		return err
	}

	affected, err := res.RowsAffected()
	if err != nil {
		tx.Rollback()
		return err
	}

	if affected == 0 {
		tx.Rollback()
		return fmt.Errorf("no rows affected")
	}

	// Insert into point history
	historyQuery := `
		INSERT INTO member_point_history 
		(member_id, admin_fkid, transaction_type, action_type, point_change, balance_after, transaction_date, created_by, notes)
		VALUES (?, ?, 'debit', 'point_expiry', ?, 0, ?, 'system', 'Points expired due to policy')`

	now := time.Now().UnixMilli()
	_, err = tx.Exec(historyQuery, memberID, adminID, points, now)
	if err != nil {
		tx.Rollback()
		log.Error("Error inserting point history: %v", err)
		return err
	}

	return tx.Commit()
}

func (m membershipRepository) LogPointExpiry(log models.PointExpiryLog) error {
	_, err := db.Insert("member_point_expiry_log", map[string]interface{}{
		"member_fkid":    log.MemberFkID,
		"admin_fkid":     log.AdminFkID,
		"expiry_type":    log.ExpiryType,
		"points_expired": log.PointsExpired,
		"execution_date": log.ExecutionDate,
	})
	if err != nil {
		return err
	}

	return nil
}

func (m membershipRepository) IsExpiryProcessed(memberID int64, adminID int, expiryType string, date int64) (bool, error) {
	query := `
		SELECT COUNT(*) 
		FROM member_point_expiry_log 
		WHERE member_fkid = ? 
		AND admin_fkid = ? 
		AND expiry_type = ? 
		AND execution_date >= ?`

	result, err := db.QueryArray(query, memberID, adminID, expiryType, date)
	if err != nil {
		log.Error("Error checking expiry process: %v", err)
		return false, err
	}

	count := 0
	if len(result) > 0 {
		count = int(result[0]["COUNT(*)"].(int64))
	}
	if err != nil {
		log.Error("Error checking expiry process: %v", err)
		return false, err
	}

	return count > 0, nil
}
