package mysql

import (
	"database/sql"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
)

type membershipRepository struct {
	db db.Repository
}

func NewMysqlMembershipRepository(conn *sql.DB) membership.MembershipRepository {
	return &membershipRepository{db.Repository{Conn: conn}}
}

func (m membershipRepository) FetchMemberTypes(user domain.UserSession) ([]map[string]interface{}, error) {
	sql := `SELECT p.name, point_target, spent_target, price, lifetime_day, lifetime_type 
from members_type mt 
join products p on p.product_id=mt.product_fkid
where mt.admin_fkid=? 
and p.data_status='on'
order by (point_target+spent_target) asc`
	return db.QueryArray(sql, user.AdminId)
}

func (m membershipRepository) UpdatePointIncrement(increment int, user domain.UserSession) error {
	_, err := db.Update("members_detail", map[string]interface{}{
		"total_point": map[string]interface{}{
			"+": increment,
		},
	}, "member_fkid=? and admin_fkid=?", user.MemberId, user.AdminId)
	return err
}

func (m membershipRepository) UpdateMemberDetails() error {
	sql := `
        UPDATE members_detail md
        SET 
            md.total_spend = COALESCE((
                SELECT SUM(s.grand_total)
                FROM sales s
                JOIN outlets o ON s.outlet_fkid = o.outlet_id
                WHERE s.member_fkid = md.member_fkid
                AND o.admin_fkid = md.admin_fkid
                AND s.status='Success'
            ), 0),    
            md.total_transaction = COALESCE((
                SELECT COUNT(*)
                FROM sales s
                JOIN outlets o ON s.outlet_fkid = o.outlet_id
                WHERE s.member_fkid = md.member_fkid
                AND o.admin_fkid = md.admin_fkid
                AND s.status='Success'
            ), 0)`

	_, err := m.db.Conn.Exec(sql)
	return err
}
