package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
)

type membershipHandler struct {
	uc membership.MembershipUseCase
}

func NewHttpMembershipHandler(app *fasthttprouter.Router, useCase membership.MembershipUseCase) {
	handler := &membershipHandler{useCase}
	app.GET("/v1/membership/type", auth.ValidatePublicKey(handler.FetchMemberTypes))
}

func (h membershipHandler) FetchMemberTypes(ctx *fasthttp.RequestCtx) {
	userSession := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchMemberTypes(userSession)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
