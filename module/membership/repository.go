package membership

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type MembershipRepository interface {
	FetchMemberTypes(user domain.UserSession) ([]map[string]interface{}, error)
	UpdateMemberDetails() error
	UpdatePointIncrement(increment int, user domain.UserSession) error
	GetMembersByTypeWithCalendarExpiry(date string) ([]models.MemberDetailEntity, error)
	GetInactiveMembersForExpiry() ([]models.MemberInactivityInfo, error)
	ResetMemberPoints(memberID int64, adminID int, points int) error
	LogPointExpiry(log models.PointExpiryLog) error
	IsExpiryProcessed(memberID int64, adminID int, expiryType string, date int64) (bool, error)
	ScheduleNotification(message domain.ScheduleMessage) error
}
