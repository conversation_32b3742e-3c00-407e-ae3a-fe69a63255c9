package usecase

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
)

type membershipUsecase struct {
	membershipRepo membership.MembershipRepository
}

func NewMembershipUseCase(mr membership.MembershipRepository) membership.MembershipUseCase {
	return &membershipUsecase{
		membershipRepo: mr,
	}
}

func (m *membershipUsecase) FetchMemberTypes(user domain.UserSession) ([]map[string]interface{}, error) {
	return m.membershipRepo.FetchMemberTypes(user)
}

func (m *membershipUsecase) UpdateMemberDetails() error {
	return m.membershipRepo.UpdateMemberDetails()
}
