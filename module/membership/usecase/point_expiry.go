package usecase

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

var muActivityBasedExpiry = &sync.Mutex{}
var muCalendarBasedExpiry = &sync.Mutex{}

func (u *membershipUsecase) scheduleExpiryNotification(member models.MemberDetailEntity, pointsExpired int, expiryType string, inactivityDays int) error {
	var title, message string

	switch expiryType {
	case "calendar":
		title = "Points Expired on Schedule"
		message = fmt.Sprintf("Your %d points have expired based on the calendar schedule", pointsExpired)
	case "activity":
		title = "Points Expired Due to Inactivity"
		message = fmt.Sprintf("Your %d points have expired due to %d days of inactivity", pointsExpired, inactivityDays)
	}

	notification := domain.ScheduleMessage{
		Title:       title,
		Message:     message,
		TimeDeliver: time.Now().Unix() * 1000, // Current time in milliseconds
		DataCreated: time.Now().Unix() * 1000,
		Media:       domain.MessageMediaPushNotif,
		Receiver:    member.FirebaseToken,
		AdminFkid:   member.AdminFkID,
	}

	return u.membershipRepo.ScheduleNotification(notification)
}

func (u *membershipUsecase) ProcessCalendarBasedExpiry() error {
	muCalendarBasedExpiry.Lock()
	defer muCalendarBasedExpiry.Unlock()

	log.Info("Starting calendar based point expiry job")

	// Get members with calendar based expiry based on today's date
	today := time.Now().Format("2006-01-02")
	members, err := u.membershipRepo.GetMembersByTypeWithCalendarExpiry(today)
	if err != nil {
		log.Error("Failed to get members for calendar expiry: %v", err)
		return err
	}

	now := time.Now()

	for _, member := range members {
		// Check if already processed today
		processed, err := u.membershipRepo.IsExpiryProcessed(
			member.MemberFkID,
			member.AdminFkID,
			"calendar",
			now.UnixMilli(),
		)
		if err != nil {
			log.Error("Failed to check expiry status for member %v: %v", member.MemberFkID, err)
			continue
		}
		if processed {
			log.Info("Point expiry already processed for member %v today", member.MemberFkID)
			continue
		}

		log.Info("Processing calendar based expiry for member_id: %v admin_id: %v points: %v",
			member.MemberFkID, member.AdminFkID, member.TotalPoint)

		// Reset points
		if err := u.membershipRepo.ResetMemberPoints(member.MemberFkID, member.AdminFkID, member.TotalPoint); err != nil {
			log.Error("Failed to reset points for member %v: %v", member.MemberFkID, err)
			continue
		}

		// Schedule notification if firebase token exists
		if member.FirebaseToken != "" {
			if err := u.scheduleExpiryNotification(member, member.TotalPoint, "calendar", 0); err != nil {
				log.Error("Failed to schedule notification for member %v: %v", member.MemberFkID, err)
				// Don't return error, continue with other members
			}
		}

		// Log expiry
		expiryLog := models.PointExpiryLog{
			MemberFkID:    member.MemberFkID,
			AdminFkID:     member.AdminFkID,
			ExpiryType:    "calendar",
			PointsExpired: member.TotalPoint,
			ExecutionDate: now.UnixMilli(),
		}
		if err := u.membershipRepo.LogPointExpiry(expiryLog); err != nil {
			log.Error("Failed to log expiry for member %v: %v", member.MemberFkID, err)
			continue
		}

		log.Info("Successfully processed calendar based expiry for member_id: %v admin_id: %v",
			member.MemberFkID, member.AdminFkID)
	}

	return nil
}

func (u *membershipUsecase) ProcessActivityBasedExpiry() error {
	muActivityBasedExpiry.Lock()
	defer muActivityBasedExpiry.Unlock()

	log.Info("Starting activity based point expiry job")

	// Get inactive members based on their configured inactivity period
	members, err := u.membershipRepo.GetInactiveMembersForExpiry()
	if err != nil {
		log.Error("Failed to get inactive members: %v", err)
		return err
	}

	now := time.Now()

	for _, info := range members {
		member := info.Member

		// Check if already processed today
		processed, err := u.membershipRepo.IsExpiryProcessed(
			member.MemberFkID,
			member.AdminFkID,
			"activity",
			now.UnixMilli(),
		)
		if err != nil {
			log.Error("Failed to check expiry status for member %v: %v", member.MemberFkID, err)
			continue
		}
		if processed {
			log.Info("Point expiry already processed for member %v today", member.MemberFkID)
			continue
		}

		log.Info("Processing activity based expiry for member_id: %v admin_id: %v points: %v inactive_days: %v last_activity: %v",
			member.MemberFkID, member.AdminFkID, member.TotalPoint, info.InactivityPeriod, time.Unix(info.LastActivity, 0))

		// Reset points
		if err := u.membershipRepo.ResetMemberPoints(member.MemberFkID, member.AdminFkID, member.TotalPoint); err != nil {
			log.Error("Failed to reset points for member %v: %v", member.MemberFkID, err)
			continue
		}

		// Schedule notification if firebase token exists
		if member.FirebaseToken != "" {
			if err := u.scheduleExpiryNotification(member, member.TotalPoint, "activity", info.InactivityPeriod); err != nil {
				log.Error("Failed to schedule notification for member %v: %v", member.MemberFkID, err)
				// Don't return error, continue with other members
			}
		}

		// Log expiry
		expiryLog := models.PointExpiryLog{
			MemberFkID:    member.MemberFkID,
			AdminFkID:     member.AdminFkID,
			ExpiryType:    "activity",
			PointsExpired: member.TotalPoint,
			ExecutionDate: now.UnixMilli(),
		}
		if err := u.membershipRepo.LogPointExpiry(expiryLog); err != nil {
			log.Error("Failed to log expiry for member %v: %v", member.MemberFkID, err)
			continue
		}

		log.Info("Successfully processed activity based expiry for member_id: %v admin_id: %v",
			member.MemberFkID, member.AdminFkID)
	}

	return nil
}
