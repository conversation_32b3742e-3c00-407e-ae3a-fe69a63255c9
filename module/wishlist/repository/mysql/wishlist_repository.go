package mysql

import (
	"database/sql"
	"time"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type wishlistRepository struct {
	db db.Repository
}

func NewMysqlWishlistRepository(conn *sql.DB) domain.WishlistRepository {
	return &wishlistRepository{db.Repository{Conn: conn}}
}

func (w wishlistRepository) FetchWishList(userSession domain.UserSession) ([]map[string]interface{}, error) {
	sql := `select * from crm_product_wishlist where member_fkid = ? and admin_fkid = ?`
	return db.QueryArray(sql, userSession.MemberId, userSession.AdminId)
}

func (w wishlistRepository) AddWishList(productId int, user domain.UserSession) (int64, error) {
	resp, err := db.Insert("crm_product_wishlist", map[string]interface{}{
		"product_fkid": productId,
		"member_fkid":  user.MemberId,
		"admin_fkid":   user.AdminId,
		"time_created": time.Now().Unix() * 1000,
	})
	if err != nil {
		return 0, err
	}
	id, err := resp.LastInsertId()
	return id, err
}

func (w wishlistRepository) RemoveWishList(wishListId int, user domain.UserSession) error {
	_, err := db.Delete("crm_product_wishlist",
		"crm_product_wishlist_id = ? and member_fkid = ? and admin_fkid = ?",
		wishListId, user.MemberId, user.AdminId)
	return err
}

func (w wishlistRepository) CountWishList(user domain.UserSession) (map[string]interface{}, error) {
	sql := `SELECT count(*) as count from crm_product_wishlist where member_fkid = ? and admin_fkid = ?`
	return db.Query(sql, user.MemberId, user.AdminId)
}

func (w wishlistRepository) FetchWishListTopItem(userSession domain.UserSession) ([]map[string]interface{}, error) {
	sql := `select count(*) as total, product_fkid from crm_product_wishlist
where admin_fkid = ? 
group by product_fkid
order by total desc `
	return db.QueryArray(sql, userSession.AdminId)
}

func (w wishlistRepository) UpdateWishListCategory(id int, categoryId int64, user domain.UserSession) error {
	if categoryId == 0 {
		return w.RemoveWishListCategory(id, user)
	}

	resp, err := db.Update("crm_product_wishlist", map[string]interface{}{
		"wishlist_category_fkid": categoryId,
	}, "crm_product_wishlist_id = ? and member_fkid = ? and admin_fkid = ?", id, user.MemberId, user.AdminId)
	if err != nil {
		return err
	}

	eff, _ := resp.RowsAffected()
	log.Info("UpdateWishListCategory: %v, effected: %v", id, eff)
	return nil
}

func (w wishlistRepository) RemoveWishListCategory(id int, user domain.UserSession) error {
	resp, err := db.GetConn().Exec("update crm_product_wishlist set wishlist_category_fkid = null where member_fkid = ? and admin_fkid = ?",
		user.MemberId, user.AdminId)
	if err != nil {
		return err
	}

	eff, _ := resp.RowsAffected()
	log.Info("RemoveWishListCategory: %v, effected: %v", id, eff)
	return nil
}

func (w wishlistRepository) AddWishListCategory(name string, user domain.UserSession) (int64, error) {
	resp, err := db.Insert("crm_product_wishlist_category", map[string]interface{}{
		"name":          name,
		"member_fkid":   user.MemberId,
		"admin_fkid":    user.AdminId,
		"data_created":  time.Now().Unix() * 1000,
		"data_modified": time.Now().Unix() * 1000,
	})
	if log.IfError(err) {
		return 0, err
	}
	id, _ := resp.LastInsertId()
	return id, nil
}

func (w wishlistRepository) FetchWishListCategory(user domain.UserSession) ([]map[string]interface{}, error) {
	sql := `select cc.id, cc.name, cc.data_created, cc.data_modified, count(*) as total 
	from crm_product_wishlist_category cc 
	left join crm_product_wishlist cpw on cpw.wishlist_category_fkid=cc.id 
	where cc.member_fkid = ? and cc.admin_fkid = ?
	group by cc.id`
	return db.QueryArray(sql, user.MemberId, user.AdminId)
}
