package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type wishlistHandler struct {
	uc domain.WishlistUseCase
}

func NewHttpWishlistHandler(app *fasthttprouter.Router, useCase domain.WishlistUseCase) {
	handler := &wishlistHandler{useCase}
	app.GET("/v1/wishlist", auth.ValidateToken(handler.FetchWishList))
	app.POST("/v1/wishlist", auth.ValidateToken(handler.AddWishList))
	app.DELETE("/v1/wishlist/:id", auth.ValidateToken(handler.RemoveWishList))
	app.PUT("/v1/wishlist/:id/category", auth.ValidateToken(handler.UpdateWishlistCategory))

	//count
	app.GET("/v1/wishlist-count", auth.ValidateToken(handler.CountWishList))

	//category
	app.POST("/v1/wishlist-category", auth.ValidateToken(handler.AddWishListCategory))
	app.GET("/v1/wishlist-category", auth.ValidateToken(handler.FetchWishListCategory))
}

func (h wishlistHandler) FetchWishList(ctx *fasthttp.RequestCtx) {
	format := string(ctx.QueryArgs().Peek("format"))

	var result []map[string]interface{}
	var err error
	//if format == "top-item" {
	//	result, err = h.uc.FetchWishListTopItem(domain.UserSessionFastHttp(ctx))
	//} else {
	//	result, err = h.uc.FetchWishList(domain.UserSessionFastHttp(ctx), format)
	//}

	result, err = h.uc.FetchWishList(domain.UserSessionFastHttp(ctx), format)

	if err != nil {
		log.Info("FetchWishList err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h wishlistHandler) AddWishList(ctx *fasthttp.RequestCtx) {
	productId := cast.ToInt(ctx.PostArgs().Peek("product_id"))
	log.Info("add wishlist productId: %v", productId)
	result, err := h.uc.AddWishList(productId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("AddWishList err: %v", err)
		errCode := 0
		if code, ok := err.(exception.WithCode); ok {
			errCode = code.Code
			if code.Code > 200 {
				ctx.SetStatusCode(code.Code)
			}
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errCode, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h wishlistHandler) RemoveWishList(ctx *fasthttp.RequestCtx) {
	wishListId := cast.ToInt(ctx.UserValue("id"))
	err := h.uc.RemoveWishList(wishListId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("RemoveWishList err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h wishlistHandler) CountWishList(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.CountWishList(domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("CountWishList err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h wishlistHandler) UpdateWishlistCategory(ctx *fasthttp.RequestCtx) {
	categoryId := cast.ToInt64(ctx.PostArgs().Peek("category_id"))
	err := h.uc.UpdateWishListCategory(cast.ToInt(ctx.UserValue("id")), categoryId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("UpdateWishlistCategory err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h wishlistHandler) AddWishListCategory(ctx *fasthttp.RequestCtx) {
	name := string(ctx.PostArgs().Peek("name"))
	result, err := h.uc.AddWishListCategory(name, domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("AddWishListCategory err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func (h wishlistHandler) FetchWishListCategory(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchWishListCategory(domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("FetchWishListCategory err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
