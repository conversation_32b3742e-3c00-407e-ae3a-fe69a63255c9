package usecase

import (
	"fmt"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
)

type wishlistUseCase struct {
	repo        domain.WishlistRepository
	repoProduct product.Repository
}

func NewWishlistUseCase(repository domain.WishlistRepository, repositoryProduct product.Repository) domain.WishlistUseCase {
	return &wishlistUseCase{repository, repositoryProduct}
}

func (w *wishlistUseCase) FetchWishList(userSession domain.UserSession, format string) ([]map[string]interface{}, error) {
	var wishList []map[string]interface{}
	var err error
	if format == "top-item" {
		wishList, err = w.repo.FetchWishListTopItem(userSession)
	} else {
		wishList, err = w.repo.FetchWishList(userSession)
	}

	if err != nil {
		return nil, err
	}

	productIds := make([]int, 0)
	for _, raw := range wishList {
		productIds = append(productIds, cast.ToInt(raw["product_fkid"]))
	}

	fmt.Println("productIds", productIds)
	productList, err := w.repoProduct.FetchProductByIds(productIds)
	fmt.Println("prouct list size: ", len(productList))
	productMap := make(map[int]interface{})
	for _, product := range productList {
		productMap[cast.ToInt(product["product_id"])] = product
	}

	fmt.Println("productMap: ", productMap)
	for i, raw := range wishList {
		wishList[i]["product"] = productMap[cast.ToInt(raw["product_fkid"])]
	}

	return wishList, nil
}

func (w *wishlistUseCase) AddWishList(productId int, user domain.UserSession) (map[string]interface{}, error) {
	id, err := w.repo.AddWishList(productId, user)
	if err != nil {
		if utils.GetErrCodeSQL(err.Error()) == utils.SQL_ERR_DUPLICATE {
			err = exception.WithCode{Code: 409, Message: "already exist"}
		}
		return nil, err
	}
	return map[string]interface{}{
		"crm_product_wishlist_id": id,
	}, nil
}

func (w *wishlistUseCase) RemoveWishList(wishListId int, user domain.UserSession) error {
	return w.repo.RemoveWishList(wishListId, user)
}

func (w *wishlistUseCase) CountWishList(userSession domain.UserSession) (map[string]interface{}, error) {
	return w.repo.CountWishList(userSession)
}

func (w *wishlistUseCase) FetchWishListTopItem(userSession domain.UserSession) ([]map[string]interface{}, error) {
	if userSession.MemberAccess != domain.AccessAdmin {
		return nil, fmt.Errorf("only admin allowed to fetch the request")
	}

	return w.repo.FetchWishListTopItem(userSession)
}

func (w *wishlistUseCase) UpdateWishListCategory(id int, categoryId int64, user domain.UserSession) error {
	//check if wishlistId valid
	//check if categoryId valid

	//update to newest category
	return w.repo.UpdateWishListCategory(id, categoryId, user)
}

func (w *wishlistUseCase) AddWishListCategory(name string, user domain.UserSession) (map[string]interface{}, error) {
	id, err := w.repo.AddWishListCategory(name, user)
	if err != nil {
		return nil, err
	}
	return map[string]interface{}{
		"id": id,
	}, nil
}

func (w *wishlistUseCase) FetchWishListCategory(user domain.UserSession) ([]map[string]interface{}, error) {
	return w.repo.FetchWishListCategory(user)
}
