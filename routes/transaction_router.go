package routes

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
)

func SetTransactionRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.POST("/v1/transaction/order", auth.ValidateToken(v1.OrderSale))
	router.POST("/v1/transaction/order_online", auth.ValidateToken(v1.CreateOrder))
	router.PUT("/v1/transaction/order_online/:id", auth.ValidateToken(v1.UpdateOrder))
	router.GET("/v1/transaction/order_online/:id", auth.ValidateToken(v1.GetHistoryDetailOrder))
	router.GET("/v1/transaction/order_online/:id/payment_detail", auth.ValidateToken(v1.GetOrderPaymentDetail))

	router.GET("/v1/transaction/history", auth.ValidateToken(v1.GetHistory))
	router.GET("/v1/transaction/history/page/:page", auth.ValidateToken(v1.GetHistory))
	router.GET("/v1/transaction/history/buy_deals/:id", auth.ValidateToken(v1.GetHistoryDetailBuyDeals))
	router.GET("/v1/transaction/history/sales/:id", auth.ValidateToken(v1.GetHistoryDetailSales))

	router.POST("/v1/transaction/feedback", auth.ValidateToken(v1.GiveFeedBack))

	//self order
	router.OPTIONS("/v1/transaction/self_order", auth.EnableCors)
	router.OPTIONS("/v1/transaction/self_order/:x", auth.EnableCors)
	router.OPTIONS("/v1/transaction/self_order/:x/:xx", auth.EnableCors)
	router.POST("/v1/transaction/self_order", auth.MultiMiddleware(v1.AddSelfOrder, auth.CORS, auth.ValidatePublicKey))
	router.GET("/v1/transaction/self_order/:outletId/:id", auth.MultiMiddleware(v1.GetSelfOrder, auth.CORS, auth.ValidatePublicKey))
	router.PUT("/v1/transaction/self_order/:id", auth.MultiMiddleware(v1.UpdateSelfOrder, auth.CORS, auth.ValidatePublicKey))

	router.POST("/v1/transaction/self_order/send", auth.MultiMiddleware(v1.SendSelfOrderCode, auth.CORS, auth.ValidatePublicKey))

	router.GET("/v1/transaction/note-suggestion", auth.ValidatePublicKey(v1.GetNoteSuggestion))

	//payment notification
	// router.POST("/payment/notification", v1.HandlePaymentNotification)
	router.GET("/payment/notification/:orderId", v1.GetPaymentInfoForSimulation)

	return router
}
