package routes

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	v2 "gitlab.com/uniqdev/backend/api-membership/controller/v2"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
)

func SetUserRoutes(router *fasthttprouter.Router, userHander *v1.UserControllerHandler) *fasthttprouter.Router {
	router.GET("/v1/user/check/phone", auth.ValidatePublicKey(v1.CheckExistingUserByPhone))
	router.POST("/v1/user/register/phone", userHander.RegisterWithPhone)
	router.POST("/v1/user/join/phone", v1.JoinWithOtherBusiness)

	//user
	router.GET("/v1/user/detail", auth.ValidateToken(v1.GetMemberDetail))
	router.GET("/v1/user/point", auth.ValidateToken(v1.GetPoint))
	router.GET("/v1/user/secret_id", auth.ValidateToken(v1.RequestSecretID))
	router.POST("/v1/user/resend_confirmation", auth.ValidateToken(v1.ResendEmailConfirmation))
	router.POST("/v1/user/firebase_token", auth.ValidateToken(v1.UpdateFirebaseToken))

	router.PUT("/v1/user", auth.ValidateToken(userHander.UpdateProfile))
	router.POST("/v1/user/lost_phone", auth.ValidatePublicKey(v1.LostPhoneNumber))
	router.POST("/v2/user/lost_phone", auth.ValidatePublicKey(v2.LostPhoneNumber))
	router.POST("/v1/user/change_phone", auth.ValidatePublicKey(v1.ChangePhoneNumber))
	router.POST("/v2/user/change_phone", auth.ValidatePublicKey(v2.ValidateLostPhoneNumber))
	router.GET("/v1/user/activation/:random/:level/:email", auth.ValidatePublicKey(v1.EmailActivation))

	//verification
	router.GET("/v1/user/verification/email/code", auth.ValidateToken(v1.SendEmailVerificationCode))
	router.POST("/v1/user/verification/email/code", auth.ValidateToken(v1.ValidateEmailVerificationCode))

	//web
	router.GET("/user/activation/:random/:level/:email", v1.EmailVerificationWeb)
	router.GET("/user/change_phone/:random/:level/:email", v1.ChangePhoneNumberRedirect)

	//notif
	router.GET("/v1/inbox", auth.ValidateToken(v1.GetInbox))
	router.GET("/v2/inbox/:id", auth.ValidateToken(v1.GetInbox))
	router.GET("/v1/inbox/page/:page", auth.ValidateToken(v1.GetInbox))
	router.GET("/v1/inbox/count/unread", auth.ValidateToken(v1.CountUnreadInbox))
	router.PUT("/v1/inbox", auth.ValidateToken(v1.UpdateInboxStatus))
	return router
}
