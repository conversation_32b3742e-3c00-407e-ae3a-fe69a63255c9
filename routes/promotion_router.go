package routes

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
)

func SetPromotionRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/v1/promo/deals", auth.ValidatePublicKey(v1.GetDeals))
	router.GET("/v1/promo/deals/detail/:id", auth.ValidatePublicKey(v1.GetDeals))
	router.GET("/v1/promo/deals/page/:page", auth.ValidatePublicKey(v1.GetDeals))
	router.POST("/v1/promo/deals/buy", auth.ValidateToken(v1.BuyDeals))

	router.GET("/v1/promo/deals/myvoucher", auth.ValidateToken(v1.GetMyVoucher))
	router.GET("/v1/promo/deals/myvoucher/group", auth.ValidateToken(v1.GetMyVoucherGroup))
	router.GET("/v1/promo/deals/myvoucher/status/:id", auth.ValidateToken(v1.GetMyVoucherStatus))
	// router.GET("/v1/promo/deals/myvoucher/detail/:id", auth.ValidateToken(v1.GetMyVoucherDetail))
	router.GET("/v1/promo/deals/myvoucher/detail/:id", auth.ValidateToken(v1.GetMyVoucher))

	router.GET("/v1/promo/deals/total_voucher", auth.ValidateToken(v1.GetTotalVoucher))

	//promotion
	router.GET("/v1/promo", auth.ValidatePublicKey(v1.GetPromotionByOutlet))
	return router
}
