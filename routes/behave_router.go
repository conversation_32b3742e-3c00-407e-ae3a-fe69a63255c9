package routes

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/integration/behave"
)

func SetBehaveRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/behave", behave.WelcomeBehave)
	router.POST("/behave/v1/auth/token", behave.RequestBehaveToken)
	router.POST("/behave/v1/order", auth.ValidateBehaveToken(behave.BehaveOrder))
	//router.POST("/behave/v1/order", v1.BehaveOrder)
	return router
}
