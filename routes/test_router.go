package routes

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
)

const (
	verifyCustomTokenURL   = "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyCustomToken?key=%s"
	verifyCustomTokenURLV1 = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key="
)

var (
	apiKey = os.Getenv("API_KEY")
)

func GetIdToken(ctx *fasthttp.RequestCtx) {
	env := os.Getenv("server")
	if env == "development" || env == "localhost" {
		ctx.SetStatusCode(fasthttp.StatusOK)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
			"id_token": createIdToken(),
		})
		return
	}
	ctx.SetStatusCode(fasthttp.StatusInternalServerError)
}

func createIdToken() string {
	app := firebase.GetFirebaseApp()
	ctx := context.Background()
	client, err := app.Auth(ctx)
	if err != nil {
		return fmt.Sprintf("init auth err: %v", err)
	}

	token, err := client.CustomToken(ctx, "hEFVO7uaiKdGjObl4SqviIKWxKz2")
	if err != nil {
		log.Fatalf("error minting custom token: %v\n", err)
	}

	idToken, err := signInWithCustomToken(token)
	if err != nil {
		return fmt.Sprintf("signInWithCustomToken err: %v", err)
	}

	return idToken
}

// doc: https://cloud.google.com/identity-platform/docs/admin/create-custom-tokens#php
func createAuthToken(customToken string) (string, error) {
	token := jwt.New(jwt.SigningMethodRS512)

	claims := jwt.MapClaims{}
	claims["iat"] = time.Now().Unix()
	claims["exp"] = time.Now().Add(1 * time.Hour).Unix()
	claims["iss"] = "<EMAIL>"
	claims["sub"] = "<EMAIL>"
	claims["aud"] = "https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit"
	claims["uid"] = time.Now().UnixMilli()

	token.Claims = claims

	privKey := auth.ReadPrivateKey("config/auth/app.rsa")
	tokenString, err := token.SignedString(privKey)
	return tokenString, err
}

func signInWithCustomToken(token string) (string, error) {
	req, err := json.Marshal(map[string]interface{}{
		"token":             token,
		"returnSecureToken": true,
	})
	if err != nil {
		return "", err
	}

	if apiKey == "" {
		panic("API_KEY env not defined")
	}

	resp, err := postRequest(fmt.Sprintf(verifyCustomTokenURL, apiKey), req)
	if err != nil {
		return "", err
	}
	var respBody struct {
		IDToken string `json:"idToken"`
	}
	if err := json.Unmarshal(resp, &respBody); err != nil {
		return "", err
	}
	return respBody.IDToken, err
}

func postRequest(url string, req []byte) ([]byte, error) {
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(req))
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected http status code: %d", resp.StatusCode)
	}
	return io.ReadAll(resp.Body)
}
