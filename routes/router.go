package routes

import (
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"time"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/job"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
)

func InitRouter() *fasthttprouter.Router {
	router := fasthttprouter.New()
	return router
}

func CreateRoutes(router *fasthttprouter.Router, promoUseCase promotion.UseCase) *fasthttprouter.Router {
	userHandler := v1.NewUserControllerHandler(promoUseCase)

	// router := fasthttprouter.New()
	router.GET("/", Index)
	router.GET("/who", CheckAPI)
	router.POST("/who", CheckAPI)

	// router.POST("/cron/lifetime", job.PubSubCheckMemberLifeTime)
	router.POST("/cron/order-status", job.ReceiveOrderStatusChange)

	router = SetAuthRoutes(router)
	// router = SetOutletRoutes(router)
	router = SetProductRoutes(router)
	router = SetUserRoutes(router, userHandler)
	router = SetPromotionRoutes(router)
	router = SetTransactionRoutes(router)
	router = SetMidtransRoutes(router)
	router = SetBehaveRoutes(router)
	router = SetSystemRoutes(router)
	router = SetAppRoutes(router)
	router = SetExperimentRoutes(router)
	router = SetHelperRoutes(router)
	router = SetSubscriptionRoutes(router)

	router.GET("/cron/notify_product", job.CronNotifyProduct)

	router.OPTIONS("/test/cors", auth.EnableCors)
	router.GET("/test/cors", Cors)
	router.GET("/test/auth/id-token", GetIdToken)
	router.GET("/test/auth/public-key", GetPublicKey)
	router.GET("/test/auth/dev-key", GetDeveloperKey)
	router.GET("/test/cache", TestSavingCache)
	router.GET("/check/mem", CheckMemory)

	return router
}

func CheckMemory(ctx *fasthttp.RequestCtx) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	stats := map[string]uint64{
		"Alloc":      m.Alloc,
		"TotalAlloc": m.TotalAlloc,
		"Sys":        m.Sys,
		"NumGC":      uint64(m.NumGC),
	}

	json.NewEncoder(ctx).Encode(stats)
}

func Cors(ctx *fasthttp.RequestCtx) {
	fmt.Println("header req : ", ctx.Request.Header.String())
	fmt.Println("header res : ", ctx.Response.Header.String())
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func Index(ctx *fasthttp.RequestCtx) {
	ctx.Write([]byte("hello world..."))
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func CheckAPI(ctx *fasthttp.RequestCtx) {
	id := time.Now().Unix()
	fmt.Println("called...", id)
	// time.Sleep(5 * time.Second)
	ctx.Write([]byte(fmt.Sprintf("Iam api-crm, run environment : %s on port %s ", os.Getenv("ENV"), os.Getenv("PORT"))))
	ctx.SetStatusCode(fasthttp.StatusOK)
	fmt.Println("finish...", id)
}

func GetPublicKey(ctx *fasthttp.RequestCtx) {
	adminId := "17"
	publicKey := utils.Encrypt(adminId, utils.UID_PUBKEY)
	authKey, _ := utils.HashPassword(adminId)
	authKey = utils.Encrypt(authKey, utils.UID_PASSSERVER)
	fmt.Println("public-key: ", publicKey)
	fmt.Println("Authorization: Bearer ", authKey)
	fmt.Printf("'%v'\n", utils.Decrypt(publicKey, utils.UID_PUBKEY))
	ctx.Write([]byte(fmt.Sprintf("Iam api-crm, run environment : %s on port %s ", os.Getenv("server"), os.Getenv("PORT"))))
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func generateKey(adminId string) map[string]string {
	publicKey := utils.Encrypt(adminId, utils.UID_PUBKEY)
	authKey, _ := utils.HashPassword(adminId)
	authKey = utils.Encrypt(authKey, utils.UID_PASSSERVER)
	fmt.Println("public-key: ", publicKey)
	fmt.Println("Authorization: Bearer ", authKey)
	fmt.Printf("'%v'\n", utils.Decrypt(publicKey, utils.UID_PUBKEY))

	return map[string]string{
		"public-key": publicKey,
		"auth-key":   authKey,
	}
}

func GetDeveloperKey(ctx *fasthttp.RequestCtx) {
	auth := auth.InitJWTAuth()
	token := auth.GenerateToken("sub", "api-ai-recommendation", "scope", os.Getenv("ENV"), "exp", time.Now().Add(time.Hour*24*30*12*3).Unix())
	fmt.Println("token >>> ", token.Token)
	ctx.Write([]byte(fmt.Sprintf("Iam api-crm, run environment : %s on port %s ", os.Getenv("server"), os.Getenv("PORT"))))
	ctx.SetStatusCode(fasthttp.StatusOK)
}

var dataCache map[string]interface{}

func TestSavingCache(ctx *fasthttp.RequestCtx) {
	id := ctx.QueryArgs().Peek("token")

	if len(dataCache) > 50 || dataCache == nil {
		dataCache = make(map[string]interface{})
	}

	if data, ok := dataCache[string(id)]; ok {
		fmt.Println("use from cache, size ", len(dataCache))
		ctx.WriteString(utils.SimplyToJson(data))
		return
	}

	fmt.Println("set new cache..")
	dataCache[string(id)] = map[string]interface{}{
		"id":         id,
		"created_at": time.Now().Unix(),
	}
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.WriteString(utils.SimplyToJson(dataCache[string(id)]))
}
