package routes

import (
	"os"

	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	v2 "gitlab.com/uniqdev/backend/api-membership/controller/v2"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
)

func SetProductRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/v1/product", auth.ValidatePublicKey(v1.GetProduct))

	router.GET("/v1/product/availability/group/:group", auth.ValidatePublicKey(v1.GetProductWithAvailability))
	router.GET("/v1/product/availability", auth.ValidatePublicKey(v1.GetProductWithAvailability))

	//router.GET("/v1/product/availability/detail/:id", auth.ValidatePublicKey(v1.GetProductWithAvailability)) //auth.MultiMiddleware(v2.GetProductWithAvailability, auth.CORS, auth.ValidatePublicKey)
	router.GET("/v1/product/availability/detail/:id", auth.MultiMiddleware(v1.GetProductWithAvailability, auth.CORS, auth.ValidatePublicKey))
	router.OPTIONS("/v1/product/availability/detail/:id", auth.EnableCors)

	env := os.Getenv("ENV")
	useV2 := env == "localhost" || env == "development" || env == "staging"
	if useV2 {
		router.GET("/v2/product/availability", auth.ValidatePublicKey(v2.GetProductWithAvailabilityImproved))
		router.GET("/v2-1/product/availability", auth.ValidatePublicKey(v2.GetProductWithAvailability))
		router.GET("/v2/product/availability/:id", auth.MultiMiddleware(v2.GetProductWithAvailabilityImproved, auth.CORS, auth.ValidatePublicKey))
	} else {
		router.GET("/v2/product/availability", auth.ValidatePublicKey(v2.GetProductWithAvailability))
		router.GET("/v2/product/availability/:id", auth.MultiMiddleware(v2.GetProductWithAvailability, auth.CORS, auth.ValidatePublicKey))
	}

	router.GET("/v3/product/availability", auth.ValidatePublicKey(v2.GetProductWithAvailabilityImproved))

	//router.GET("/v2/product/availability/:id", auth.ValidatePublicKey(v2.GetProductWithAvailability))

	//router.GET("/v1/product/availability/detail/:productId", auth.ValidatePublicKey(v1.GetProductAvailability))
	router.GET("/v1/product/outlet/:outletId", auth.ValidatePublicKey(v1.GetProductByOutlet))
	router.GET("/v1/product/gratuity/:outletId", auth.ValidatePublicKey(v1.GetGratuityByOutletId))
	// router.GET("/v1/product/gratuity", auth.ValidatePublicKey(v1.GetGratuityByOutletId))

	//router.GET("/v1/product/url/:id", auth.ValidatePublicKey(v1.GetProductUrl))
	router.GET("/v1/product/url/:id", auth.MultiMiddleware(v1.GetProductUrl, auth.CORS, auth.ValidatePublicKey))
	router.OPTIONS("/v1/product/url/:id", auth.EnableCors)

	router.GET("/v1/product/outlet/:outletId/group/:group", auth.MultiMiddleware(v1.GetProductByOutlet, auth.CORS, auth.ValidatePublicKey))
	router.OPTIONS("/v1/product/outlet/:outletId/group/:group", auth.EnableCors)

	router.POST("/v1/product/search", auth.ValidateToken(v1.SaveSearchProductHistory))
	router.GET("/v1/product/search", auth.ValidateToken(v1.GetSearchHistory))

	//Purchase Order
	router.POST("/v1/product/po", auth.ValidateToken(v1.MakePurchaseOrder))
	router.GET("/v1/product/po/page/:page", auth.ValidateToken(v1.GetPurchaseOrderHistory))
	router.GET("/v1/product/po/", auth.ValidateToken(v1.GetPurchaseOrderHistory))

	router.POST("/v1/product/notify", auth.ValidateToken(v1.AddNotifyProductOutStock))
	router.GET("/v1/product/notify", auth.ValidateToken(v1.GetNotifyProductList))

	return router
}
