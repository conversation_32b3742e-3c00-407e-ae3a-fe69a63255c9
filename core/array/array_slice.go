package array

import (
	"fmt"
	"reflect"
)

func Contain(data, search interface{}) bool {
	if s := reflect.ValueOf(data); s.Kind() == reflect.Slice {
		for i := 0; i < s.Len(); i++ {
			// fmt.Printf(" '%v' VS '%v' | ", s.Index(i).Interface(), search)
			if s.Index(i).Interface() == search {
				return true
			}
		}
	} else {
		fmt.Println("array.Contain warn: not slice")
	}
	return false
}

func ChunkMapArray(data []map[string]interface{}, max int) [][]map[string]interface{} {
	result := make([][]map[string]interface{}, 0)
	for i := 0; i <= len(data); i += max {
		j := i + max
		if j > len(data) {
			j = len(data)
		}
		result = append(result, data[i:j])
	}

	return result
}
