package array

import (
	"reflect"
	"testing"
)

func TestTakeValuesByKey(t *testing.T) {
	type args struct {
		data interface{}
		key  interface{}
	}
	tests := []struct {
		name string
		args args
		want []interface{}
	}{
		{"single map", args{data: map[string]interface{}{
			"id": 5,
		}, key: "id"}, []interface{}{5}},
		{"array map", args{data: []map[string]interface{}{
			{"id": 5},
			{"id": 6},
		}, key: "id"}, []interface{}{5, 6}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TakeValuesByKey(tt.args.data, tt.args.key); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rrorf("TakeValuesByKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGroupBy(t *testing.T) {
	dataArr1 := []map[string]interface{}{
		{"id": 1, "point": 5},
		{"id": 1, "point": 10},
		{"id": 2, "point": 3},
	}
	dataArrGroup1 := make(map[string][]map[string]interface{})
	dataArrGroup1["1"] = make([]map[string]interface{}, 0)
	dataArrGroup1["1"] = append(dataArrGroup1["1"], dataArr1[0])
	dataArrGroup1["1"] = append(dataArrGroup1["1"], dataArr1[1])

	dataArrGroup1["2"] = make([]map[string]interface{}, 0)
	dataArrGroup1["2"] = append(dataArrGroup1["2"], dataArr1[2])

	type args struct {
		data     []map[string]interface{}
		groupKey string
	}
	tests := []struct {
		name string
		args args
		want map[string][]map[string]interface{}
	}{
		{"test1", args{data: dataArr1, groupKey: "id"}, dataArrGroup1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GroupBy(tt.args.data, tt.args.groupKey); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GroupBy() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetKeysOfMapInt(t *testing.T) {
	arg := map[int]int{1: 1, 2: 1}
	want := []int{1, 2}
	if got := GetKeysOfMap(arg); !reflect.DeepEqual(got, want) {
		t.Errorf("GetKeysOfMap() = %v, want %v", got, want)
	}
}

func TestGetKeysOfMapStr(t *testing.T) {
	arg := map[string]int{"one": 1, "two": 2}
	want := []string{"one", "two"}
	if got := GetKeysOfMap(arg); !reflect.DeepEqual(got, want) {
		t.Errorf("GetKeysOfMap() = %v, want %v", got, want)
	}
}

func TestTakeOnly(t *testing.T) {
	type args struct {
		data map[string]interface{}
		keys []string
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		{"test1", args{data: map[string]interface{}{"id": 1, "name": "test", "age": 20}, keys: []string{"id", "name"}}, map[string]interface{}{"id": 1, "name": "test"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TakeOnly(tt.args.data, tt.args.keys...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TakeOnly() = %v, want %v", got, tt.want)
			}
		})
	}
}
