package array

import (
	"fmt"
	"reflect"
	"strings"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
)

// FromMapKey returning keys from map as array
func FromMapKey(data map[string]interface{}) []interface{} {
	result := make([]interface{}, 0)
	for k, _ := range data {
		result = append(result, k)
	}
	return result
}

func GetKeys(data interface{}) []interface{} {
	result := make([]interface{}, 0)
	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result = append(result, key.Interface())
		}
	}
	return result
}

func GetKeysOfMap[T comparable, V any](data map[T]V) []T {
	result := make([]T, 0)
	for key := range data {
		result = append(result, key)
	}
	return result
}

func FlatMapArray(data []map[string]interface{}, key string) map[string]map[string]interface{} {
	result := make(map[string]map[string]interface{})
	for _, row := range data {
		if row[key] == nil {
			continue
		}
		result[cast.ToString(row[key])] = row
	}
	return result
}

// func Group(data []map[string]interface{}, key string) map[string][]map[string]interface{} {
// 	result := make(map[string][]map[string]interface{})
// 	for _, row := range data {
// 		if row[key] == nil {
// 			continue
// 		}
// 		if result[cast.ToString(row[key])] == nil {
// 			result[cast.ToString(row[key])] = make([]map[string]interface{}, 0)
// 		}
// 		result[cast.ToString(row[key])] = append(result[cast.ToString(row[key])], row)
// 	}
// 	return result
// }

func Copy(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range data {
		result[k] = v
	}
	return result
}

func CopyData(data interface{}) interface{} {
	result := make(map[string]interface{}, 0)
	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result[key.String()] = v.MapIndex(key).Interface()
		}
	}
	return result
}

func GetSafe(data []map[string]interface{}, index int) map[string]interface{} {
	if len(data) > index {
		return data[index]
	}
	return map[string]interface{}{}
}

func Join(separator string, data ...interface{}) string {
	result := make([]string, 0)
	for _, row := range data {
		if str := cast.ToString(row); str != "" {
			result = append(result, str)
		}
	}
	return strings.Join(result, separator)
}

// return map with specific key only
func TakeOnly(data map[string]interface{}, keys ...string) map[string]interface{} {
	result := make(map[string]interface{}, len(keys))
	for _, key := range keys {
		result[key] = data[key]
	}
	return result
}

func TakeJust(data interface{}, keys ...string) interface{} {
	v := reflect.ValueOf(data)

	switch v.Kind() {
	case reflect.Map:
		return takeOnlyMap(data.(map[string]interface{}), keys...)
	case reflect.Slice:
		return takeOnlySlice(data.([]map[string]interface{}), keys...)
	default:
		return nil // or handle error as appropriate
	}
}

func takeOnlyMap(data map[string]interface{}, keys ...string) map[string]interface{} {
	result := make(map[string]interface{})
	for _, key := range keys {
		if value, exists := data[key]; exists {
			result[key] = value
		}
	}
	return result
}

func takeOnlySlice(data []map[string]interface{}, keys ...string) []map[string]interface{} {
	result := make([]map[string]interface{}, len(data))
	for i, item := range data {
		result[i] = takeOnlyMap(item, keys...)
	}
	return result
}

func TakeValues(data interface{}) []interface{} {
	result := make([]interface{}, 0)
	// for _, v := range data {
	// 	result = append(result, v)
	// }

	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result = append(result, v.MapIndex(key).Interface())
		}
	}
	return result
}

func TakeValuesByKey(data, key interface{}) []interface{} {
	result := make([]interface{}, 0)
	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, dataKey := range v.MapKeys() {
			fmt.Println("-- key: ", dataKey.String())
			if dataKey.String() == key {
				result = append(result, v.MapIndex(dataKey).Interface())
			}
		}
	} else {
		fmt.Println("TakeValuesByKey: data is not map but ", reflect.TypeOf(data).Kind())
	}
	return result
}

func GroupBy(data []map[string]interface{}, groupKey string) map[string][]map[string]interface{} {
	result := make(map[string][]map[string]interface{})
	if len(data) == 0 {
		return result
	}

	for _, row := range data {
		if _, ok := result[cast.ToString(row[groupKey])]; !ok {
			result[cast.ToString(row[groupKey])] = make([]map[string]interface{}, 0)
		}
		result[cast.ToString(row[groupKey])] = append(result[cast.ToString(row[groupKey])], row)
	}
	return result
}

func MergeMapInplace(data map[string]interface{}, others ...map[string]interface{}) {
	for _, dataMap := range others {
		for k, v := range dataMap {
			data[k] = v
		}
	}
}
