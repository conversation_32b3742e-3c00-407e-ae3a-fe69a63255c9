package array

import (
	"fmt"
	"testing"
)

func TestContain(t *testing.T) {
	type args struct {
		data   interface{}
		search interface{}
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{"match", args{data: []string{"delivery", "pickup"}, search: "pickup"}, true},
		{"not-match", args{data: []string{"delivery", "pickup"}, search: "self-order"}, false},
		{"match-integer", args{data: []int{1, 2, 3}, search: 3}, true},
		{"not-match-integer", args{data: []int{1, 2, 3}, search: 4}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Contain(tt.args.data, tt.args.search); got != tt.want {
				t.<PERSON>rrorf("Contain() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChunkMapArray(t *testing.T) {
	tempData := make([]map[string]interface{}, 0)
	for i := 0; i <= 10003; i++ {
		tempData = append(tempData, map[string]interface{}{
			"id": i,
		})
	}

	type args struct {
		data []map[string]interface{}
		max  int
	}
	tests := []struct {
		name string
		args args
	}{
		{"per-5000", args{data: tempData, max: 5000}},
		{"per-5010", args{data: tempData, max: 5010}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ChunkMapArray(tt.args.data, tt.args.max)
			total := 0
			for _, v := range got {
				total += len(v)
			}
			if total != len(tt.args.data) {
				t.Errorf("ChunkMapArray() return %v data, want %v", total, len(tt.args.data))
			}
			fmt.Println("got as we expected!")
		})
	}
}
