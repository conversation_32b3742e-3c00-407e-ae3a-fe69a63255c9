package auth

type AuthTokenData struct {
	Data struct {
		User struct {
			AccountID    string `json:"account_id"`
			BusinessID   string `json:"business_id"`
			BusinessName string `json:"business_name"`
			Email        string `json:"email"`
			Name         string `json:"name"`
			Phone        string `json:"phone"`
			UserID       string `json:"user_id"`
			UserType     string `json:"user_type"`
		} `json:"user"`
		UserRole struct {
			OutletAccess string `json:"outlet_access"`
		} `json:"user_role"`
	} `json:"data"`
	Exp int    `json:"exp"`
	Iat int    `json:"iat"`
	Jti string `json:"jti"`
	Nbf int    `json:"nbf"`
}
