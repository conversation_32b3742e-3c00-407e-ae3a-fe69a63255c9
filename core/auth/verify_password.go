package auth

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	net "net/url"
)

type verify struct {
	Status bool `json:"status"`
}

func VerifyPassword(password, hash string) bool {
	data := net.Values{}
	data.Set("password", password)
	data.Set("hash", hash)

	res, err := http.PostForm("http://verify.uniq.id", data)
	if err != nil {
		fmt.Println("Error ", err)
		return false
	}

	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)

	verifyPswd := new(verify)
	json.Unmarshal(body, &verifyPswd)

	return verifyPswd.Status
}
