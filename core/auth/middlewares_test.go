package auth

import (
	"strconv"
	"testing"

	"github.com/valyala/fasthttp"
)

func Test_claimToken(t *testing.T) {
	publicRequest := new(fasthttp.RequestCtx)
	publicRequest.Request.Header.Add("Public-Key", "Y6H4kpYjWVEpn5YaGjJ-x8Y=")
	publicRequest.Request.Header.Add("Authorization", "Y6H4kpYjWVEpn5YaGjJ-x8Y=")

	publicRequestInvalid := new(fasthttp.RequestCtx)
	publicRequestInvalid.Request.Header.Add("Public-Key", "Xxxxxxxx")

	privateRequest := new(fasthttp.RequestCtx)
	privateRequest.Request.Header.Add("Authorization", "")

	jwtAuthInstance = &JWTAuthBackend{
		privateKey: ReadPrivateKey("/Users/<USER>/Documents/WORK/api-crm/config/auth/app.rsa"),
		PublicKey:  getPub<PERSON><PERSON><PERSON>("/Users/<USER>/Documents/WORK/api-crm/config/auth/app.rsa.pub"),
	}

	type args struct {
		ctx     *fasthttp.RequestCtx
		reqType string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{"test-valid-public-access", args{publicRequest, "public"}, true},
		{"test-invalid-public-access", args{publicRequestInvalid, "public"}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := claimToken(tt.args.ctx, tt.args.reqType)
			if got != tt.want {
				t.Errorf("claimToken() = %v, want %v", got, tt.want)
			}

			//for valid request, uid must be set as a header
			if got && tt.args.reqType == "public" {
				result, err := strconv.Atoi(string(tt.args.ctx.Request.Header.Peek("uid")))
				if err != nil {
					t.Error(err)
				}
				if result != 7 {
					t.Errorf("result =%d, want 7", result)
				}
			}
		})
	}
}
