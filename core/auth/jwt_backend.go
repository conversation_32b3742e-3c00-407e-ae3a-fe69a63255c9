package auth

import (
	"bufio"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/dgrijalva/jwt-go"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type JWTAuthBackend struct {
	privateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
}

var jwtAuthInstance *JWTAuthBackend = nil

const (
	RSA_PRIVATE_KEY = "config/auth/app.rsa"
	RSA_PUBLIC_KEY  = "config/auth/app.rsa.pub"
)

func InitJWTAuth() *JWTAuthBackend {
	if jwtAuthInstance == nil {
		checkOrCreateKeys()
		jwtAuthInstance = &JWTAuthBackend{
			privateKey: ReadPrivateKey(RSA_PRIVATE_KEY),
			PublicKey:  getPub<PERSON><PERSON>ey(RSA_PUBLIC_KEY),
		}
	}
	return jwtAuthInstance
}

func (auth *JWTAuthBackend) GenerateToken(dataClaim ...interface{}) *models.AuthToken {
	expired := time.Now().Add(time.Minute * 60).Unix()
	if os.Getenv("ENV") == "development" { //for testing
		// expired = time.Now().Add(time.Minute * 3).Unix()
	}
	token := jwt.New(jwt.SigningMethodRS512)

	claims := jwt.MapClaims{}
	claims["exp"] = expired
	claims["iat"] = time.Now().Unix()
	claims["scope"] = fmt.Sprintf("uniq-crm-%v", os.Getenv("ENV"))

	fmt.Println("total claims: ", len(dataClaim), dataClaim)
	if len(dataClaim) > 1 {
		for i := 0; i < len(dataClaim); i += 2 {
			claims[utils.ToString(dataClaim[i])] = dataClaim[i+1]
			fmt.Println("adding", dataClaim[i], dataClaim[i+1])
		}
	}

	token.Claims = claims

	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if err != nil {
		panic(err)
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired
	return authToken
}

func ReadPrivateKey(keyPath string) *rsa.PrivateKey {
	privateKeyFile, err := os.Open(keyPath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := privateKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(privateKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode([]byte(pemBytes))

	privateKeyFile.Close()

	privateKeyImported, err := x509.ParsePKCS1PrivateKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	return privateKeyImported
}

func getPublicKey(keyPath string) *rsa.PublicKey {
	publicKeyFile, err := os.Open(keyPath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := publicKeyFile.Stat()
	var size int64 = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(publicKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode([]byte(pemBytes))

	publicKeyFile.Close()

	publicKeyImported, err := x509.ParsePKIXPublicKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	rsaPub, ok := publicKeyImported.(*rsa.PublicKey)

	if !ok {
		panic(err)
	}

	return rsaPub
}

func checkOrCreateKeys() {
	if os.Getenv("ENV") != "localhost" {
		return
	}


	// Check if the file exists
	if _, err := os.Stat(RSA_PRIVATE_KEY); os.IsNotExist(err) {
		fmt.Println("File does not exist:", RSA_PRIVATE_KEY)
	} else {
		fmt.Println("File exists:", RSA_PRIVATE_KEY)
		return
	}

	// Generate a new RSA key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Println("Error generating key:", err)
		return
	}

	// Encode the private key in PEM format
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}
	privateKeyFile, err := os.Create(RSA_PRIVATE_KEY)
	if err != nil {
		fmt.Println("Error creating private key file:", err)
		return
	}
	defer privateKeyFile.Close()
	if err := pem.Encode(privateKeyFile, privateKeyBlock); err != nil {
		fmt.Println("Error encoding private key:", err)
		return
	}

	// Encode the public key in PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		fmt.Println("Error marshalling public key:", err)
		return
	}
	publicKeyBlock := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}
	publicKeyFile, err := os.Create(RSA_PUBLIC_KEY)
	if err != nil {
		fmt.Println("Error creating public key file:", err)
		return
	}
	defer publicKeyFile.Close()
	if err := pem.Encode(publicKeyFile, publicKeyBlock); err != nil {
		fmt.Println("Error encoding public key:", err)
		return
	}

	fmt.Println("RSA key pair generated successfully!")
}
