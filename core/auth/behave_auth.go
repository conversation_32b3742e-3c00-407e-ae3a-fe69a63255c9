package auth

import (
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"github.com/dgrijalva/jwt-go/request"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"net/http"
	"time"
)

func (auth *JWTAuthBackend) GenerateBehaveToken(dataClaim ...interface{}) *models.AuthToken {
	expired := time.Now().Add(time.Minute * 60).Unix()
	token := jwt.New(jwt.SigningMethodRS512)

	claims := jwt.MapClaims{}
	claims["exp"] = expired
	claims["iat"] = time.Now().Unix()
	claims["scope"] = "behave"

	if len(dataClaim) > 1 {
		for i := 0; i <= (len(dataClaim) / 2); i += 2 {
			claims[utils.ToString(dataClaim[i])] = dataClaim[i+1]
		}
	}

	token.Claims = claims

	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if err != nil {
		panic(err)
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired
	return authToken
}

func ValidateBehaveToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx) {
		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", string(ctx.Request.Header.Peek("Authorization")))
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})

		if err == nil && token.Valid {
			claims := token.Claims.(jwt.MapClaims)
			if claims["scope"] == "behave" {
				ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
				ctx.Response.Header.Set("X-Frame-Options", "DENY")
				ctx.SetContentType("application/json")
				next(ctx)
				return
			}
		}

		ctx.Response.Header.Set("WWW-Authenticate", "Basic realm=Restricted")
		ctx.Error(fasthttp.StatusMessage(fasthttp.StatusUnauthorized), fasthttp.StatusUnauthorized)
	})
}
