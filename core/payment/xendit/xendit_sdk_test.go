package xendit

import (
	"fmt"
	"reflect"
	"testing"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/models"
)

func TestConfig_CheckPaymentQris(t *testing.T) {
	secretKey := "xnd_development_ziEL7uDxQzRM6RfM4XiqtJV93FUZF6Lb8gR0bvGcUNHrxeNF9mSPBssmVALZy"

	type fields struct {
		SecretKey string
	}
	type args struct {
		orderId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{"test1", fields{SecretKey: secretKey}, args{orderId: fmt.Sprintf("%v", time.Now().Unix())}, nil, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := Config{
				SecretKey: tt.fields.SecretKey,
			}
			got, err := c.CheckPayment<PERSON>ris(tt.args.orderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Config.CheckPaymentQris() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Config.CheckPaymentQris() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfig_CreatePaymentQris(t *testing.T) {
	type fields struct {
		SecretKey string
	}
	type args struct {
		payment models.Payment
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    models.PaymentResult
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := Config{
				SecretKey: tt.fields.SecretKey,
			}
			got, err := c.CreatePaymentQris(tt.args.payment)
			if (err != nil) != tt.wantErr {
				t.Errorf("Config.CreatePaymentQris() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Config.CreatePaymentQris() = %v, want %v", got, tt.want)
			}
		})
	}
}
