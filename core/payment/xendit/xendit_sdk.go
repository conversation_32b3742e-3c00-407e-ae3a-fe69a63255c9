package xendit

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type Config struct {
	SecretKey string
}

func Default() Config {
	return Config{
		SecretKey: os.Getenv("XENDIT_SECRET_KEY"),
	}
}

func (c Config) CheckPaymentQris(orderId string) (map[string]interface{}, error) {
	req := utils.HttpRequest{
		Method: "GET",
		Url:    fmt.Sprintf("https://api.xendit.co/qr_codes/%s", orderId),
	}
	req.SetBasicAuth(c.<PERSON>ey, "")
	resp, err := req.Execute()
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == 404 {
		return nil, exception.WithCode{
			Code:    404,
			Message: "not found",
		}
	}

	var result map[string]interface{}
	err = json.Unmarshal([]byte(resp.Body), &result)
	return result, err
}

func (c Config) CreatePaymentQris(payment models.Payment) (models.PaymentResult, error) {
	var result map[string]interface{}
	var err error
	statusCode := 0
	result, err = c.CheckPaymentQris(payment.OrderId)
	if err != nil {
		if errCode, ok := err.(exception.WithCode); ok {
			if errCode.Code == 404 {
				//payment not found or not created...
				statusCode = errCode.Code
			} else {
				return models.PaymentResult{}, err
			}
		} else {
			return models.PaymentResult{}, err
		}
	}

	response := ""
	if statusCode == 404 {
		req := utils.HttpRequest{
			Method: "POST",
			Url:    "https://api.xendit.co/qr_codes",
			PostRequest: utils.PostRequest{Form: map[string]string{
				"external_id":  payment.OrderId,
				"type":         "DYNAMIC",
				"callback_url": "https://uniq.id",
				"amount":       utils.ToString(payment.GranTotal),
			}},
		}
		req.SetBasicAuth(c.SecretKey, "")
		resp, err := req.Execute()
		if err != nil {
			return models.PaymentResult{}, err
		}

		fmt.Println("xendit resp -> ", resp.Body)
		response = resp.Body
		err = json.Unmarshal([]byte(resp.Body), &result)
		log.IfError(err)
	}

	return models.PaymentResult{
		QrCode:   utils.ToString(result["qr_string"]),
		Response: response,
	}, nil
}
