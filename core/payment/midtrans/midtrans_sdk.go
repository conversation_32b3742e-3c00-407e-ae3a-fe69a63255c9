package midtrans

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

const (
	PAYMENT_NOT_FOUND     = "404"
	PAYMENT_STATUS_CANCEL = "cancel"
)

var TmpQrUrl = make(map[string]string)

type Config struct {
	ServerKey string
}

func Default() Config {
	return Config{
		ServerKey: os.Getenv("MIDTRANS_SECRET_KEY"),
	}
}

func baseUrl() string {
	if os.Getenv("server") == "production" {
		return "https://api.midtrans.com"
	}
	return "https://api.sandbox.midtrans.com"
}

func (c Config) CheckPaymentStatus(transactionID string) (models.PaymentStatus, error) {
	paymentStatus := models.PaymentStatus{}
	request := utils.HttpRequest{
		Method: "GET",
		Url:    fmt.Sprintf("%s/v2/%s/status", baseUrl(), transactionID),
		Header: map[string]interface{}{
			"Content-Type": "application/json",
			"Accept":       "application/json",
		},
	}
	request.SetBasicAuth(c.ServerKey, "")
	body, err := request.ExecuteRequest()
	if err != nil {
		return paymentStatus, err
	}

	fmt.Printf("[MIDTRANS] check response body: %s\n", string(body))
	err = json.Unmarshal(body, &paymentStatus)
	if err != nil {
		return paymentStatus, err
	}
	return paymentStatus, err
}

func (c Config) UpdatePaymentStatus(transactionId, status string) (models.MidtransResponse, error) {
	var result models.MidtransResponse
	request := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("%s/v2/%s/%s", baseUrl(), transactionId, status),
		Header: map[string]interface{}{
			"Content-Type":  "application/json",
			"Accept":        "application/json",
			"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:", c.ServerKey))),
		},
	}

	body, err := request.ExecuteRequest()
	if err != nil {
		return result, err
	}

	fmt.Printf("[MIDTRANS] update response body: %s\n", string(body))
	err = json.Unmarshal(body, &result)
	if err != nil {
		return result, err
	}
	return result, err
}

func (c Config) CreateQrisPayment(payment models.Payment) (models.PaymentResult, error) {
	paymentStatus, err := c.CheckPaymentStatus(payment.OrderId)
	if err != nil {
		return models.PaymentResult{}, err
	}

	//if pending cancel to create new
	log.Info("payment status for '%s' -> %s", payment.OrderId, paymentStatus.TransactionStatus)
	if paymentStatus.TransactionStatus == "pending" {
		_, err = c.UpdatePaymentStatus(payment.OrderId, PAYMENT_STATUS_CANCEL)
		if err != nil {
			return models.PaymentResult{}, err
		}
	}

	data := map[string]interface{}{
		"payment_type": "qris",
		"qris": map[string]interface{}{
			"acquirer": "airpay shopee", //--> airpay shopee, gopay
		},
		"transaction_details": map[string]interface{}{
			"order_id":     payment.OrderId,
			"gross_amount": payment.GranTotal,
		},
		"customer_details": map[string]interface{}{
			"first_name": payment.Customer.Name,
			"email":      payment.Customer.Email,
			"phone":      payment.Customer.Phone,
		},
	}

	req := utils.HttpRequest{
		Method: "POST",
		Url:    baseUrl() + "/v2/charge",
		Header: map[string]interface{}{
			"Content-Type": "application/json",
		},
		PostRequest: utils.PostRequest{
			Body: data,
		},
	}
	req.SetBasicAuth(c.ServerKey, "")

	resp, err := req.Execute()
	if err != nil {
		return models.PaymentResult{}, err
	}

	fmt.Println("midtrans resp --> ", resp.Body)
	if resp.StatusCode != 200 {
		err = fmt.Errorf("request to midtrans err, code %d - %s", resp.StatusCode, resp.Body)
		log.IfError(err)
		return models.PaymentResult{}, err
	}

	var result map[string]interface{}
	err = json.Unmarshal([]byte(resp.Body), &result)
	if err != nil {
		return models.PaymentResult{}, err
	}

	if code, ok := result["status_code"]; ok {
		if utils.ToInt(code) >= 300 {
			err = fmt.Errorf("request failed. response : %s", resp.Body)
			fmt.Println("midtrans return err code >= 300")
			return models.PaymentResult{}, err
		}
	}

	urlQr := ""
	if actions, ok := result["actions"].([]interface{}); ok {
		if action1, ok1 := actions[0].(map[string]interface{}); ok1 {
			urlQr = utils.ToString(action1["url"])
			log.Info("url qr : %s", urlQr)
		}
	}

	fmt.Println("url qr --> ", urlQr)
	if os.Getenv("server") == "development" {
		TmpQrUrl[payment.OrderId] = urlQr
	}

	return models.PaymentResult{
		QrCode:   utils.ToString(result["qr_string"]),
		Response: resp.Body,
	}, nil
}
