package parser

import (
	"github.com/valyala/fasthttp"
)

func ParseParamFastHttp(ctx *fasthttp.RequestCtx) map[string]interface{} {
	result := make(map[string]interface{})
	if ctx.IsPost() {
		ctx.PostArgs().VisitAll(func(key, value []byte) {
			result[string(key)] = string(value)
		})
	} else if ctx.IsGet() {
		ctx.QueryArgs().VisitAll(func(key, value []byte) {
			result[string(key)] = string(value)
		})
	}
	return result
}
