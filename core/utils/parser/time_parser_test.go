package parser

import "testing"

func TestToTimeMillis(t *testing.T) {
	type args struct {
		time string
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{"hourAndMonute", args{time: "12:30"}, 0},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToTimeMillis(tt.args.time); got != tt.want {
				t.<PERSON><PERSON>rf("ToTimeMillis() = %v, want %v", got, tt.want)
			}
		})
	}
}
