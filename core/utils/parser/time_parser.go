package parser

import (
	"regexp"
	"strconv"
)

func ToTimeMillis(time string) int64 {
	re := regexp.MustCompile(`([0-9]{2}):([0-9]{2})`)
	if re.MatchString(time) {
		matches := re.FindStringSubmatch(time)
		hours := 0
		minutes := 0
		if len(matches) > 1 {
			hours, _ = strconv.Atoi(matches[1])
		}
		if len(matches) > 2 {
			minutes, _ = strconv.Atoi(matches[2])
		}
		return int64(hours*3600+minutes*60) * 1000
	}

	return 0
}
