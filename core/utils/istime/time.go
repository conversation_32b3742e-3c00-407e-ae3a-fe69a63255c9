package istime

import (
	"fmt"
	"strings"
	"time"
)

func Between(startTime, endTime string, timeOffset int) bool {
	// Add padding to the time strings if needed (format: "15:04:05")
	startTime = addPaddingToTime(startTime)
	endTime = addPaddingToTime(endTime)

	if startTime == "00:00:00" {
		startTime = "00:00:01"
	}

	// Get the current time
	currentTime := time.Now().UTC().Add(time.Duration(timeOffset) * time.Hour)

	// Parse the start and end times as time.Time objects
	layout := "15:04:05" // 24-hour time format
	start, err := time.Parse(layout, startTime)
	if err != nil {
		fmt.Println("Error parsing start time:", err)
		return false
	}

	end, err := time.Parse(layout, endTime)
	if err != nil {
		fmt.Println("Error parsing end time:", err)
		return false
	}

	// Convert the current time to minutes since midnight
	currentMinutes := currentTime.Hour()*60 + currentTime.Minute()

	// Convert the start and end times to minutes since midnight
	startMinutes := start.Hour()*60 + start.Minute()
	endMinutes := end.Hour()*60 + end.Minute()

	// Check if the current time is between the start and end times
	return currentMinutes >= startMinutes && currentMinutes <= endMinutes

	// Check if the current time is between the start and end times
	// return currentTime.After(start) && currentTime.Before(end)
}

// addPaddingToTime adds ":00" to the time string if it's in "15:04" format.
func addPaddingToTime(t string) string {
	if len(t) == 5 && strings.Count(t, ":") == 1 {
		return t + ":00"
	}
	return t
}

// Description:
//
//	The After function checks if a given date and time are after the current time in the specified timezone.
//	It takes three arguments: dateInMillis, timeStr, and timezoneOffset.
//
// Functions:
//
//	After(dateInMillis int64, timeStr string, timezoneOffset int) bool
//
//	    The After function first converts the date in milliseconds to a time.Time object and adds the timezone offset.
//	    It then parses the input time string and gets the current time with the timezone offset applied.
//	    The function checks if the date is today and, if so, combines the input time with today's date and checks if it's greater than the current time.
//	    Finally, it returns the result of this comparison.
//
//	    Parameters:
//	    - dateInMillis (int64): The date in Unix milliseconds.
//	    - timeStr (string): The time in the format "15:04:05".
//	    - timezoneOffset (int): The timezone offset in seconds from UTC.
//
//	    Return value:
//	    - bool: true if the given date and time are after the current time in the specified timezone, false otherwise.
func After(dateInMillis int64, timeStr string, timezoneOffset int) bool {
	// Convert the date in milliseconds to a time.Time object
	dateTime := time.Unix(0, dateInMillis*int64(time.Millisecond))

	// Add the timezone offset to the dateTime
	location := time.FixedZone("Custom", int(timezoneOffset))
	dateTimeInZone := dateTime.In(location)

	// Parse the input time string
	inputTime, err := time.Parse("15:04:05", timeStr)
	if err != nil {
		fmt.Println("Error parsing input time:", err)
		return false
	}

	// Get the current time and add the timezone offset
	currentTime := time.Now().In(location)

	// Check if the date is today
	isToday := currentTime.Year() == dateTimeInZone.Year() && currentTime.Month() == dateTimeInZone.Month() && currentTime.Day() == dateTimeInZone.Day()
	fmt.Println("currentTime", currentTime)
	if isToday {
		// Combine the input time with today's date, preserving the original time if it's 00:00:00
		inputTimeToday := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), inputTime.Hour(), inputTime.Minute(), inputTime.Second(), 0, location)
		if dateTimeInZone.Hour() == 0 && dateTimeInZone.Minute() == 0 && dateTimeInZone.Second() == 0 {
			inputTimeToday = dateTimeInZone
		}

		// Check if the input time is greater than the current time
		return inputTimeToday.After(currentTime)
	}

	return false
}
