package istime

import (
	"testing"
	"time"
)

func TestBetween(t *testing.T) {
	type args struct {
		startTime  string
		endTime    string
		timeOffset int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{"in-between", args{"09:00", "19:00", 7}, true},
		{"not-in-between", args{"09:00", "15:00", 7}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Between(tt.args.startTime, tt.args.endTime, tt.args.timeOffset); got != tt.want {
				t.Errorf("Between() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestAfter(t *testing.T) {
	type args struct {
		dateInMillis   int64
		timeStr        string
		timezoneOffset int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test today with time greater than current time",
			args: args{
				dateInMillis:   1709485200000, //time.Now().Unix() * 1000, //time.Now().UnixNano() / int64(time.Millisecond),
				timeStr:        "23:59:59",
				timezoneOffset: 27200,
			},
			want: true,
		},
		{
			name: "Test today with time less than current time",
			args: args{
				dateInMillis:   time.Now().UnixNano() / int64(time.Millisecond),
				timeStr:        "00:00:01",
				timezoneOffset: 27200,
			},
			want: false,
		},
		{
			name: "Test tomorrow with time greater than current time",
			args: args{
				dateInMillis:   time.Now().AddDate(0, 0, 1).UnixNano() / int64(time.Millisecond),
				timeStr:        "23:59:59",
				timezoneOffset: 27200,
			},
			want: false,
		},
		{
			name: "Test yesterday with time less than current time",
			args: args{
				dateInMillis:   time.Now().AddDate(0, 0, -1).UnixNano() / int64(time.Millisecond),
				timeStr:        "00:00:01",
				timezoneOffset: 27200,
			},
			want: false,
		},
		{
			name: "Test timezone offset for UTC+7",
			args: args{
				dateInMillis:   time.Now().UnixNano() / int64(time.Millisecond),
				timeStr:        "11:00:00",
				timezoneOffset: 27200,
			},
			want: time.Now().In(time.FixedZone("Custom", 27200*60)).Hour() < 11,
		},
		{
			name: "Test timezone offset for UTC-5",
			args: args{
				dateInMillis:   time.Now().UnixNano() / int64(time.Millisecond),
				timeStr:        "11:00:00",
				timezoneOffset: -18000,
			},
			want: time.Now().In(time.FixedZone("Custom", -18000*60)).Hour() < 11,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := After(tt.args.dateInMillis, tt.args.timeStr, tt.args.timezoneOffset); got != tt.want {
				t.Errorf("After() = %v, want %v", got, tt.want)
			}
		})
	}
}
