package utils

import (
	"log"
	"reflect"
	"testing"
)

func TestRemvoveEmptyFields(t *testing.T) {
	type args struct {
		obj map[string]interface{}
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		{"test 1", args{
			map[string]interface{}{"id": "", "name": "andi", "age": 0},
		}, map[string]interface{}{"name:": "andi"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemvoveEmptyFields(tt.args.obj); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemvoveEmptyFields() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFormatPhoneNumber(t *testing.T) {
	type args struct {
		phone interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{phone: "0857422"}, "62857422"},
		{"test2", args{phone: "857422"}, "62857422"},
		{"test3", args{phone: "620857422"}, "62857422"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FormatPhoneNumber(tt.args.phone); got != tt.want {
				t.Errorf("FormatPhoneNumber() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStripHtmlRegex(t *testing.T) {
	html1 := `<p>promo makan nasi gratis lauk&nbsp;</p><p>hanya berlaku di hari ini dan jam tadi.</p><p>kalau sekarang ya gak promo lagi.</p><p>1</p><p>2</p><p>3</p><p>4</p><p>5</p><p>selamat mas&nbsp;#nama</p>`
	text1 := `promo makan nasi gratis lauk 

	hanya berlaku di hari ini dan jam tadi.
	
	kalau sekarang ya gak promo lagi.
	
	1
	
	2
	
	3
	
	4
	
	5
	
	selamat mas #nama`

	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{html1}, text1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StripHtmlRegex(tt.args.s); got != tt.want {
				t.Errorf("StripHtmlRegex() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExtractHtml(t *testing.T) {
	html1 := `<p>promo makan nasi gratis lauk&nbsp;</p><p>hanya berlaku di hari ini dan jam tadi.</p><p>kalau sekarang ya gak promo lagi.</p><p>1</p><p>2</p><p>3</p><p>4</p><p>5</p><p>selamat mas&nbsp;#nama</p>`
	text1 := `promo makan nasi gratis lauk 
	hanya berlaku di hari ini dan jam tadi.	
	kalau sekarang ya gak promo lagi.	
	1	
	2	
	3	
	4	
	5	
	selamat mas #nama`

	noHtml1 := "hello"

	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("test..")

	type args struct {
		htmlContent string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{html1}, text1},
		{"test-nohtml", args{noHtml1}, noHtml1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ExtractHtml(tt.args.htmlContent); got != tt.want {
				t.Errorf("ExtractHtml() = '%v', want '%v'", got, tt.want)
			}
		})
	}
}

// Should return true for integer input
func TestIsNumber_IntegerInput(t *testing.T) {
	data := 10
	result := IsNumber(data)
	if !result {
		t.Errorf("Expected true, but got false")
	}
}

// Should return true for negative integer input
func TestIsNumber_NegativeIntegerInput(t *testing.T) {
	data := -5
	result := IsNumber(data)
	if !result {
		t.Errorf("Expected true, but got false")
	}
}

// Should return true for string integer input
func TestIsNumber_StringIntegerInput(t *testing.T) {
	data := "015"
	result := IsNumber(data)
	if !result {
		t.Errorf("Expected true, but got false")
	}
}

// Should return false for string with leading/trailing spaces
func TestIsNumber_StringWithSpaces(t *testing.T) {
	data := " 10 "
	result := IsNumber(data)
	if result {
		t.Errorf("Expected false, but got true")
	}
}

// Should return false for string with non-numeric characters
func TestIsNumber_StringWithNonNumericCharacters(t *testing.T) {
	data := "abc123"
	result := IsNumber(data)
	if result {
		t.Errorf("Expected false, but got true")
	}
}

// Should return false for float input
func TestIsNumber_FloatInput(t *testing.T) {
	data := 3.14
	result := IsNumber(data)
	if result {
		t.Errorf("Expected false, but got true")
	}
}

func TestConcatArgs(t *testing.T) {
	k := ArrayOf([]int{5, 6}, "A")
	print("--- k", k)
	type args struct {
		args []interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test", args{[]interface{}{1, "A", []int{2, 3}, []string{"B", "C"}, 4}}, "1:A:2:3:B:C:4"},
		{"merge", args{ArrayOf([]interface{}{5, 6}, "A")}, "5:6:A"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ConcatArgs(tt.args.args); got != tt.want {
				t.Errorf("ConcatArgs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConcatData(t *testing.T) {
	tests := []struct {
		data      interface{}
		separator string
		expected  string
	}{
		{[]string{"apple", "banana", "cherry"}, ", ", "apple, banana, cherry"},
		{[]int{1, 2, 3}, ":", "1:2:3"},
		{[]interface{}{"foo", 42, true}, "|", "foo|42|true"},
		{[]float64{1.1, 2.2, 3.3}, "-", "1.1-2.2-3.3"},
		{[]int{}, ", ", ""},
		{nil, ", ", ""},
		{"not a slice", ", ", ""},
		{[]interface{}{1, "A", []int{2, 3}, []string{"B", "C"}, 4}, ":", "1:A:2:3:B:C:4"},
	}

	for _, test := range tests {
		result := ConcatData(test.data, test.separator)
		if result != test.expected {
			t.Errorf("ConcatData(%v, %q) = %q; expected %q", test.data, test.separator, result, test.expected)
		}
	}
}
