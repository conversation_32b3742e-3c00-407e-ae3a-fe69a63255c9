package generate

import (
	"fmt"
	"net/url"
)

func QrCode(text string, size int) string {
	// qrCode := utils.ParseQuery("https://chart.googleapis.com/chart", map[string]interface{}{
	// 	"chs": "300x300",
	// 	"chl": qrPayment,
	// 	"cht": "qr",
	// })

	// params := url.Values{}
	// 	params.Add("cht", "qr")
	// 	params.Add("chs", "250x250")
	// 	params.Add("chl", utils.ToString(code))

	// 	baseUrl, err := url.Parse("https://chart.googleapis.com")
	// 	log.IfError(err)
	// 	baseUrl.Path = "chart"
	// 	baseUrl.RawQuery = params.Encode()

	return fmt.Sprintf("https://quickchart.io/qr?text=%s&size=%d", url.QueryEscape(text), size)
}
