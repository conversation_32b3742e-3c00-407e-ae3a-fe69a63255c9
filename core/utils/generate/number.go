package generate

import (
	"math/rand"
	"time"
)

func RandomNumber(length int) int {
	if length <= 0 {
		return 0
	}

	// Initialize the random number generator with a unique seed (e.g., time)
	rand.Seed(time.Now().UnixNano())

	// Calculate the minimum and maximum values based on the length
	min := 1
	max := 1
	for i := 1; i < length; i++ {
		min *= 10
		max = max*10 + 9
	}

	// Generate and return a random number within the specified range
	return rand.Intn(max-min+1) + min
}
