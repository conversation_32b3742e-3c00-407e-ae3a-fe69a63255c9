package utils

import "fmt"

func AddPadding(memberDetailId string, maxLength int) string {
	result := memberDetailId
	for len(result) < maxLength {
		result += ToString(RandomNumber(0, 9))
	}
	return result
}

func ChangeNumber(secretId string) string {
	changer := map[string]string{
		"0": "3",
		"1": "4",
		"2": "9",
		"3": "8",
		"4": "1",
		"5": "7",
		"6": "0",
		"7": "2",
		"8": "6",
		"9": "5",
	}

	newId := ""
	for _, id := range secretId {
		if changer[fmt.Sprintf("%c", id)] != "" {
			newId += changer[fmt.Sprintf("%c", id)]
		} else {
			fmt.Println(fmt.Sprintf("%c", id), " not found")
		}
	}

	return newId
}
