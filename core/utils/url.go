package utils

import (
	"net/url"
)

func BuildUrl(baseUrl string, params map[string]interface{}) string {
	u, err := url.Parse(baseUrl)
	if err != nil {
		return ""
	}

	q := u.Query()
	for key, value := range params {
		valueStr := ToString(value)
		if value == nil || valueStr == "" {
			continue
		}
		q.Set(key, valueStr)
	}

	u.RawQuery = q.Encode()
	return u.String()
}

func ExtractBaseUrl(longUrl string) string {
	parsedUrl, err := url.Parse(longUrl)
	if err != nil {
		return ""
	}
	// return parsedUrl.Scheme + "://" + parsedUrl.Host
	return parsedUrl.Host
}
