package utils

import (
	"fmt"
	"testing"
)

func TestEncrypt(t *testing.T) {
	type args struct {
		text string
		key  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{"77474", KEY_PROMOTION_BARCODE}, "b3BzdG9tZXI="},
		{"member", args{"1", KEY_MEMBER_BARCODE}, "b3BzdG9tZXI="},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Encrypt(tt.args.text, tt.args.key); got == "" {
				t.Errorf("Encrypt() = %v, want %v", got, tt.want)
			} else {
				fmt.Printf("%v - QR (%v): '%v'\n", tt.name, tt.args.text, got)
			}
		})
	}
}

func TestDecrypt(t *testing.T) {
	type args struct {
		cryptoText string
		key        string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"member", args{cryptoText: "8IfSr56cktCb4QDcz0atGPy_1g4=", key: "buvaZZvR2xKN7rCeuJ4btZ7yLw72vFYH"}, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Decrypt(tt.args.cryptoText, tt.args.key); got != tt.want {
				t.Errorf("Decrypt() = %v, want %v", got, tt.want)
			}
		})
	}
}
