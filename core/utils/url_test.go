package utils

import "testing"

func TestExtractBaseUrl(t *testing.T) {
	type args struct {
		longUrl string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{"https://crm-page.uniq.id/link"}, "https://crm-page.uniq.id"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ExtractBaseUrl(tt.args.longUrl); got != tt.want {
				t.Errorf("ExtractBaseUrl() = %v, want %v", got, tt.want)
			}
		})
	}
}
