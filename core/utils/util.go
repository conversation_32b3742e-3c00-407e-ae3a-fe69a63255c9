package utils

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"log"
	rand2 "math/rand"
	net "net/url"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/goccy/go-json"

	"github.com/valyala/fasthttp"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/net/html"
)

func Cleanup(ctx *fasthttp.RequestCtx) {
	if r := recover(); r != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		fmt.Fprint(ctx, r)
		fmt.Println(date, " : Panic ", r)
	}
}

// return true if found error
func CheckError(ctx *fasthttp.RequestCtx, err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		fmt.Fprintln(ctx, "Server Error "+err.Error())
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s   %s:%d %v", date, fn, line, err)
		log.Printf("%s   %s:%d %v", date, fn, line, err)
		errMsg := fmt.Sprintf("%s   %s:%d %v", date, fn, line, err)

		sendErrorToSlack(errMsg)

		return true
	}
	return false
}

func CheckErr(err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		//fmt.Println(date, "   Error While ", onDoing, ". ", err)
		_, fn, line, _ := runtime.Caller(1)
		errMsg := fmt.Sprintf("%s  [error] %s:%d %v", date, fn, line, err)
		log.Printf("%s  [error] %s:%d %v", date, fn, line, err)

		if os.Getenv("server") != "localhost" {
			sendErrorToSlack(errMsg)
		}

		return true
	}

	return false
}

func SendMessageToSlack(message string) {
	request := HttpRequest{}
	request.Method = "POST"

	webhookToken := os.Getenv("SLACK_WEBHOOK_TOKEN")
	if webhookToken == "" {
		webhookToken = "TG2SFH18B/BGEAQN3FA/SO1h8Y2cnfBwtlgkdNWe3N6s"
	}

	request.Url = "https://hooks.slack.com/services/" + webhookToken

	env := os.Getenv("server")
	if tag := os.Getenv("SERVER_TAG"); tag != "" {
		env = fmt.Sprintf("%v-%v", env, strings.ToLower(tag))
	}

	var slackMsg SlackMessage
	var slackFields []SlackFields
	slackFields = append(slackFields, SlackFields{
		Title: "Project",
		Value: "API CRM",
		Short: true,
	})
	slackFields = append(slackFields, SlackFields{
		Title: "Environment",
		Value: env,
		Short: true,
	})
	slackMsg.Attachments = append(slackMsg.Attachments, SlackAttachments{
		Fallback: message,
		Text:     message,
		Color:    "#F35A00",
		Fields:   slackFields,
	})
	request.PostRequest.Body = slackMsg

	_, err := request.ExecuteRequest()
	if err != nil {
		fmt.Println("Sending to slack error ", err)
	}
}

func sendErrorToSlack(message string) {
	request := HttpRequest{}
	request.Method = "POST"
	request.Url = "*****************************************************************************"
	request.PostRequest.Body = map[string]interface{}{
		"text": fmt.Sprintf("[api membership | %s] %s", os.Getenv("server"), message),
	}
	_, err := request.ExecuteRequest()
	if err != nil {
		fmt.Println("Sending to slack error ", err)
	}
}

func MillisToDateTime(timeMillis int64) (string, error) {
	t := time.Unix(timeMillis, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}

func ByteToInt(data []byte) int {
	intValue, err := strconv.Atoi(string(data))
	if err != nil {
		return 0
	}
	return intValue
}

func StringToInt(data string) int {
	res, err := strconv.Atoi(data)
	if err != nil {
		return 0
	} else {
		return res
	}
}

func ReplaceEmoji(data string) string {
	var emojiRx = regexp.MustCompile(`[\x{1F600}-\x{1F6FF}|[\x{2600}-\x{26FF}]`)
	return emojiRx.ReplaceAllString(data, ``)
}

func CurrencyFormat(amount int) string {
	result := ""
	amountStr := strconv.Itoa(amount)
	index := 1
	for i := len(amountStr); i > 0; i-- {
		data := amountStr[i-1 : i]
		if index%3 == 0 {
			result += data + "."
		} else {
			result += data
		}
		index++
	}
	result = Reverse(result)
	if strings.HasPrefix(result, ".") {
		result = result[1:]
	} else if strings.HasPrefix(result, "-.") {
		result = "-" + result[2:]
	}

	return result
}

func Reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func ExeCommand(name string, args ...string) (string, error) {
	cmd := exec.Command(name, args...)

	cmdOutput := &bytes.Buffer{}
	// Attach buffer to command
	cmd.Stdout = cmdOutput

	err := cmd.Run()
	if err != nil {
		fmt.Printf("==> Error When Executing : %s\n", strings.Join(cmd.Args, " "))
		fmt.Println("==> Error Message : ", err.Error())
		return "", err
	}

	outputByte := cmdOutput.Bytes()
	if len(outputByte) > 0 {
		return string(outputByte), nil
	} else {
		return "", nil
	}
}

func ReadFile(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()
	result, err := ioutil.ReadAll(file)
	return string(result), nil
}

const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func RandStringBytes(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand2.Intn(len(letterBytes))]
	}
	return string(b)
}

func RandomNumber(min, max int) int {
	rand2.Seed(time.Now().UnixNano())
	return rand2.Intn(max-min) + min
}

func RemoveArray(slice []interface{}, item interface{}) []interface{} {
	for i, d := range slice {
		if d == item {
			copy(slice[i:], slice[i+1:])
			return slice[:len(slice)-1]
		}
	}
	return slice
}

func RemvoveEmptyFields(obj map[string]interface{}) map[string]interface{} {
	for k, v := range obj {
		if v == nil || (reflect.TypeOf(v).Kind() == reflect.String && strings.TrimSpace(ToString(v)) == "") {
			delete(obj, k)
		}
	}
	return obj
}

func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	return string(bytes), err
}

func HashPasswordWithCost(password string, cost int) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	return string(bytes), err
}

func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func ToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case []uint8:
		return string(v)
	case string:
		return v
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d - Invalid recognize data type toString %v \n", GetFileName(fn, true), line, reflect.TypeOf(data))
		return ""
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if CheckErr(err) {
		return 0
	} else {
		return result
	}
}

func ToInt64(data interface{}) int64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseInt(dataStr, 10, 64)
	if CheckErr(err) {
		return 0
	} else {
		return result
	}
}

func ToFloat(data interface{}) float64 {
	dataStr := ToString(data)
	result, err := strconv.ParseFloat(dataStr, 64)
	if err != nil {
		//fmt.Println("Converting string to float error", err, "Data : ", data)
		return 0
	}
	return result
}

func GetFileName(path string, withoutExtension bool) string {
	pathArray := strings.Split(path, "/")
	lastPath := path
	if len(pathArray) > 1 {
		lastPath = pathArray[len(pathArray)-1]
	}

	if withoutExtension {
		lastPath = strings.TrimSuffix(lastPath, filepath.Ext(lastPath))
	}

	return lastPath
}

func RemoveFieldArray(dataArr []map[string]interface{}, keys ...string) {
	for _, dataMap := range dataArr {
		for _, key := range keys {
			if dataMap[key] != nil {
				delete(dataMap, key)
			}
		}
	}
}

func RemoveField(dataMap map[string]interface{}, keys ...string) {
	for _, key := range keys {
		if _, ok := dataMap[key]; ok {
			delete(dataMap, key)
		}
	}
}

func Date(timeMillis int64) (string, error) {
	t := time.Unix(timeMillis, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}

func SimplyToJson(data interface{}) string {
	resp, err := json.Marshal(data)
	if err == nil {
		return string(resp)
	} else {
		return "<INVALID>"
	}
}

//func CurrencyFormat(amount int) string {
//	result := ""
//	amountStr := strconv.Itoa(amount)
//	index := 1
//	for i := len(amountStr); i > 0; i-- {
//		data := amountStr[i-1 : i]
//		if index%3 == 0 {
//			result += data + "."
//		} else {
//			result += data
//		}
//		index++
//	}
//	result = Reverse(result)
//	if strings.HasPrefix(result, ".") {
//		result = result[1:]
//	} else if strings.HasPrefix(result, "-.") {
//		result = "-" + result[2:]
//	}
//
//	return result
//}

func IsNumber(data interface{}) bool {
	if data == nil {
		return false
	}
	_, err := strconv.ParseInt(ToString(data), 10, 64)
	return err == nil
}

func ToBase64(filePath string) (string, error) {
	f, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	// Read entire JPG into byte slice.
	reader := bufio.NewReader(f)
	content, err := ioutil.ReadAll(reader)
	if err != nil {
		return "", err
	}

	// Encode as base64.
	return base64.StdEncoding.EncodeToString(content), nil
}

func DeleteFile(path string) {
	// delete file
	var err = os.Remove(path)
	CheckErr(err)

	fmt.Println("==> done deleting file - ", path)
}

func ArrayOf(data ...interface{}) []interface{} {
	result := make([]interface{}, 0)
	for _, row := range data {
		if reflect.TypeOf(row).Kind() == reflect.Slice {
			result = append(result, ToInterfaceArray(row)...)
		} else {
			result = append(result, row)
		}
	}

	return result
}

func ToInterfaceArray(data interface{}) []interface{} {
	if reflect.TypeOf(data).Kind() != reflect.Slice {
		return []interface{}{data}
	}

	result := make([]interface{}, 0)
	switch v := data.(type) {
	case []string:
		for _, row := range v {
			result = append(result, row)
		}
	case []int:
		for _, row := range v {
			result = append(result, row)
		}
	case []int32:
		for _, row := range v {
			result = append(result, row)
		}
	case []int64:
		for _, row := range v {
			result = append(result, row)
		}
	case []float32:
		for _, row := range v {
			result = append(result, row)
		}
	case []float64:
		for _, row := range v {
			result = append(result, row)
		}
	default:
		fmt.Println("unknown slice of", reflect.TypeOf(data))
	}

	return result
}

func TakesOnly(data map[string]interface{}, keys ...string) map[string]interface{} {
	result := make(map[string]interface{})
	for _, key := range keys {
		result[key] = data[key]
	}
	return result
}

func MergeMaps(original map[string]interface{}, extras ...map[string]interface{}) map[string]interface{} {
	for _, extra := range extras {
		for k, v := range extra {
			original[k] = v
		}
	}
	return original
}

func MapOf(data ...interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for i := 0; i < len(data); i += 2 {
		if len(data) > i+1 {
			result[ToString(data[i])] = data[i+1]
		} else {
			fmt.Println(data[i], "is excluded from map")
		}
	}
	return result
}

func MapToArray(data map[string]map[string]interface{}) []interface{} {
	result := make([]interface{}, 0)
	for _, v := range data {
		result = append(result, v)
	}
	return result
}

func ParseQuery(url string, queries map[string]interface{}) string {
	u, err := net.Parse(url)
	if err != nil {
		fmt.Printf("parsing url '%s' error : %v\n", url, err)
	}

	q := u.Query()
	for key, v := range queries {
		q.Set(key, ToString(v)) //net.QueryEscape(ToString(v))
	}
	u.RawQuery = q.Encode()
	return u.String()
}

func FindBaseDomain(url string) string {
	re := regexp.MustCompile(`^https://(?:[a-z\-)]*\.)?([a-z]+.[a-z]+)/*`)
	result := re.FindStringSubmatch(url)
	if len(result) > 0 {
		return result[1]
	}
	return ""
}

func GetErrCodeSQL(msg string) int {
	code := "0"
	p := regexp.MustCompile("^Error ([0-9]{4}): ")
	result := p.FindAllStringSubmatch(msg, 1)
	if len(result) > 0 {
		code = result[0][1]
	}
	return ToInt(code)
}

func CopyMap(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for key, v := range data {
		result[key] = v
	}
	return result
}

func FormatPhoneNumber(phone interface{}) string {
	if phone == nil {
		return ""
	}

	phoneStr := ToString(phone)
	if phoneStr == "" {
		return ""
	}
	phoneStr = strings.TrimLeft(phoneStr, "+")
	if strings.HasPrefix(phoneStr, "08") {
		phoneStr = "62" + strings.TrimLeft(phoneStr, "0")
	} else if strings.HasPrefix(phoneStr, "8") {
		phoneStr = "62" + phoneStr
	} else if strings.HasPrefix(phoneStr, "6208") {
		phoneStr = "62" + phoneStr[3:]
	}
	return phoneStr
}

func TakeIf(condition bool, valueTrue, valueFalse interface{}) interface{} {
	if condition {
		return valueTrue
	}
	return valueFalse
}

// This method uses a regular expresion to remove HTML tags.
func StripHtmlRegex(s string) string {
	p := `(<(/|)(p|b|i|span)>|&nbsp;)`
	r := regexp.MustCompile(p)
	s = r.ReplaceAllString(s, "")

	p = `<(/br)[^>]*>`
	s = regexp.MustCompile(p).ReplaceAllString(s, "\n")
	return strings.TrimSpace(s)
}

func ExtractHtml(htmlContent string) string {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		return ""
	}

	var text strings.Builder
	var f func(*html.Node)
	f = func(n *html.Node) {
		if n.Type == html.TextNode {
			fmt.Println(n.Data)
			text.WriteString(n.Data)
			text.WriteString("\n")
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			f(c)
		}
	}

	f(doc)
	return text.String()
}

func ConcatArgs(args []interface{}) string {
	var sb strings.Builder
	for i, arg := range args {
		// Handle different types of values
		switch v := arg.(type) {
		case string:
			sb.WriteString(v)
		case int:
			sb.WriteString(fmt.Sprintf("%d", v))
		case []interface{}:
			// Concatenate the slice recursively
			sb.WriteString(ConcatArgs(v))
		case []int:
			// Concatenate the slice of ints
			for j, num := range v {
				sb.WriteString(fmt.Sprintf("%d", num))
				if j < len(v)-1 {
					sb.WriteString(":")
				}
			}
		case reflect.Value:
			// Handle slice of any type using reflection
			if v.Kind() == reflect.Slice {
				for j := 0; j < v.Len(); j++ {
					sb.WriteString(fmt.Sprintf("%v", v.Index(j).Interface()))
					if j < v.Len()-1 {
						sb.WriteString(":")
					}
				}
			} else {
				// Handle other types of reflect.Value
				sb.WriteString(fmt.Sprintf("%v", v.Interface()))
			}
		case []string:
			// Concatenate the slice of strings
			for j, str := range v {
				sb.WriteString(str)
				if j < len(v)-1 {
					sb.WriteString(":")
				}
			}
		default:
			// Handle other types or use the default string representation
			sb.WriteString(fmt.Sprintf("%v", v))
		}

		// Append the separator if not the last element
		if i < len(args)-1 {
			sb.WriteString(":")
		}
	}
	return sb.String()
}

func ConcatData(data interface{}, separator string) string {
	// Use reflection to handle different data types
	val := reflect.ValueOf(data)

	// Check if it's a slice or array
	if val.Kind() != reflect.Slice && val.Kind() != reflect.Array {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v ConcatData failed, its not slice.. but %v \n", fn, line, val.Kind())
		return "" // Or handle the error as needed
	}

	var strParts []string
	for i := 0; i < val.Len(); i++ {
		// Convert each element to a string
		strParts = append(strParts, fmt.Sprintf("%v", val.Index(i)))
	}

	fmt.Println(">>>> ", strings.Join(strParts, separator))
	return strings.Join(strParts, separator)
}
