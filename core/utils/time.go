package utils

import "time"

func GetCurrentMillis() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// formatTimeMillis formats a given time in milliseconds to "DD/MM/YYYY" format
// with the specified timezone offset.
func FormatTimeMillis(timeMillis int64, timezoneOffset int) string {
	// Convert milliseconds to seconds
	seconds := timeMillis / 1000

	// Create a time object with the given seconds and timezone offset
	loc := time.FixedZone("UTC+7", timezoneOffset) // Example: UTC+7
	t := time.Unix(seconds, 0).In(loc)

	// Format the time to "DD/MM/YYYY"
	return t.Format("02/01/2006")
}