package utils

/*func SendEmail(toAddress, content string) error {
	url := "https://mail.zoho.com/api/accounts/5959564000000008002/messages"
	fmt.Println("URL:>", url)

	jsonMap := map[string]string{
		"fromAddress" : "<EMAIL>",
		"toAddress" : toAddress,
		"subject" : "Email Confirmation",
		"content": content,
	}

	jsonStr, err := json.Marshal(jsonMap)
	if err != nil {
		fmt.Println("Parse map to json error : ", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Authorization", "6269fea601cf0fe94cde7b67fd849499")
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Request to zoho failed - ", err)
		return err
	}
	defer resp.Body.Close()

	//fmt.Println("response Headers:", resp.Header)
	fmt.Println("Send Email to "+ toAddress +" response Status:", resp.Status)
	body, _ := ioutil.ReadAll(resp.Body)
	if resp.StatusCode >= 300 {
		fmt.Println("response Body:", string(body))
		return errors.New(fmt.Sprintf("status code %d", resp.StatusCode))
	} else {
		return nil
	}
}*/
