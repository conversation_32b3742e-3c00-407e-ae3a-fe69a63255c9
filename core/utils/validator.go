package utils

import (
	"context"
	"errors"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
)

func FileMustExist(paths ...string) {
	for _, path := range paths {
		if _, err := os.Stat(path); err != nil {
			panic(fmt.Sprintf("file '%s' is not exist", path))
		}
	}
}

func IsValidPostInput(ctx *fasthttp.RequestCtx, params ...string) (bool, string) {
	status := true
	message := ""
	for _, param := range params {
		if ctx.PostArgs().Peek(param) == nil {
			status = false
			message = fmt.Sprintf("%s is required", param)
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			break
		}
	}
	return status, message
}

func IsValidIdToken(idToken string) (string, string, error) {
	timeStart := time.Now()
	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		return "", "", err
	}

	token, err := client.VerifyIDToken(ctxBack, idToken)
	if err != nil {
		return "", "", err
	}

	fmt.Println("token claim: ", token.Claims)
	if token.Claims["phone_number"] == nil && token.Claims["email"] == nil {
		return "", "", errors.New("server can not get your phone or email")
	}

	phone := ToString(token.Claims["phone_number"])
	phone = strings.TrimPrefix(phone, "+")

	email := ToString(token.Claims["email"])
	fmt.Printf("validate id token took : %v  | email: %v | phone: %v \n", time.Since(timeStart), email, phone)
	return phone, email, nil
}

func IsValidEmailAddress(email string) bool {
	pattern := "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$"
	r, _ := regexp.Compile(pattern)
	return r.MatchString(email)
}

func MustHaveFile(path string) string {
	info, err := os.Stat(path)
	if (os.IsNotExist(err) || info.IsDir()) && os.Getenv("server") != "localhost" {
		panic(fmt.Sprintf("this file is not exist or directory : %s", path))
	}
	return path
}
