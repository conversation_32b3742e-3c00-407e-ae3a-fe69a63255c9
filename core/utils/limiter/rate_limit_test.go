package limiter

import (
	"fmt"
	"testing"
	"time"
)

func TestRateLimiter_Allow(t *testing.T) {
	type args struct {
		id string
	}
	tests := []struct {
		name   string
		fields *RateLimiter
		args   args
		want   bool
	}{
		{"exeed", NewRateLimiter(5, 2), args{id: "123"}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// if got := rl.Allow(tt.args.id); got != tt.want {
			// 	t.Errorf("RateLimiter.Allow() = %v, want %v", got, tt.want)
			// }
			fmt.Println("time window: ", tt.fields.GetTimeWindow())
			for i := 0; i < 10; i++ {
				fmt.Println("allow : ", tt.fields.Allow(tt.args.id))
				time.Sleep(1 * time.Second)
			}
		})
	}
}
