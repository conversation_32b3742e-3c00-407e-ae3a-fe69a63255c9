package limiter

import (
	"sync"
	"time"
)

type RateLimiter struct {
	mu           sync.Mutex
	requestTimes map[string][]time.Time
	timeWindow   time.Duration
	requestLimit int
}

// timeWindow: in second, requestLimit: total max limit within that time window
func NewRateLimiter(timeWindow, requestLimit int) *RateLimiter {
	return &RateLimiter{
		requestTimes: make(map[string][]time.Time),
		timeWindow:   time.Duration(timeWindow) * time.Second,
		requestLimit: requestLimit,
	}
}

// get time window formatted, e.g 3 minutes, 1 hour
func (rl *RateLimiter) GetTimeWindow() string {
	return rl.timeWindow.String()
}

func (rl *RateLimiter) Allow(id string) bool {
	// Lock to ensure concurrent access safety.
	rl.mu.Lock()
	defer rl.mu.Unlock()

	// this is for temporary, resetting the map to allocate more memory
	if len(rl.requestTimes) > 200 {
		rl.requestTimes = make(map[string][]time.Time)
	}

	// Remove request times that are older than x minutes.
	now := time.Now()
	cutoffTime := now.Add(-rl.timeWindow)
	newRequestTimes := []time.Time{}

	// Retrieve the request times for the id.
	existingRequestTimes := rl.requestTimes[id]

	for _, t := range existingRequestTimes {
		if t.After(cutoffTime) {
			newRequestTimes = append(newRequestTimes, t)
		}
	}

	// If the id has made fewer than 5 requests in the last 15 minutes, allow the request.
	if len(newRequestTimes) < rl.requestLimit {
		newRequestTimes = append(newRequestTimes, now)
		rl.requestTimes[id] = newRequestTimes
		return true
	}

	return false
}
