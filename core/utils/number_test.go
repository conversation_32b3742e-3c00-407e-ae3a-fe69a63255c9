package utils

import "testing"

func TestRoundUpPayment(t *testing.T) {
	type args struct {
		amount float64
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{"5090", args{5090.0}, 5100},
		{"5140", args{5140.0}, 5200},
		{"23500", args{23500.0}, 23500},
		{"23509", args{23509.0}, 23600},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RoundUpPayment(tt.args.amount); got != tt.want {
				t.Errorf("RoundUpPayment() = %v, want %v", got, tt.want)
			}
		})
	}
}
