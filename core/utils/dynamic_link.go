package utils

import (
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"os"
)

func CreateDynamicLink(link, androidId string) (string, error) {
	body := map[string]interface{}{
		"dynamicLinkInfo": map[string]interface{}{
			"domainUriPrefix": "https://crm-page.uniq.id", //"https://crm-app.uniq.id",//https://crmuniq.page.link
			"link":            link,
			"androidInfo": map[string]interface{}{
				"androidPackageName": androidId,
			},
		},
	}

	req := HttpRequest{
		Method: "POST",
		Url:    "https://firebasedynamiclinks.googleapis.com/v1/shortLinks",
		PostRequest: PostRequest{
			Body: body,
		},
		MultipartRequest: MultipartRequest{},
		Params: map[string]interface{}{
			"key": os.Getenv("firebase_web_api_key"),
		},
	}

	resp, err := req.ExecuteRequest()
	if err != nil {
		return "", err
	}

	var deepLinkResp models.DeepLinkResponse
	err = json.Unmarshal(resp, &deepLinkResp)
	if err != nil {
		return "", err
	}

	if deepLinkResp.Error.Code > 0 {
		fmt.Println("deep link resp error : ", deepLinkResp.Error)
		return "", errors.New(deepLinkResp.Error.Message)
	}

	return deepLinkResp.ShortLink, nil
}

func GetAndroidPackageName(adminId int) string {
	dataJson, err := ReadFile("config/data/mobile_ids.json")
	if err != nil {
		fmt.Println("read json error : ", err)
	}

	var data []models.MobileConfig
	err = json.Unmarshal([]byte(dataJson), &data)
	if err != nil {
		fmt.Println("parse json error : ", err)
	}

	for _, conf := range data {
		if conf.Environment == os.Getenv("server") && adminId == conf.AdminID {
			return conf.AndroidPackageID
		}
	}

	fmt.Println("[[MOBILE ID NOT FOUND]] - adminId : ", adminId)
	return ""
}
