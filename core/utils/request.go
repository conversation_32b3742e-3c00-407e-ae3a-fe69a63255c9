package utils

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	net "net/url"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"
)

type HttpRequest struct {
	Method           string
	Url              string
	Header           map[string]interface{}
	PostRequest      PostRequest
	MultipartRequest MultipartRequest
	Params           map[string]interface{}
}

type PostRequest struct {
	Body interface{}
	Form map[string]string
}

type MultipartRequest struct {
	FilePath  string
	FileParam string
	Form      map[string]string
}

type RequestResponse struct {
	Body       string
	StatusCode int
}

func (req *HttpRequest) SetBasicAuth(username, password string) {
	if req.Header == nil {
		req.Header = make(map[string]interface{})
	}

	auth := fmt.Sprintf("%s:%s", username, password)
	req.Header["Authorization"] = fmt.Sprintf("Basic %s", base64.StdEncoding.EncodeToString([]byte(auth)))
	fmt.Println(">>", req.Header)
}

func (req HttpRequest) ExecuteRequest() ([]byte, error) {
	result, err := request(req)
	return []byte(result.Body), err
}

func (req HttpRequest) Execute() (RequestResponse, error) {
	return request(req)
}

func request(request HttpRequest) (RequestResponse, error) {
	fmt.Printf("[REQ] | %s |  %s\n", request.Method, request.Url)
	timeStart := time.Now()

	var reqResponse RequestResponse
	var body string
	if len(request.PostRequest.Form) > 0 {
		data := net.Values{}
		for key, value := range request.PostRequest.Form {
			data.Set(key, value)
		}
		if request.Header == nil {
			request.Header = make(map[string]interface{})
		}
		if request.Header["Content-Type"] == nil {
			request.Header["Content-Type"] = "application/x-www-form-urlencoded"
		}
		body = data.Encode()
		fmt.Printf("[REQ] Form: %s\n", SimplyToJson(request.PostRequest.Form))
	} else if request.PostRequest.Body != nil {
		dataJson, err := json.Marshal(request.PostRequest.Body)
		if err != nil {
			fmt.Println("Parse map to json error : ", err)
		}

		body = string(dataJson)

		if request.Header == nil {
			request.Header = make(map[string]interface{})
		}
		if body != "" && request.Header["Content-Type"] == nil {
			request.Header["Content-Type"] = "application/json"
		}

		fmt.Printf("[REQ] Body: %s\n", strings.Replace(SimplyToJson(request.PostRequest.Body), "\n", " ", -1))
	}

	fmt.Printf("[REQ] Header: %s\n", strings.Replace(SimplyToJson(request.Header), "\n", " ", -1))

	if len(request.Params) > 0 {
		u, err := net.Parse(request.Url)
		if err != nil {
			fmt.Printf("parsing url '%s' error : %v\n", request.Url, err)
		}

		q := u.Query()
		for key, v := range request.Params {
			q.Set(key, ToString(v))
		}
		u.RawQuery = q.Encode()
		request.Url = u.String()
		fmt.Println("[REQ]", request.Url)
	}

	var req *http.Request
	var err error

	if request.MultipartRequest.FilePath != "" {
		req, err = createMultipartRequest(request)
	} else {
		req, err = http.NewRequest(request.Method, request.Url, strings.NewReader(body))
	}

	if err != nil {
		fmt.Println("Creating http request error ", err)
		return reqResponse, err
	}

	for key, value := range request.Header {
		req.Header.Set(key, ToString(value))
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Request to '%s' error %v \n", request.Url, err)
		return reqResponse, err
	}
	defer resp.Body.Close()

	fmt.Printf("[REQ] | %d |  %v\n", resp.StatusCode, time.Since(timeStart))
	bodyResp, err := ioutil.ReadAll(resp.Body)
	reqResponse.StatusCode = resp.StatusCode
	reqResponse.Body = string(bodyResp)
	return reqResponse, err
}

func createMultipartRequest(request HttpRequest) (*http.Request, error) {
	file, err := os.Open(request.MultipartRequest.FilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile(request.MultipartRequest.FileParam, filepath.Base(request.MultipartRequest.FilePath))
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(part, file)

	for key, val := range request.MultipartRequest.Form {
		_ = writer.WriteField(key, val)
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", request.Url, body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	return req, err
}

func (req HttpRequest) DownloadFile(savePath string) error {
	dir, _ := path.Split(savePath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		err = os.Mkdir(dir, os.ModePerm)
		if err != nil {
			fmt.Println("create directory err - ", err)
		}
	}

	img, err := os.Create(savePath)
	if err != nil {
		fmt.Printf("create byte file error : %v \n", err)
		return err
	}

	defer img.Close()

	resp, err := http.Get(req.Url)
	if err != nil {
		fmt.Printf("get file to download error : %v \n", err)
		return err
	}
	defer resp.Body.Close()

	b, err := io.Copy(img, resp.Body)
	fmt.Println("File size: ", b)

	return err
}
