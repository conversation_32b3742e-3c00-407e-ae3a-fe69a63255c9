package utils

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
)

const SHORT_URL_UNIQ string = "https://y.uniq.id/"

type ShortUrlModel struct {
	LongUrl   string //the original url (long version)
	Title     string
	Tags      []string
	Length    int //the lengh of sort url (exclude main domain)
	ExpiredAt int64
}

type ShortUrlResponse struct {
	Status   bool   `json:"status"`
	Message  string `json:"message"`
	ShortURL string `json:"short_url"`
}

type BitLy struct {
	CreatedAt      string        `json:"created_at"`
	ID             string        `json:"id"`
	Link           string        `json:"link"`
	CustomBitlinks []interface{} `json:"custom_bitlinks"`
	LongURL        string        `json:"long_url"`
	Archived       bool          `json:"archived"`
	Tags           []interface{} `json:"tags"`
	Deeplinks      []interface{} `json:"deeplinks"`
	References     struct {
		Group string `json:"group"`
	} `json:"references"`
}

func ShortUrl(model ShortUrlModel) string {
	result := ShortUniq(model)
	if result == "" {
		result = ShortBitLy(model)
	}
	return result
}

func ShortUniq(model ShortUrlModel) string {
	request := HttpRequest{}
	request.Method = "POST"
	request.Url = SHORT_URL_UNIQ + "create"
	request.Header = map[string]interface{}{
		"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzaG9yLXVybCIsIm5hbWUiOiJVTklRLURFViIsImlhdCI6MTU4MTY1MDU1MDY0Mn0.OKztNL736gbCdue72GBr4tzXLuhns4dBpjlUfuK_Jbc",
		"Content-Type":  "application/x-www-form-urlencoded",
	}
	formData := map[string]string{
		"url": model.LongUrl,
		"tag": strings.Join(model.Tags, ","),
	}
	if model.Length > 0 {
		formData["length"] = cast.ToString(model.Length)
	}
	if model.ExpiredAt > 0 {
		formData["expired_at"] = cast.ToString(model.ExpiredAt)
	}
	request.PostRequest.Form = formData

	var short ShortUrlResponse
	resp, err := request.Execute()
	if err == nil {
		fmt.Println("short with uniq resp : ", string(resp.Body))
		err = json.Unmarshal([]byte(resp.Body), &short)
		if err != nil {
			fmt.Println("parsing json at short url with uniq error - ", err)
		}
	} else {
		fmt.Println("short url with uniq error - ", err)
	}
	if short.ShortURL != "" {
		return SHORT_URL_UNIQ + short.ShortURL
	}

	return short.ShortURL
}

func ShortBitLy(model ShortUrlModel) string {
	request := HttpRequest{}
	request.Method = "POST"
	request.Url = "https://api-ssl.bitly.com/v4/bitlinks"
	request.Header = map[string]interface{}{
		"Authorization": "Bearer c9c7e0987e65803245b34c25a71e1acb06c86a18",
		"Content-Type":  "application/json",
	}
	request.PostRequest.Body = map[string]interface{}{
		"title":    model.Title,
		"long_url": model.LongUrl,
		"tags":     model.Tags,
	}
	var bitly BitLy
	resp, err := request.Execute()
	if err == nil {
		err = json.Unmarshal([]byte(resp.Body), &bitly)
		if err != nil {
			fmt.Println("parsing json at short url with bitly error - ", err)
		}
	} else {
		fmt.Println("short url with bitly error - ", err)
	}

	return bitly.Link
}
