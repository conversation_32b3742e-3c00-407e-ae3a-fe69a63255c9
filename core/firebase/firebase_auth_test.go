package firebase

import (
	"reflect"
	"testing"
)

func TestVerifyIdToken(t *testing.T) {
	idToken := "eyJhbGciOiJSUzI1NiIsImtpZCI6IjU4ODI0YTI2ZjFlY2Q1NjEyN2U4OWY1YzkwYTg4MDYxMTJhYmU5OWMiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g7USqm5w_FjEIhxK_ACmZuHVmaNQ_vQkEMvl3MAA40-FhX8VQ_9wZHG7NdRihvr8lGAAkg39HxYZpUVNbI-329M0t5NvinMnvIk8XvAGnbd898X5-OPPLT9SfFa5fVYM_E6vA-LcworGOiofztHgW7kbmF0qF7weyAJiFrnh3bw117S-bSA4wg6ysHMHYZJryrs2wdFWagXdxQyO4VLYcrVjLo7cu80WnEzbZ69T3BES0DtR51ina20QO3Dr6elxx9N7Bi_ukvCh-HnxhYjP3m63mCidsum4d12winJtBMweaRyMMk6PtmsI1Zh4W8_q7vDri3Tlmrr6PiYe9qI1HA"
	idTokenResult := IdTokenResult{
		Email:    "<EMAIL>",
		Provider: "password",
	}

	SetupFirebaseConfig("/Users/<USER>/Documents/WORK/api-crm/config/auth/uniq-crm-firebase-adminsdk.json")

	type args struct {
		idToken string
	}

	tests := []struct {
		name    string
		args    args
		want    IdTokenResult
		wantErr bool
	}{
		{"test1", args{idToken: idToken}, idTokenResult, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := VerifyIdToken(tt.args.idToken)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyIdToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VerifyIdToken() = %v, want %v", got, tt.want)
			}
		})
	}
}
