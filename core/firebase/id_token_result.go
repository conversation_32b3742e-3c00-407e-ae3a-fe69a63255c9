package firebase

type IdTokenClaim struct {
	Name          string   `json:"name,omitempty"`
	Picture       string   `json:"picture,omitempty"`
	Iss           string   `json:"iss,omitempty"`
	Aud           string   `json:"aud,omitempty"`
	AuthTime      int      `json:"auth_time,omitempty"`
	UserID        string   `json:"user_id,omitempty"`
	Sub           string   `json:"sub,omitempty"`
	Iat           int      `json:"iat,omitempty"`
	Exp           int      `json:"exp,omitempty"`
	Email         string   `json:"email,omitempty"`
	Phone         string   `json:"phone,omitempty"`
	EmailVerified bool     `json:"email_verified,omitempty"`
	Firebase      Firebase `json:"firebase,omitempty"`
}

type Identities struct {
	GoogleCom []string `json:"google.com,omitempty"`
	Email     []string `json:"email,omitempty"`
}

type Firebase struct {
	Identities     Identities `json:"identities,omitempty"`
	SignInProvider string     `json:"sign_in_provider,omitempty"`
}
