package firebase

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	firebase "firebase.google.com/go"
	"google.golang.org/api/option"
)

var app *firebase.App

var config = new()

func new() firebaseConfig {
	configPath := "config/auth/uniq-crm-firebase-adminsdk.json"
	if credPath := os.Getenv("FIREBASE_CREDENTIAL_PATH"); credPath != "" {
		configPath = credPath
	}

	return firebaseConfig{
		CredentialPath: configPath,
	}
}

type firebaseConfig struct {
	CredentialPath string
}

func SetupFirebaseConfig(credentialPath string) {
	config = firebaseConfig{
		CredentialPath: credentialPath,
	}
}

func initFirebase() {
	fmt.Println("init firebase sdk...")
	var err error
	//uniq-crm-firebase-adminsdk.json
	//chat-support-102fc-firebase-adminsdk.json
	firebaseCredPath := config.CredentialPath
	opt := option.WithCredentialsFile(firebaseCredPath)

	projectId := "uniq-crm"
	credJson, err := os.ReadFile(firebaseCredPath)
	if err == nil {
		var credential struct {
			ProjectId string `json:"project_id"`
		}
		fmt.Println("cred json: ", string(credJson))
		err = json.Unmarshal(credJson, &credential)
		if err != nil {
			fmt.Println("unmarshal firebase credential err: ", err)
		}
		projectId = credential.ProjectId
	} else {
		fmt.Println("read firebase credential err: ", err)
	}

	fmt.Println("firebase project id: ", projectId)
	config := &firebase.Config{
		ProjectID:   projectId,
		DatabaseURL: fmt.Sprintf("https://%s.firebaseio.com/", projectId),
	}

	app, err = firebase.NewApp(context.Background(), config, opt)
	if err != nil && os.Getenv("ENV") != "localhost" {
		panic(fmt.Errorf("error initializing app: %v", err))
	}
}

func GetFirebaseApp() *firebase.App {
	if app == nil {
		initFirebase()
	}
	return app
}
