package firebase

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
)

const (
	SignInProviderPassword = "password"
	SignInProviderGoogle   = "google.com"
)

var verifiedIdTokenCache map[string]IdTokenResult

type IdTokenResult struct {
	Phone    string
	Email    string
	Provider string
}

func VerifyIdToken(idToken string) (IdTokenResult, error) {
	//reset
	if len(verifiedIdTokenCache) > 50 || verifiedIdTokenCache == nil {
		fmt.Println("reset idToken cache")
		verifiedIdTokenCache = make(map[string]IdTokenResult)
	}

	if data, ok := verifiedIdTokenCache[idToken]; ok {
		fmt.Println("idToken from cache...")
		return data, nil
	}

	if strings.TrimSpace(idToken) == "" {
		return IdTokenResult{}, fmt.Errorf("idToken is empty")
	}

	ctxBack := context.Background()
	client, err := GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		return IdTokenResult{}, err
	}

	token, err := client.VerifyIDToken(ctxBack, idToken)
	if err != nil {
		fmt.Println("can not verify this token: ", idToken)
		return IdTokenResult{}, err
	}

	fmt.Println("token claim: ", token.Claims)
	if token.Claims["phone_number"] == nil && token.Claims["email"] == nil {
		return IdTokenResult{}, errors.New("server can not get your phone or email")
	}

	var result IdTokenResult

	if phoneNumber, ok := token.Claims["phone_number"]; ok {
		result.Phone = phoneNumber.(string)
		result.Phone = strings.TrimPrefix(result.Phone, "+")
	}

	if email, ok := token.Claims["email"]; ok {
		result.Email = email.(string)
	}

	if firebase, ok := token.Claims["firebase"]; ok {
		v := reflect.ValueOf(firebase)
		if v.Kind() == reflect.Map {
			for _, key := range v.MapKeys() {
				strct := v.MapIndex(key)
				fmt.Println(key.Interface(), strct.Interface())
				if key.String() == "sign_in_provider" {
					result.Provider = strct.Interface().(string)
				}
			}
		}
	}

	verifiedIdTokenCache[idToken] = result
	return result, nil
}
