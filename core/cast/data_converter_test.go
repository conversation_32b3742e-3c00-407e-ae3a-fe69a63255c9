package cast

import (
	"encoding/json"
	"fmt"
	"testing"
)

func TestMapArrayToStruct(t *testing.T) {
	dataMap := []map[string]interface{}{
		{"id": 1, "is_available": "true"},
	}

	type Products struct {
		Id          int  `json:"id,omitempty"`
		IsAvailable bool `json:"is_available,omitempty"`
	}

	var productList []Products

	type args struct {
		maps     []map[string]interface{}
		variable interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"data1", args{maps: dataMap, variable: &productList}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := MapArrayToStruct(tt.args.maps, tt.args.variable); (err != nil) != tt.wantErr {
				t.Errorf("MapArrayToStruct() error = %v, wantErr %v", err, tt.wantErr)
			}
			resp, _ := json.Marshal(tt.args.variable)
			fmt.Println("data: ", string(resp))
		})
	}
}
