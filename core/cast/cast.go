package cast

import (
	"fmt"
	"reflect"
	"runtime"
	"strconv"

	"github.com/goccy/go-json"
)

func ToInt(v interface{}) int {
	if v == nil {
		return 0
	}
	// Get caller information
	_, file, line, _ := runtime.Caller(1)

	// Function to print error with caller info
	printError := func(err error) {
		fmt.Printf("Error at %s:%d - %v\n", file, line, err)
	}

	// Check the type of v and convert accordingly
	switch value := v.(type) {
	case int:
		return value
	case int32:
		return int(value)
	case int64:
		return int(value)
	case float32:
		return int(value)
	case float64:
		return int(value)
	case string:
		i, err := strconv.Atoi(value)
		if err != nil {
			printError(fmt.Errorf("cannot convert string to int: %v", err))
			return 0
		}
		return i
	case bool:
		if value {
			return 1
		}
		return 0
	case []uint8:
		s := string(value)
		i, err := strconv.Atoi(s)
		if err != nil {
			printError(fmt.E<PERSON><PERSON>("cannot convert []uint8 to int: %v", err))
			return 0
		}
		return i
	default:
		// For other types, try to use reflection
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return int(rv.Int())
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return int(rv.Uint())
		case reflect.Float32, reflect.Float64:
			return int(rv.Float())
		default:
			printError(fmt.Errorf("unsupported type for conversion to int: %T", v))
			return 0
		}
	}
}

func ToString(data interface{}) string {
	if data == nil {
		return ""
	}

	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int32:
		return strconv.Itoa(int(v))
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case []uint8:
		return string(v)
	case string:
		return v
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d - failed string conversion of '%v' : actual type %v \n", fn, line, data, reflect.TypeOf(data))
		return ""
	}
}

func ToSingleMap(dataMapArray []map[string]interface{}, key, value string) map[interface{}]interface{} {
	result := make(map[interface{}]interface{})
	for _, data := range dataMapArray {
		var resultKey, resultValue interface{}
		for k, v := range data {
			if k == key {
				resultKey = v
			} else if k == value {
				resultValue = v
			}
		}
		result[resultKey] = resultValue
	}
	return result
}

func ToFloat(data interface{}) float64 {

	dataStr := ToString(data)
	result, err := strconv.ParseFloat(dataStr, 64)
	if err != nil {
		//fmt.Println("Converting string to float error", err, "Data : ", data)
		return 0
	}
	return result
}

func ToFloat64(value interface{}) float64 {
	switch v := value.(type) {
	case int:
		return float64(v)
	case int8:
		return float64(v)
	case int16:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case uint:
		return float64(v)
	case uint8:
		return float64(v)
	case uint16:
		return float64(v)
	case uint32:
		return float64(v)
	case uint64:
		return float64(v)
	case float32:
		return float64(v)
	case float64:
		return v
	case string:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			_, fn, line, _ := runtime.Caller(1)
			fmt.Printf("%s:%d - failed float64 (ParseFloat) conversion of '%v' : actual type %v \n", fn, line, value, reflect.TypeOf(value))
		}
		return f
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d - failed float64 conversion of '%v' : actual type %v \n", fn, line, value, reflect.TypeOf(value))
		return 0
	}
}

func ToMap(data interface{}) map[string]interface{} {
	var jsonByte []byte
	var err error

	switch v := data.(type) {
	case string:
		jsonByte = []byte(v)
	case []byte:
		jsonByte = v
	default:
		jsonByte, err = json.Marshal(data)
		if err != nil {
			return map[string]interface{}{}
		}
	}

	result := make(map[string]interface{})
	err = json.Unmarshal(jsonByte, &result)
	if err != nil {
		fmt.Println("error unmarshal", err)
	}
	return result
}

func ToMapArray(data interface{}) []map[string]interface{} {
	var result []map[string]interface{}
	switch v := data.(type) {
	case []map[string]interface{}:
		result = v
	case map[string]interface{}:
		result = []map[string]interface{}{v}
	case []byte:
		err := json.Unmarshal(v, &result)
		if err != nil {
			fmt.Println("error unmarshalling to map array", err)
		}
	case string:
		jsonByte := []byte(v)
		err := json.Unmarshal(jsonByte, &result)
		if err != nil {
			fmt.Println("error unmarshalling to map array", err)
		}
	default:
		fmt.Println("unsupported type for ToMapArray conversion")
	}
	return result
}

func ToInt64(data interface{}) int64 {
	if data == nil {
		return 0
	}

	if reflect.TypeOf(data).Kind() == reflect.Float64 {
		return int64(data.(float64))
	}

	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}

	result, err := strconv.ParseInt(dataStr, 10, 64)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d parsing to int64 err: %v", fn, line, err)
		return 0
	} else {
		return result
	}
}

func ToFloat32(data interface{}) float32 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseFloat(dataStr, 32)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d parsing to float32 err: %v", fn, line, err)
		return 0
	} else {
		return float32(result)
	}
}
