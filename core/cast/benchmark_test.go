package cast

import (
	"fmt"
	"testing"
)

// Example usage
type User struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Email    string `json:"email,omitempty"`
	IsActive bool   `json:"is_active"`
}

// Benchmark for Single Struct Conversion
func BenchmarkSingleStructConversion(b *testing.B) {
	// Prepare test data
	singleMap := map[string]interface{}{
		"id":        1,
		"name":      "<PERSON>",
		"email":     "<EMAIL>",
		"is_active": true,
	}

	b.Run("Old Reflection Method", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			var user User
			_ = ToModel(singleMap, &user)
		}
	})

	b.Run("Go-JSON Method", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			var user User
			_ = MapToModel(singleMap, &user)
		}
	})
}

// Benchmark for Slice of Structs Conversion
func BenchmarkSliceStructConversion(b *testing.B) {
	// Prepare test data with multiple entries
	multiMap := make([]map[string]interface{}, 100)
	for i := 0; i < 100; i++ {
		multiMap[i] = map[string]interface{}{
			"id":        i,
			"name":      "User " + string(rune(i)),
			"email":     "user" + string(rune(i)) + "@example.com",
			"is_active": i%2 == 0,
		}
	}

	b.Run("Old Reflection Method", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			var users []User
			_ = ToModel(multiMap, &users)
		}
	})

	b.Run("Go-JSON Method", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			var users []User
			_ = MapToModel(multiMap, &users)
		}
	})
}

// Memory Allocation Benchmark
func BenchmarkMemoryAllocation(b *testing.B) {
	singleMap := map[string]interface{}{
		"id":        1,
		"name":      "John Doe",
		"email":     "<EMAIL>",
		"is_active": true,
	}

	b.Run("Old Reflection Method", func(b *testing.B) {
		b.ReportAllocs()
		for i := 0; i < b.N; i++ {
			var user User
			_ = ToModel(singleMap, &user)
		}
	})

	b.Run("Go-JSON Method", func(b *testing.B) {
		b.ReportAllocs()
		for i := 0; i < b.N; i++ {
			var user User
			_ = MapToModel(singleMap, &user)
		}
	})
}

// Expanded User struct to match JSON structure
type EnhancedUser struct {
	ID       int         `json:"id"`
	Name     string      `json:"name"`
	Email    string      `json:"email"`
	IsActive bool        `json:"is_active"`
	Details  UserDetails `json:"details"`
}

type UserDetails struct {
	Age        int     `json:"age"`
	Department string  `json:"department"`
	Salary     float64 `json:"salary"`
}

type UserCollection struct {
	Users []EnhancedUser `json:"users"`
}

func generateLargeMapData(count int) []map[string]interface{} {
	mapData := make([]map[string]interface{}, count)
	for i := 0; i < count; i++ {
		mapData[i] = map[string]interface{}{
			"id":        i + 1,
			"name":      fmt.Sprintf("User %d", i+1),
			"email":     fmt.Sprintf("<EMAIL>", i+1),
			"is_active": i%2 == 0,
			"details": map[string]interface{}{
				"age":        20 + (i % 40),
				"department": []string{"Engineering", "Marketing", "Sales", "Support"}[i%4],
				"salary":     50000.0 + float64(i*100),
			},
		}
	}
	return mapData
}

func BenchmarkLargeDataConversion(b *testing.B) {
	// Different dataset sizes to test scalability
	testSizes := []int{10, 100, 1000, 10000}

	for _, size := range testSizes {
		b.Run(fmt.Sprintf("Old_Reflection_Method_%d_items", size), func(b *testing.B) {
			mapData := generateLargeMapData(size)
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				var users []EnhancedUser
				_ = ToModel(mapData, &users)
			}
		})

		b.Run(fmt.Sprintf("Go-JSON_Method_%d_items", size), func(b *testing.B) {
			mapData := generateLargeMapData(size)
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				var users []EnhancedUser
				_ = MapToModel(mapData, &users)
			}
		})
	}
}
