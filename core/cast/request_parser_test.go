package cast

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/valyala/fasthttp"
)

func TestParseRequestFastHttp(t *testing.T) {
	req := new(fasthttp.RequestCtx)

	requestData := map[string]string{
		"name":  "<PERSON><PERSON>",
		"email": "<EMAIL>",
	}

	reqDataJson, _ := json.Marshal(requestData)

	for k, v := range requestData {
		req.Request.PostArgs().Add(k, v)
	}

	var model struct {
		Email string `json:"email,omitempty"`
		Name  string `json:"name,omitempty"`
	}
	myModel := &model

	type args struct {
		ctx   *fasthttp.RequestCtx
		model interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"test-post", args{ctx: req, model: myModel}, false},
		{"test-get", args{ctx: req, model: myModel}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ParseRequestFastHttp(tt.args.ctx, tt.args.model); (err != nil) != tt.wantErr {
				t.Errorf("ParseRequestFastHttp() error = %v, wantErr %v", err, tt.wantErr)
			}

			respDataJson, _ := json.Marshal(tt.args.model)
			fmt.Printf("result: %s\n", respDataJson)
			if string(respDataJson) != string(reqDataJson) {
				t.Errorf("ParseRequestFastHttp() data not match, want %s, got: %s", reqDataJson, respDataJson)
			}
		})
	}
}
