package cast

import (
	"errors"
	"fmt"
	"reflect"
	"runtime"
	"strings"

	"github.com/goccy/go-json"
)

func ToModel(data interface{}, model interface{}) error {
	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if m, ok := data.(map[string]interface{}); ok {
		return MapToStruct(m, model)
	} else if arr, ok := data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				return MapArrayToStruct(arr, model)
			} else {
				return MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("ToModel failed, model is not single map")
}

// ToModel converts map or slice of maps to a struct or slice of structs
func MapToModel(data interface{}, model interface{}) error {
	var err error
	var jsonData []byte
	if byteData, ok := data.([]byte); ok {
		jsonData = byteData
	} else if strData, ok := data.(string); ok {
		jsonData = []byte(strData)
	} else {
		jsonData, err = json.Marshal(data)
		if err != nil {
			fmt.Println("error marshal data", err)
			return err
		}
	}

	switch v := data.(type) {
	// case map[string]interface{}:
	// 	// Single map to struct
	// 	return json.Unmarshal(jsonData, model)
	case []map[string]interface{}:
		// Slice of maps
		if len(v) == 0 {
			return nil
		}

		// If model is a slice, unmarshal entire array
		if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
			return json.Unmarshal(jsonData, model)
		} else {
			// Single map to struct
			return MapToModel(v[0], model)
		}
	default:
		return json.Unmarshal(jsonData, model)
	}
}

func MapToStruct(m map[string]interface{}, s interface{}) error {
	for k, v := range m {
		SetField(s, k, v)
	}
	return nil
}

func MapArrayToStruct(maps []map[string]interface{}, variable interface{}) error {
	structField := reflect.TypeOf(variable).Elem()
	structArray := reflect.ValueOf(variable).Elem()

	for _, m := range maps {
		newStruct := reflect.New(structField.Elem()).Elem()
		for k, v := range m {
			SetField(newStruct, k, v)
		}
		structArray.Set(reflect.Append(structArray, newStruct))
	}

	return nil
}

func SetField(m interface{}, key string, value interface{}) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("key %s, value %v, err : %v\n", key, value, r)
		}
	}()
	if value == nil {
		return
	}

	//map is not supported yet
	if reflect.ValueOf(value).Kind() == reflect.Map {
		fmt.Println("SetField: map is not supported yet")
		return
	}

	var structValue reflect.Value
	switch res := m.(type) {
	case reflect.Value:
		structValue = res
	default:
		structValue = reflect.ValueOf(m).Elem()
	}

	structFieldValue := structValue.FieldByName(key)

	//if key not match, search for json tag
	if !structFieldValue.IsValid() {
		for i := 0; i < structValue.NumField(); i++ {
			field := structValue.Type().Field(i)
			if field.Anonymous {
				structAnonymous := structValue.Field(i)
				structFieldValue = structAnonymous.FieldByName(key)
				if !structFieldValue.IsValid() {
					isFound := false
					for j := 0; j < structAnonymous.NumField(); j++ {
						fieldAnonymous := structAnonymous.Type().Field(j)
						if v, ok := fieldAnonymous.Tag.Lookup("json"); ok {
							if v == key || strings.HasPrefix(v, fmt.Sprintf("%s,", key)) {
								structFieldValue = structAnonymous.FieldByName(fieldAnonymous.Name)
								isFound = true
								break
							}
						}
					}
					if isFound {
						break
					}
				}
			} else if v, ok := field.Tag.Lookup("json"); ok {
				if v == key || strings.HasPrefix(v, fmt.Sprintf("%s,", key)) {
					structFieldValue = structValue.FieldByName(field.Name)
					break
				}
			}
		}
	}

	if !structFieldValue.IsValid() {
		//fmt.Printf("no such field: %s in obj\n", key)
		return
	}

	if !structFieldValue.CanSet() {
		fmt.Printf("can not set %s field value\n", key)
		return
	}

	structFieldType := structFieldValue.Type()
	val := reflect.ValueOf(value)

	//if data type from struct and map different, convert it
	if structFieldType != val.Type() {
		switch structFieldType.Kind() {
		case reflect.String:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int:
			val = reflect.ValueOf(ToInt(value))
		case reflect.Interface:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int64:
			val = reflect.ValueOf(ToInt64(value))
		case reflect.Float32:
			val = reflect.ValueOf(ToFloat32(value))
		case reflect.Ptr:
			fmt.Println(reflect.ValueOf(&structFieldValue).Elem().Kind())
			fmt.Println(val.Type().Kind() == reflect.Map)
			reflect.ValueOf(&structFieldValue).Elem().Set(reflect.ValueOf(value))
			return
		default:
			if structFieldType.Kind() == reflect.Bool && val.Kind() == reflect.Int64 {
				val = reflect.ValueOf(value.(int64) == 1)
			} else {
				fmt.Printf("field '%s' type didn't match obj field type, type is %v while value type is %v -> '%v'\n", key, structFieldType, val.Type(), value)
				stackSlice := make([]byte, 512)
				s := runtime.Stack(stackSlice, false)
				stacks := string(stackSlice[0:s])
				fmt.Println(">>stacktrace<<", stacks)
				return
			}
		}
	}

	structFieldValue.Set(val)
}
