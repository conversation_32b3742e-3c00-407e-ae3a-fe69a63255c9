package convert

func ToSingleMap(dataMapArray []map[string]interface{}, key, value string) map[interface{}]interface{} {
	result := make(map[interface{}]interface{})
	for _, data := range dataMapArray {
		var resultKey, resultValue interface{}
		for k, v := range data {
			if k == key {
				resultKey = v
			} else if k == value {
				resultValue = v
			}
		}
		result[resultKey] = resultValue
	}
	return result
}

func Boolean(b bool, vTrue interface{}, vFalse interface{}) interface{} {
	if b {
		return vTrue
	}
	return vFalse
}
