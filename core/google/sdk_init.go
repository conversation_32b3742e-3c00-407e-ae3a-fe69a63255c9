package google

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"cloud.google.com/go/profiler"
	"cloud.google.com/go/pubsub"
	"cloud.google.com/go/storage"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"google.golang.org/api/option"
)

var storageClient *storage.Client
var pubsubClient *pubsub.Client

func init() {
	fmt.Println("init google sdk...")
	opt := option.WithCredentialsFile("config/auth/google_credential.json")
	ctx := context.Background()

	var err error
	storageClient, err = storage.NewClient(ctx, opt)
	if err != nil && os.Getenv("ENV") != "localhost" {
		// log.Fatalf("init storage client error - %v", err)
		return
	}

	projectId := os.Getenv("PROJECT_ID")
	pubsubCredPath := os.Getenv("PUBSUB_CREDENTIAL_PATH")
	if pubsubCredPath == "" {
		pubsubCredPath = "config/auth/pubsub_credential.json"
	}

	if projectId == "" {
		credential, err := utils.ReadFile(pubsubCredPath)
		if err == nil {
			var serviceAccount struct {
				ProjectID string `json:"project_id"`
			}
			err = json.Unmarshal([]byte(credential), &serviceAccount)
			if err == nil {
				projectId = serviceAccount.ProjectID
			}
		} else {
			fmt.Println("reading credential file err: ", err)
		}
	}

	fmt.Println("projectId: ", projectId)
	pubsubClient, err = pubsub.NewClient(ctx, projectId, option.WithCredentialsFile(pubsubCredPath))
	if err != nil {
		fmt.Println("pubsub.NewClient: ", err)
	}
}

func GetStorageClient() *storage.Client {
	return storageClient
}

func GetPubSubClient() *pubsub.Client {
	return pubsubClient
}

func Profiler() profiler.Config {
	fmt.Println("get profiler...")

	os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", "config/auth/gcloud-service-key.json")
	serviceName := strings.TrimSpace(fmt.Sprintf("api-crm-%v", os.Getenv("ENV")))

	//optional tag
	if tag := os.Getenv("SERVER_TAG"); tag != "" {
		serviceName = fmt.Sprintf("%v-%v", serviceName, strings.ToLower(tag))
	}

	cfg := profiler.Config{
		Service:        serviceName,
		ServiceVersion: "1.0.0",
		ProjectID:      "uniq-187911",
	}
	return cfg
}
