package google

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"cloud.google.com/go/pubsub"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

func Publish(topicId string, data map[string]interface{}) error {
	client := GetPubSubClient()
	if client == nil {
		log.Info("pubsub client has not been initialized, topic: %v", topicId)
		return errors.New("pubsub client has not been initialized")
	}

	t := client.Topic(topicId)
	ctx := context.Background()
	result := t.Publish(ctx, &pubsub.Message{
		Data: []byte(utils.SimplyToJson(data)),
	})

	id, err := result.Get(ctx)
	if log.IfError(err) {
		// Error handling code can be added here.
		fmt.Printf("pubsub: failed to publish to topic '%s' with data: %s --> %v\n", topicId, utils.Simply<PERSON><PERSON><PERSON><PERSON>(data), err)
		return err
	} else {
		fmt.Printf("pubsub: published to topic: '%s', msg ID: %v\n", topicId, id)
	}
	return nil
}

func Subscribe(subsId string, action func(data []byte) bool) error {
	client := pubsubClient
	if client == nil {
		fmt.Println("pubsub client has not been initialized")
		return fmt.Errorf("pubsub client has not been initialized")
	}

	fmt.Println("pubsub try to subscribe to id: ", subsId)

	var mu sync.Mutex
	ctx := context.Background()
	sub := client.Subscription(subsId)
	cctx, _ := context.WithCancel(ctx)
	err := sub.Receive(cctx, func(ctx context.Context, msg *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		fmt.Printf("pubsub receive message, subs id: %s | id: %s | %s \n", subsId, msg.ID, string(msg.Data))
		if action(msg.Data) {
			fmt.Printf("pubsub ack? : %v | id: %s \n", true, msg.ID)
			msg.Ack()
		}
	})

	fmt.Printf("pubsub subsribe to %s error: %v \n", subsId, err)
	return err
}
