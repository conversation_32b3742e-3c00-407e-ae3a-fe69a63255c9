package google

import (
	"cloud.google.com/go/storage"
	"context"
	"fmt"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"io"
)

func UploadFile(r io.Reader, desPath string, public bool) (string, error) {
	ctx := context.Background()
	client := GetStorageClient()

	bucket := "uniq-187911.appspot.com"
	bh := client.Bucket(bucket)

	//check if bucket exist
	if _, err := bh.Attrs(ctx); log.IfError(err) {
		log.Debug("bucket '%s' is not exist", bucket)
		return "", err
	}

	obj := bh.Object(desPath)
	w := obj.NewWriter(ctx)
	if _, err := io.Copy(w, r); log.IfError(err) {
		return "", err
	}

	if err := w.Close(); log.IfError(w.Close()) {
		return "", err
	}

	if public {
		if err := obj.ACL().Set(ctx, storage.AllUsers, storage.RoleReader); log.IfError(err) {
			return "", err
		}
	}

	attrs, err := obj.Attrs(ctx)
	log.IfError(err)

	url := fmt.Sprintf("https://storage.googleapis.com/%s/%s", attrs.Bucket, attrs.Name)
	return url, nil
}
