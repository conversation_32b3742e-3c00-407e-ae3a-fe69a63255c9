package log

import (
	"errors"
	"fmt"
	"os"

	"github.com/bugsnag/bugsnag-go/v2"
)

// BugsnagHook implements the Hook interface for Bugsnag error reporting
type BugsnagHook struct {
}

// NewBugsnagHook creates a new Bugsnag hook
func NewBugsnagHook(apiKey string) (*BugsnagHook, error) {
	config := bugsnag.Configuration{
		APIKey: apiKey,
		// You can add more configuration options here
		ReleaseStage: os.Getenv("ENV"), // This can be made configurable
	}

	bugsnag.Configure(config)

	return &BugsnagHook{}, nil
}

// Fire implements the Hook interface
func (hook *BugsnagHook) Fire(entry *Entry) error {
	// Only send errors and higher severity to Bugsnag
	if entry.Level <= ErrorLevel {
		metadata := bugsnag.MetaData{
			"fields": entry.Fields,
		}

		// If the message contains an error, report it
		err := bugsnag.Notify(
			errors.New(entry.Message),
			metadata,
		)

		fmt.Println("--------- sent to bugsnag, ", err)
		return err
	}

	return nil
}

// Levels returns the logging levels that this hook should be fired for
func (hook *BugsnagHook) Levels() []Level {
	return []Level{
		PanicLevel,
		FatalLevel,
		ErrorLevel,
	}
}
