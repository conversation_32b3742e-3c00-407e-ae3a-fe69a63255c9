package log

import (
	"fmt"
	"runtime"
	"strings"
	"time"
)

type timeElapse struct {
	timeStart time.Time
	timeLog   time.Time
	logStr    strings.Builder
}

func TimeInit() *timeElapse {
	return &timeElapse{
		timeStart: time.Now(),
		timeLog:   time.Now(),
		logStr:    strings.Builder{},
	}
}

func (t *timeElapse) AddLog(log string) *timeElapse {
	t.logStr.WriteString(fmt.Sprintf(">> %s took: %v ", log, time.Since(t.timeLog)))
	t.timeLog = time.Now()
	return t
}

func (t *timeElapse) Print() {
	_, file, line, _ := runtime.Caller(1)
	// if time.Since(t.timeStart) > time.Second*10 {

	// }
	fmt.Printf("%v:%v all took: %v --  %s\n", file, line, time.Since(t.timeStart), t.logStr.String())
}
