package log

import (
	"context"
	"fmt"
	"log"
	"os"
	"regexp"
	"runtime"
	"strings"
	"time"

	"cloud.google.com/go/logging"
	"github.com/joho/godotenv"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"google.golang.org/api/option"
)

const (
	PREFIX_DEBUG = "\033[1;37m[DEBUG]\033[0m"
	PREFIX_INFO  = "\033[1;32m[INFO]\033[0m"
	PREFIX_WARN  = "\033[1;33m[WARN]\033[0m"
	PREFIX_ERROR = "\033[1;31m[ERROR]\033[0m"

	LOG_APP_ID = "api-crm"
)

var (
	loggerClient *logging.Client
	useColor     bool
)

func init() {
	e := godotenv.Load()
	if e != nil {
		log.Printf("load env error - %v", e)
	}

	ctx := context.Background()
	var err error
	optLogger := option.WithCredentialsFile("config/auth/google_credential_logger.json")
	loggerClient, err = logging.NewClient(ctx, os.Getenv("GCP_PROJECT_ID"), optLogger)
	if err != nil {
		log.Printf("init logger client error - %v", err)
	}

	useColor = os.Getenv("LOG_USE_COLOR") == "true"
}

func GetLogger() *logging.Logger {
	return loggerClient.Logger(LOG_APP_ID)
}

func LoggerLabel() map[string]string {
	return map[string]string{
		"environment": os.Getenv("server"),
	}
}

func WriteLog(log string, severity logging.Severity) {
	if loggerClient != nil {
		//GetLogger().Log(logging.Entry{Payload: log, Severity: severity, Labels: LoggerLabel()})
	}
}

func Debug(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("  %s:%d  >> ", utils.GetFileName(fn, true), line)
	logMsg := fmt.Sprintf(prefix+msg+"\n", v...)
	if useColor {
		fmt.Printf(PREFIX_DEBUG + " " + GetDate() + logMsg)
	} else {
		fmt.Printf(logMsg)
	}
	WriteLog(logMsg, logging.Debug)
}

func Info(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("%s:%d  >> ", utils.GetFileName(fn, true), line)
	logMsg := fmt.Sprintf(prefix+msg+"\n", v...)
	if useColor {
		fmt.Printf(PREFIX_INFO + " " + GetDate() + logMsg)
	} else {
		fmt.Printf(logMsg)
	}
	WriteLog(logMsg, logging.Info)
}

func Warn(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf(" %s:%d  >> ", utils.GetFileName(fn, true), line)
	fmt.Printf(GetDate()+prefix+msg+"\n", v...)
	WriteLog(fmt.Sprintf(prefix+msg+"\n", v...), logging.Warning)
}

func Error(msg string, v ...interface{}) {
	msg = fmt.Sprintf(msg, v...)
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("  %s:%d  >> ", utils.GetFileName(fn, true), line)
	if useColor {
		println(PREFIX_ERROR + " " + GetDate() + prefix + msg)
	} else {
		println(GetDate() + prefix + msg)
	}
	WriteLog(fmt.Sprintf(prefix+msg+"\n", v...), logging.Error)

	go func() {
		serviceName := strings.ToUpper(os.Getenv("server"))
		//optional tag
		if tag := os.Getenv("SERVER_TAG"); tag != "" {
			serviceName = fmt.Sprintf("%v-%v", serviceName, strings.ToUpper(tag))
		}
		utils.SendMessageToSlack(fmt.Sprintf("#%s  [ERROR] %s  :: %s", serviceName, prefix, msg))
	}()
}

func IfError(err error) (res bool) {
	if err != nil {
		stackSlice := make([]byte, 512)
		s := runtime.Stack(stackSlice, false)
		stacks := string(stackSlice[0:s])
		stacks = beautifyStacks(stacks)

		_, file, line, _ := runtime.Caller(1)
		//msg := fmt.Sprintf("%s %s %s $%s()  >> %v ", PREFIX_ERROR, GetDate(), callers[len(callers)-1], strings.TrimSpace(functions[len(functions)-1]), err)
		msg := fmt.Sprintf("%s:%v  >> %v ", file, line, err)
		fmt.Println(msg)
		WriteLog(msg, logging.Error)
		fmt.Println(stacks)

		for _, hook := range logs.Hooks[ErrorLevel] {
			err := hook.Fire(&Entry{
				Logger:  logs,
				Level:   ErrorLevel,
				Message: msg,
				Fields:  Fields{"stack": stacks},
			})
			if err == nil {
				break
			} else {
				fmt.Println("sending hook err ", err)
			}
		}

		// if os.Getenv("ENV") != "localhost" {
		// 	go func(msg string) {
		// utils.SendMessageToSlack(fmt.Sprintf("[ERROR] >> %s \n```%s```", strings.Replace(msg, PREFIX_ERROR, "", -1), stacks))
		// 	}(msg)
		// }

		return true
	}

	return false
}

func IfErrorSetStatus(ctx *fasthttp.RequestCtx, err error) bool {
	if IfError(err) {
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return true
	}
	return false
}

func GetDate() string {
	_, offset := time.Now().Zone()
	diff := int64(25200 - offset) //25200 is developer offset (WIB)
	return time.Unix(time.Now().Unix()+diff, 0).Format("02/01/2006 15:04:05")
}

func beautifyStacks(stacks string) (result string) {
	defer func() {
		if r := recover(); r != nil {
			result = stacks
		}
	}()

	rex, _ := regexp.Compile(`[/a-zA-Z\d\-_/]+\.go:\d+\s`)
	callers := rex.FindAllString(stacks, -1)

	rex, _ = regexp.Compile(`([a-zA-Z\\(\\*\\)]+\.{1,3})+[a-zA-Z]+((\([a-zA-Z0-9\s,\.]*\))|)\012`)
	functions := rex.FindAllString(stacks, -1)
	funcNames := make([]string, 0)
	for _, x := range functions {
		fName := strings.Replace(x, "\n", "", 1)
		param := regexp.MustCompile(`\([^\\*].+\)`).FindString(fName)
		if param != "" {
			fName = strings.Replace(fName, param, "()", 1)
		}
		funcNames = append(funcNames, fName)
	}

	for i, f := range callers {
		if i == 0 {
			continue
		}
		fName := ""
		if len(funcNames) > i {
			fName = funcNames[i]
		}
		if fName == "log.IfError()" {
			continue
		}
		result += fmt.Sprintf("\tat %s   %s\n", fName, strings.TrimSpace(f))
	}
	return result
}
