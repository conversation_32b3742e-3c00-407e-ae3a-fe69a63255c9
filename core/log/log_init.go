package log

import (
	"io"
	"sync"
)

// Level type
type Level uint32

// These are the different logging levels
const (
	// PanicLevel level, highest level of severity
	PanicLevel Level = iota
	// FatalLevel level
	FatalLevel
	// ErrorLevel level
	ErrorLevel
	// WarnLevel level
	WarnLevel
	// InfoLevel level
	InfoLevel
	// DebugLevel level
	DebugLevel
)

// Fields type, used to pass to `WithFields`
type Fields map[string]interface{}

// Hook interface for external logging systems
type Hook interface {
	Levels() []Level
	Fire(entry *Entry) error
}

// Entry represents a log entry
type Entry struct {
	Logger  *Logger
	Level   Level
	Time    string
	Message string
	Fields  Fields
}

// Logger represents the logger structure
type Logger struct {
	Out       io.Writer
	Hooks     map[Level][]Hook
	Level     Level
	mu        sync.Mutex
	Fields    Fields
	Formatter Formatter
}

// Formatter interface is used to implement a custom Formatter
type Formatter interface {
	Format(*Entry) ([]byte, error)
}

// New creates a new Logger
func New() *Logger {
	return &Logger{
		Out:       nil,
		Hooks:     make(map[Level][]Hook),
		Level:     InfoLevel,
		Fields:    make(Fields),
		Formatter: &TextFormatter{},
	}
}

// AddHook adds a hook to the logger hooks
func (logger *Logger) AddHook(hook Hook) {
	logger.mu.Lock()
	defer logger.mu.Unlock()
	for _, level := range hook.Levels() {
		logger.Hooks[level] = append(logger.Hooks[level], hook)
	}
}

// TextFormatter implements the Formatter interface
type TextFormatter struct {
	// Add formatting options here
	TimestampFormat string
	ColorScheme     bool
}

func (f *TextFormatter) Format(entry *Entry) ([]byte, error) {
	// Basic implementation - can be enhanced later
	return []byte(entry.Time + " [" + entry.Level.String() + "] " + entry.Message + "\n"), nil
}

// String returns the string representation of the log level
func (level Level) String() string {
	switch level {
	case PanicLevel:
		return "PANIC"
	case FatalLevel:
		return "FATAL"
	case ErrorLevel:
		return "ERROR"
	case WarnLevel:
		return "WARN"
	case InfoLevel:
		return "INFO"
	case DebugLevel:
		return "DEBUG"
	}
	return "UNKNOWN"
}

// default logs
var (
	logs = New()
)

// AddHook adds a hook to the logger hooks
func AddHook(hook Hook) {
	logs.mu.Lock()
	defer logs.mu.Unlock()
	for _, level := range hook.Levels() {
		logs.Hooks[level] = append(logs.Hooks[level], hook)
	}
}
