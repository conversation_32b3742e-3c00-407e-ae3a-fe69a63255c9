package log

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// SlackHook implements the Hook interface for Slack notifications
type SlackHook struct {
	WebhookURL string
	Channel    string
	Username   string
	IconEmoji  string
	Fields     []Field
}

// SlackMessage represents the message structure for Slack
type SlackMessage struct {
	Username    string       `json:"username,omitempty"`
	IconEmoji   string       `json:"icon_emoji,omitempty"`
	Channel     string       `json:"channel,omitempty"`
	Text        string       `json:"text"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

// Attachment represents a Slack message attachment
type Attachment struct {
	Color  string  `json:"color"`
	Text   string  `json:"text"`
	Fields []Field `json:"fields,omitempty"`
}

// Field represents a field in a Slack message attachment
type Field struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// NewSlackHook creates a new Slack hook
func NewSlackHook(webhookURL string, options ...func(*SlackHook)) (*SlackHook, error) {
	if webhookURL == "" {
		return nil, fmt.Errorf("webhook URL is required")
	}

	hook := &SlackHook{
		WebhookURL: webhookURL,
		Username:   "Logger Bot",
		IconEmoji:  ":warning:",
	}

	// Apply options
	for _, option := range options {
		option(hook)
	}

	return hook, nil
}

// WithChannel sets the Slack channel
func WithChannel(channel string) func(*SlackHook) {
	return func(h *SlackHook) {
		h.Channel = channel
	}
}

// WithUsername sets the bot username
func WithUsername(username string) func(*SlackHook) {
	return func(h *SlackHook) {
		h.Username = username
	}
}

// WithIconEmoji sets the bot icon emoji
func WithIconEmoji(emoji string) func(*SlackHook) {
	return func(h *SlackHook) {
		h.IconEmoji = emoji
	}
}

func WithField(fields map[string]string) func(*SlackHook) {
	return func(h *SlackHook) {
		if h.Fields == nil {
			h.Fields = make([]Field, 0)
		}
		for k, v := range fields {
			h.Fields = append(h.Fields, Field{
				Title: k,
				Value: v,
			})
		}
	}
}

// Fire implements the Hook interface
func (hook *SlackHook) Fire(entry *Entry) error {
	color := hook.getColorForLevel(entry.Level)

	// Create fields from entry.Fields
	var fields []Field
	for key, value := range entry.Fields {
		fields = append(fields, Field{
			Title: key,
			Value: fmt.Sprintf("%v", value),
			Short: true,
		})
	}

	for _, field := range hook.Fields {
		fields = append(fields, field)
	}

	msg := &SlackMessage{
		Username:  hook.Username,
		IconEmoji: hook.IconEmoji,
		Channel:   hook.Channel,
		Attachments: []Attachment{
			{
				Color:  color,
				Text:   fmt.Sprintf("*[%s]* %s", entry.Level.String(), entry.Message),
				Fields: fields,
			},
		},
	}

	return hook.send(msg)
}

// Levels returns the logging levels that this hook should be fired for
func (hook *SlackHook) Levels() []Level {
	return []Level{
		PanicLevel,
		FatalLevel,
		ErrorLevel,
	}
}

// getColorForLevel returns the color code for the given log level
func (hook *SlackHook) getColorForLevel(level Level) string {
	switch level {
	case PanicLevel, FatalLevel:
		return "#FF0000" // Red
	case ErrorLevel:
		return "#D00000" // Dark Red
	case WarnLevel:
		return "#FFA500" // Orange
	case InfoLevel:
		return "#00FF00" // Green
	default:
		return "#808080" // Gray
	}
}

// send sends the message to Slack
func (hook *SlackHook) send(msg *SlackMessage) error {
	body, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	resp, err := http.Post(hook.WebhookURL, "application/json", bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("slack webhook failed with status code: %d", resp.StatusCode)
	}

	return nil
}
