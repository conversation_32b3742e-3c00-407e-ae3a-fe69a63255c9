package messaging

import (
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

func SendEmail(toAddress, content, subject string) error {
	//using pubsub instead
	scheduleMsg := map[string]interface{}{
		"title":    subject,
		"message":  content,
		"media":    "email",
		"receiver": toAddress,
	}
	err := google.Publish("messaging-gateway-production", scheduleMsg)

	//if using pubsub success, return nil
	if err == nil {
		fmt.Print("sending email with pubsub success....")
		return nil
	}

	request := utils.HttpRequest{
		Url:    fmt.Sprintf("%s/send/message", os.Getenv("API_MESSAGER")),
		Method: "POST",
		PostRequest: utils.PostRequest{
			Body: map[string]interface{}{
				"message": content,
				"title":   subject,
				"recipient": []map[string]interface{}{
					{
						"media":   "email",
						"address": to<PERSON>dd<PERSON>,
					},
				},
			},
		},
	}
	resp, err := request.ExecuteRequest()
	fmt.Println("send email resp : ", string(resp))
	return err
}
