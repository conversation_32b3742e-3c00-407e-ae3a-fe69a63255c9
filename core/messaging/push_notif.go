package messaging

import (
	"fmt"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"time"
)

type Notification struct {
	Title         string
	Message       string
	MemberId      interface{}
	AdminId       interface{}
	FirebaseToken string
}

func PushNotification(notification Notification) {
	if notification.FirebaseToken == "" {
		member, err := db.Query("SELECT firebase_token FROM members_detail where member_fkid = ? and admin_fkid = ? LIMIT 1", notification.MemberId, notification.AdminId)
		if log.IfError(err) {
			return
		}
		if member["firebase_token"] != nil {
			notification.FirebaseToken = utils.ToString(member["firebase_token"])
		}
	}

	resp, err := db.Insert("system_notification", map[string]interface{}{
		"title":        notification.Title,
		"message":      notification.Message,
		"type":         "member",
		"receiver_id":  notification.MemberId,
		"admin_fkid":   notification.AdminId,
		"data_created": time.Now().Unix() * 1000,
	})
	if log.IfError(err) {
		return
	}

	notifId, _ := resp.LastInsertId()

	if notification.FirebaseToken == "" {
		log.Info("this will not send. token is null. --> %s", utils.SimplyToJson(notification))
		return
	}

	request := utils.HttpRequest{}
	request.Method = "POST"
	request.Url = "https://fcm.googleapis.com/fcm/send"
	request.Header = map[string]interface{}{
		"Authorization": "key = AIzaSyBzCzuMS1dtkHGi0QDp7W76DKaQR3_3u8k",
		"Content-Type":  "application/json",
	}
	request.PostRequest.Body = map[string]interface{}{
		"registration_ids": []string{notification.FirebaseToken},
		"data": map[string]interface{}{
			"message": notification.Message,
			"title":   notification.Title,
			"id":      notifId,
		},
	}
	body, err := request.ExecuteRequest()
	log.IfError(err)
	fmt.Println("FMC Resp : ", string(body))
}
