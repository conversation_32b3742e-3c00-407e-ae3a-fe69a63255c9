package db

import (
	"context"
	"errors"
	"fmt"
	"time"
)

func (db *Repository) SetQueryX(cacheKey string, expiration time.Duration, sql string, args ...interface{}) (result *SqlResult) {
	dbx := GetDbx()
	if dbx == nil {
		return &SqlResult{Error: errors.New("dbx not initialized")}
	}

	result = &SqlResult{useDbx: true, repo: db}
	result.queryX = QueryX{
		Sql:        sql,
		Args:       args,
		CacheKey:   cacheKey,
		Expiration: expiration,
	}
	return
}

func (db *Repository) RunQueryX(param QueryX, model interface{}) (result *SqlResult) {
	dbx := GetDbx().Unsafe()
	result = &SqlResult{}

	ctx, cancel := context.WithTimeout(context.Background(), 90*time.Second)
	defer cancel()

	result.Error = dbx.SelectContext(ctx, model, param.Sql, param.Args...)
	if result.Error == nil {
		go db.saveCache(model, param.CacheKey, param.Expiration, param.Args...)
	} else {
		//log err
		fmt.Println("[ERROR] dbx query: ", result.Error)
	}
	return
}
