package db

import (
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"strconv"
	"strings"
)

const CommaSpace = ", "

type MySQLQueryBuilder struct {
	Tokens []string
	Params []interface{}
	Raw    map[string]interface{}
}

// Select will join the fields
func (qb *MySQLQueryBuilder) Select(fields ...string) QueryBuilder {
	qb.Raw["select"] = strings.Join(fields, CommaSpace)
	qb.Tokens = nil
	qb.Tokens = append(qb.Tokens, "SELECT", strings.Join(fields, CommaSpace))
	qb.From()
	return qb
}

// ForUpdate add the FOR UPDATE clause
func (qb *MySQLQueryBuilder) ForUpdate() QueryBuilder {
	qb.Tokens = append(qb.Tokens, "FOR UPDATE")
	return qb
}

// From join the tables
func (qb *MySQLQueryBuilder) From() QueryBuilder {
	qb.Tokens = append(qb.Tokens, "FROM", qb.Raw["from"].(string))
	return qb
}

// InnerJoin INNER JOIN the table
func (qb *MySQLQueryBuilder) InnerJoin(table string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "INNER JOIN", table)
	return qb
}

// LeftJoin LEFT JOIN the table
func (qb *MySQLQueryBuilder) LeftJoin(table string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "LEFT JOIN", table)
	return qb
}

// RightJoin RIGHT JOIN the table
func (qb *MySQLQueryBuilder) RightJoin(table string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "RIGHT JOIN", table)
	return qb
}

// On join with on cond
func (qb *MySQLQueryBuilder) On(cond string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "ON", cond)
	return qb
}

// Where join the Where cond
func (qb *MySQLQueryBuilder) Where(cond string, params ...interface{}) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "WHERE", cond)
	for param := range params {
		qb.Params = append(qb.Params, param)
	}
	return qb
}

// And join the and cond
func (qb *MySQLQueryBuilder) And(cond string, param interface{}) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "AND", cond)
	qb.Params = append(qb.Params, param)
	return qb
}

// Or join the or cond
func (qb *MySQLQueryBuilder) Or(cond string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "OR", cond)
	return qb
}

// In join the IN (vals)
func (qb *MySQLQueryBuilder) In(vals ...string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "IN", "(", strings.Join(vals, CommaSpace), ")")
	return qb
}

// OrderBy join the Order by fields
func (qb *MySQLQueryBuilder) OrderBy(fields ...string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "ORDER BY", strings.Join(fields, CommaSpace))
	return qb
}

// Asc join the asc
func (qb *MySQLQueryBuilder) Asc() QueryBuilder {
	qb.Tokens = append(qb.Tokens, "ASC")
	return qb
}

// Desc join the desc
func (qb *MySQLQueryBuilder) Desc() QueryBuilder {
	qb.Tokens = append(qb.Tokens, "DESC")
	return qb
}

// Limit join the limit num
func (qb *MySQLQueryBuilder) Limit(limit int) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "LIMIT", strconv.Itoa(limit))
	return qb
}

// Offset join the offset num
func (qb *MySQLQueryBuilder) Offset(offset int) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "OFFSET", strconv.Itoa(offset))
	return qb
}

// GroupBy join the Group by fields
func (qb *MySQLQueryBuilder) GroupBy(fields ...string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "GROUP BY", strings.Join(fields, CommaSpace))
	return qb
}

// Having join the Having cond
func (qb *MySQLQueryBuilder) Having(cond string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "HAVING", cond)
	return qb
}

// Update join the update table
func (qb *MySQLQueryBuilder) Update(tables ...string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "UPDATE", strings.Join(tables, CommaSpace))
	return qb
}

// Set join the set kv
func (qb *MySQLQueryBuilder) Set(kv ...string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "SET", strings.Join(kv, CommaSpace))
	return qb
}

// Delete join the Delete tables
func (qb *MySQLQueryBuilder) Delete(cond string, params ...interface{}) QueryBuilder {
	qb.Tokens = nil
	qb.Tokens = append(qb.Tokens, "DELETE")
	qb.From()
	qb.Where(cond, params)
	return qb
}

// InsertInto join the insert SQL
func (qb *MySQLQueryBuilder) InsertInto(table string, fields ...string) QueryBuilder {
	qb.Tokens = append(qb.Tokens, "INSERT INTO", table)
	if len(fields) != 0 {
		fieldsStr := strings.Join(fields, CommaSpace)
		qb.Tokens = append(qb.Tokens, "(", fieldsStr, ")")
	}
	return qb
}

// Values join the Values(vals)
func (qb *MySQLQueryBuilder) Values(vals ...string) QueryBuilder {
	valsStr := strings.Join(vals, CommaSpace)
	qb.Tokens = append(qb.Tokens, "VALUES", "(", valsStr, ")")
	return qb
}

// Subquery join the sub as alias
func (qb *MySQLQueryBuilder) Subquery(sub string, alias string) string {
	return fmt.Sprintf("(%s) AS %s", sub, alias)
}

func (qb *MySQLQueryBuilder) Query(query string) QueryBuilder {
	qb.Tokens = nil
	qb.Tokens = append(qb.Tokens, query)
	return qb
}

// String join all Tokens
func (qb *MySQLQueryBuilder) String() string {
	return strings.Join(qb.Tokens, " ")
}

func (qb *MySQLQueryBuilder) Get() ([]map[string]interface{}, error) {
	fmt.Println("SQL : ", qb.String())
	return QueryArray(qb.String(), qb.Params...)
}

func (qb *MySQLQueryBuilder) First() (map[string]interface{}, error) {
	fmt.Println("SQL : ", qb.String())
	return Query(qb.String(), qb.Params...)
}

func (qb *MySQLQueryBuilder) DataTables(ctx *fasthttp.RequestCtx) (map[string]interface{}, error) {
	draw := ctx.QueryArgs().Peek("draw")
	search := string(ctx.QueryArgs().Peek("search[value]"))
	length, err := strconv.Atoi(string(ctx.QueryArgs().Peek("length")))
	start, err := strconv.Atoi(string(ctx.QueryArgs().Peek("start")))

	var columns []string
	index := 0
	for true {
		col := ctx.QueryArgs().Peek(fmt.Sprintf("columns[%d][data]", index))
		fmt.Println("Col : ", string(col))
		if col == nil {
			fmt.Println("No col, break...")
			break
		}
		columns = append(columns, string(col))
		index++
	}

	if qb.Raw["select"] == nil {
		qb.Select(columns...)
	}

	fmt.Println("Search : ", search)
	var whereStr string
	if len(search) > 0 {
		for k, v := range columns {
			if k != 0 {
				whereStr += " OR "
			}
			whereStr += v + " LIKE ?" //like
			qb.Params = append(qb.Params, "%"+search+"%")
		}
		qb.Where(whereStr)
	}

	qb.Limit(length)
	qb.Offset(start)
	query := qb.String()
	fmt.Println(query)
	data, err := QueryArray(query, qb.Params...)
	if utils.CheckErr(err) {
		fmt.Println("Query : ", query)
	}

	recordTotal := len(data)

	count, err := Query(fmt.Sprintf("SELECT COUNT(*) as cnt FROM %s", qb.Raw["from"]))
	utils.CheckErr(err)
	recordTotal, err = strconv.Atoi(count["cnt"].(string))

	recordsFiltered := recordTotal
	if len(search) > 0 {
		recordsFiltered = len(data)
	}

	return map[string]interface{}{
			"draw":            string(draw),
			"recordsTotal":    recordTotal,
			"recordsFiltered": recordsFiltered,
			"data":            data},
		err
}
