package db

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"reflect"
	"runtime"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/domain"
)

// Repository struct
type Repository struct {
	Conn     *sql.DB
	option   queryOption
	sql      string
	args     []interface{}
	LogQuery bool
	CacheDb  domain.CacheInterface
}

// Query sql
func (db *Repository) Query(sql string, args ...interface{}) (result *SqlResult) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	return db.QueryContext(ctx, sql, args...)
}

func (db *Repository) QueryContext(ctx context.Context, sql string, args ...interface{}) (result *SqlResult) {
	tableData := make([]map[string]interface{}, 0)
	result = &SqlResult{Data: tableData}

	if db.option == nil {
		db.option = defaultOption(0)
	}

	db.sql = sql
	db.args = args

	sql, args = db.option.alterQuery(sql, args)
	result.SqlQuery = getSQLRaw(sql, args...)

	rows, err := db.Conn.QueryContext(ctx, sql, args...)
	if err != nil {
		result.Error = err
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> Query Err: %v \n>>%v\n", file, line, result.SqlQuery, err)
		return
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			log.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		result.Error = err
		return
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			result.Error = err
			return
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]
			if b, ok := v.([]byte); ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	result.Error = rows.Err()
	result.Data = db.option.getData(db, tableData)
	return
}

func (db *Repository) Prepare(sql string, args ...interface{}) *Repository {
	if db.option == nil {
		db.option = defaultOption(0)
	}

	db.sql = sql
	db.args = args

	db.sql, db.args = db.option.alterQuery(db.sql, db.args)

	return db
}

func (db *Repository) GetWithCache(cacheKey string, expiration time.Duration, model interface{}) error {
	err := db.fromCache(model, cacheKey, db.args...)
	if err == nil {
		return nil
	}
	err = db.Get(model)
	if err != nil {
		return err
	}

	//save cache
	go db.saveCache(model, cacheKey, expiration, db.args...)
	return nil
}

// func (db *Repository) GetWithContext(ctx context.Context, model interface{}) error {

// }

func (db *Repository) Get(model interface{}) error {
	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	rows, err := db.Conn.QueryContext(ctx, db.sql, db.args...)
	if err != nil {
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> Query Err: %v \n", file, line, err)
		fmt.Println(">> [ERR] ", getSQLRaw(db.sql, db.args...))
		fmt.Println(">> [args] ", db.args)
		return err
	} else if db.LogQuery {
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> %v \n", file, line, getSQLRaw(db.sql, db.args...))
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			log.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		return err
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	var structField reflect.Type
	var structArray reflect.Value
	isModelSlice := reflect.ValueOf(model).Elem().Kind() == reflect.Slice

	if isModelSlice {
		structField = reflect.TypeOf(model).Elem()
		structArray = reflect.ValueOf(model).Elem()
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return err
		}

		if isModelSlice {
			newStruct := reflect.New(structField.Elem()).Elem()
			for i, col := range columns {
				cast.SetField(newStruct, col, values[i])
			}
			structArray.Set(reflect.Append(structArray, newStruct))
		} else {
			for i, col := range columns {
				cast.SetField(model, col, values[i])
			}
			break
		}
	}

	return nil
}

func (db *Repository) PrintSql() *Repository {
	query := getSQLRaw(db.sql, db.args...)
	_, file, line, _ := runtime.Caller(1)
	fmt.Printf("%v:%v >> %v \n", file, line, query)
	return db
}

// Insert func
func (db *Repository) Insert(table string, data map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?) "
	res, err := db.Conn.Exec(query, values...)
	return res, err
}

// BulkInsert insert bulk
func (db *Repository) BulkInsert(table string, data []map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table
	var key []string

	for _, t := range data {
		query += "("
		for u := range t {
			query += u + ", "
			key = append(key, u)
		}
		query = query[:len(query)-2]
		query += ") VALUES "
		break
	}

	for _, v := range data {
		query += "("
		for _, w := range key {
			values = append(values, v[w])
			query += "?, "
		}
		query = query[:len(query)-2]
		query += "), "
	}
	query = query[:len(query)-2]
	res, err := db.Conn.Exec(query, values...)
	return res, err
}

func (db *Repository) Update(table string, data map[string]interface{}, whereQuery string, whereParams ...interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereQuery

	for _, param := range whereParams {
		values = append(values, param)
	}

	res, err := db.Conn.Exec(query, values...)
	if err != nil {
		fmt.Printf("Query Error >> `%s`\n", getSQLRaw(query, values...))
	}

	return res, err
}

// Updates func
func (db *Repository) Updates(table string, data map[string]interface{}, where map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE "

	for col, val := range where {
		query += col + "=? AND "
		values = append(values, val)
	}
	query = query[:len(query)-4] // -4 : to remove word 'AND' at the end

	res, err := db.Conn.Exec(query, values...)
	if err != nil {
		fmt.Printf("Query Error >> `%s`\n", getSQLRaw(query, values...))
	}

	return res, err

}

// SingleUpdate single update query
func (db *Repository) SingleUpdate(table string, where string, data []map[string]interface{}) (sql.Result, error) {
	var key []string
	query := "UPDATE " + table
	fmt.Println(data)
	for _, a := range data {
		query += " SET "
		for b := range a {
			query += b + " = ?, "
			key = append(key, b)
		}
		query = query[:len(query)-2]
		break
	}

	query += " WHERE " + where + " = ?"

	val := []interface{}{}
	for _, v := range data {
		for _, w := range key {
			val = append(val, v[w])
		}
		val = append(val, v[where])
	}

	res, err := db.Conn.Exec(query, val...)
	return res, err
}

// BulkUpdate bulk update query
func (db *Repository) BulkUpdate(table string, where string, data []map[string]interface{}) error {
	var key []string
	query := "UPDATE " + table
	for _, a := range data {
		query += " SET "
		for b := range a {
			if b != where {
				query += b + " = ?, "
				key = append(key, b)
			}
		}
		query = query[:len(query)-2]
		break
	}

	query += " WHERE " + where + " = ?"

	arrval := [][]interface{}{}
	val := make([]interface{}, 0)
	for _, v := range data {
		for _, w := range key {
			val = append(val, v[w])
		}
		val = append(val, v[where])
		arrval = append(arrval, val)
		val = []interface{}{}
	}

	tx, err := db.Conn.Begin()
	if err != nil {
		fmt.Println(err)
		return err
	}

	stt, err := tx.Prepare(query)
	if err != nil {
		fmt.Println(err)
		return err
	}

	for _, k := range arrval {
		_, err := stt.Exec(k...)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

// Deletes func
func (db *Repository) Deletes(table string, where map[string]interface{}) (sql.Result, error) {
	wheres := make([]interface{}, 0)
	query := "DELETE FROM " + table
	if len(where) > 1 {
		for col, val := range where {
			query += " WHERE " + fmt.Sprintf("%v", col) + "=?, "
			wheres = append(wheres, val)
		}
		query = query[:len(query)-2]
	} else {
		for col, val := range where {
			query += " WHERE " + fmt.Sprintf("%v", col) + "=?"
			wheres = append(wheres, val)
		}
	}
	res, err := db.Conn.Exec(query, wheres...)
	return res, err
}

// func getSQLRaw(sql string, params ...interface{}) string {
// 	for i := 0; i < len(params); i++ {
// 		index := strings.Index(sql, "?")
// 		sql = sql[:index] + cast.ToString(params[i]) + sql[index+1:]
// 	}
// 	return sql
// }

// QueryWhereAnd where and
func (db *Repository) QueryWhereAnd(sql string, args []interface{}) ([]map[string]interface{}, error) {
	tableData := make([]map[string]interface{}, 0)
	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		return tableData, err
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			log.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, err
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, err
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	err = rows.Err()
	if err != nil {
		return tableData, err
	}

	return tableData, nil
}
