package db

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

type SqlResult struct {
	Data      interface{}
	Error     error
	SqlQuery  string
	sqlOrigin string
	args      []interface{}
	repo      *Repository
	useDbx    bool
	queryX    QueryX
}

type QueryX struct {
	Sql        string
	Args       []interface{}
	CacheKey   string
	Expiration time.Duration
}

type queryOption interface {
	alterQuery(sql string, args []interface{}) (string, []interface{})
	getData(db *Repository, data interface{}) interface{}
}

func (s *SqlResult) Model(model interface{}) error {
	if s.Error != nil {
		return s.Error
	}

	//return mapstructure.Decode(s.Result[0], model)
	//e, _ := json.Marshal(s.Data)
	//fmt.Println(string(e))

	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if s.useDbx {
		fmt.Println("use dbx...")
		return s.repo.RunQueryX(s.queryX, model).Error
	}

	// cast using library (such be using less resources)
	err := cast.MapToModel(s.Data, model)
	if !log.IfError(err) {
		return err
	} else {
		log.Info("error cast to model, dataType: %v, modelType: %v, err: %v | data: %s", reflect.TypeOf(s.Data),reflect.TypeOf(model),  err,  utils.SimplyToJson(s.Data),)
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return cast.MapToStruct(m, model)
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				//jsonStr, err := json.Marshal(arr)
				//if err != nil {
				//	return err
				//}
				//return json.Unmarshal(jsonStr, model)
				return cast.MapArrayToStruct(arr, model)
			} else {
				return cast.MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("assign data to model err, model is not map")
}

func (s *SqlResult) Map() (map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return m, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr[0], nil
		} else {
			return map[string]interface{}{}, nil
		}
	} else if str, ok := s.Data.(string); ok {
		return cast.ToMap(str), nil
	}
	return nil, fmt.Errorf("model is not single map, %v", reflect.TypeOf(s.Data))
}

func (s *SqlResult) ArrayMap() ([]map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if s.useDbx {
		fmt.Println("use dbx...")
		model := []map[string]interface{}{}
		result := s.repo.RunQueryX(s.queryX, &model)
		return model, result.Error
	}

	if m, ok := s.Data.([]map[string]interface{}); ok {
		return m, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr, nil
		} else {
			return []map[string]interface{}{}, nil
		}
	} else {
		return cast.ToMapArray(s.Data), nil
	}

	// return nil, fmt.Errorf("model is not multi map, %v", reflect.TypeOf(s.Data))
}

func (s *SqlResult) MustJson() string {
	jsonStr, err := json.Marshal(s.Data)
	if err != nil {
		fmt.Println("failed converting to json:", err)
		return ""
	}
	return string(jsonStr)
}

func (s *SqlResult) PrintSql() *SqlResult {
	fmt.Println(s.SqlQuery)
	return s
}

func (s SqlResult) DataTable(draw, length, start int, search string) (map[string]interface{}, error) {
	tableName, err := findTableName(s.sqlOrigin)
	if err != nil {
		return nil, err
	}
	fmt.Println("table name:", tableName)

	//totalFiltered := 0
	if strings.TrimSpace(search) != "" {
		columns := findColumns(s.Data)
		where := ""
		whereArgs := make([]interface{}, 0)
		for i, c := range columns {
			if i != 0 {
				where += " OR "
			}
			where += c + " LIKE ?"
			whereArgs = append(whereArgs, "%"+search+"%")
		}
		fmt.Println(getSQLRaw(where, whereArgs...))

		res := s.repo.Query(s.SqlQuery+" where "+where, whereArgs...)
		if res.Error != nil {
			return nil, res.Error
		}
		fmt.Println("filter : ", res.MustJson()[0:50])
	}

	//count
	sqlCount, args, err := replaceColQuery(s.sqlOrigin, " count(*) as cnt ", s.args)
	if err != nil {
		return nil, err
	}

	resCount, err := s.repo.Query(sqlCount, args...).PrintSql().Map()
	if err != nil {
		return nil, err
	}
	fmt.Println("count: ", resCount)

	dt := map[string]interface{}{
		"draw":            draw,
		"recordsTotal":    cast.ToInt(resCount["cnt"]),
		"recordsFiltered": 0,
		"data":            s.Data,
	}
	return dt, nil
}

func getTotalParam(sql string) int {
	p := `\?`
	re, err := regexp.Compile(p)
	if err != nil {
		fmt.Println("regex err", p)
		return 0
	}
	return len(re.FindAllString(sql, -1))
}

func findWhereIndex(sql string) int {
	p := `(?i)^select .* from [a-z_\s]+.*(where)`
	re, err := regexp.Compile(p)
	if err != nil {
		fmt.Println("regex err", p)
		return 0
	}

	indexes := re.FindAllStringSubmatchIndex(sql, -1)
	if len(indexes) > 0 {
		return indexes[0][2]
	}
	return 0
}

func replaceColQuery(query, replacer string, args []interface{}) (string, []interface{}, error) {
	p := `(?i)^select (.*) from\s+[a-z_]+`
	re, err := regexp.Compile(p)
	if err != nil {
		return "", args, err
	}
	result := re.FindAllStringSubmatchIndex(query, -1)
	if len(result) == 0 {
		return "", args, fmt.Errorf("failed to get column select")
	}

	newQuery := query[0:result[0][2]] + replacer + query[result[0][3]:]

	totalParam := getTotalParam(query[result[0][2]:result[0][3]])
	return newQuery, args[totalParam:], nil
}

func removeLimit(query string) string {
	p := "(?i)limit [0-9]+ offset [0-9]+$"
	re, err := regexp.Compile(p)
	if err != nil {
		fmt.Println("failed to remove LIMIT query: ", err)
		return query
	}

	return re.ReplaceAllString(query, "")
}

func findColumns(data interface{}) []string {
	result := make([]string, 0)
	mapData := make(map[string]interface{})
	if m, ok := data.(map[string]interface{}); ok {
		mapData = m
	} else if arr, ok := data.([]map[string]interface{}); ok && len(arr) > 0 {
		mapData = arr[0]
	}
	for k, _ := range mapData {
		result = append(result, k)
	}
	return result
}

func findTableName(sql string) (string, error) {
	p := `(?i)^select .* from ([a-z_]+)`
	re, err := regexp.Compile(p)
	if err != nil {
		return "", err
	}
	result := re.FindAllStringSubmatch(sql, -1)
	if len(result) == 0 {
		return "", fmt.Errorf("can not find table name")
	}

	if len(result[0]) > 2 {
		fmt.Println("[WARNING] table name return ", len(result[0]))
	}

	return result[0][1], nil
}
