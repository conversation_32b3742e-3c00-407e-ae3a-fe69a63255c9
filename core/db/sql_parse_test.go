package db

import (
	"reflect"
	"testing"
)

func Test_insertBulkQuery(t *testing.T) {
	data := []map[string]interface{}{
		{"id": "1", "name": "andi"},
		{"id": "2", "name": "budi"},
		{"id": "3", "name": "clara"},
	}

	type args struct {
		table string
		data  []map[string]interface{}
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []interface{}
	}{
		{"test1", args{table: "user", data: data}, "INSERT INTO user (id, name) VALUES (?)", []interface{}{1, 2, 3}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := insertBulkQuery(tt.args.table, tt.args.data)
			if got != tt.want {
				t.Errorf("insertBulkQuery() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.<PERSON><PERSON>("insertBulkQuery() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_updateQuery(t *testing.T) {
	dataValues1 := []interface{}{"new item", 10, 1}
	data1 := map[string]interface{}{
		"name": dataValues1[0],
		"qty": map[string]interface{}{
			"+": dataValues1[1],
		},
	}
	query1 := "UPDATE product SET name = ?,qty = qty + ? WHERE id=?"

	query2 := "UPDATE product SET name = ? WHERE id=?"
	dataValues2 := []interface{}{"new name", 1}
	data2 := map[string]interface{}{
		"name": dataValues2[0],
	}

	type args struct {
		table       string
		data        map[string]interface{}
		whereCond   string
		whereParams []interface{}
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []interface{}
	}{
		{"incrementUpdate", args{table: "product", data: data1, whereCond: "id=?", whereParams: []interface{}{1}}, query1, dataValues1},
		{"generalUpdate", args{table: "product", data: data2, whereCond: "id=?", whereParams: []interface{}{1}}, query2, dataValues2},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := updateQuery(tt.args.table, tt.args.data, tt.args.whereCond, tt.args.whereParams...)
			if got != tt.want {
				t.Errorf("updateQuery() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("updateQuery() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
