package db

import (
	"database/sql"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

type MockProduct struct {
	Id    int    `json:"id,omitempty"`
	Name  string `json:"name,omitempty"`
	Price int    `json:"price,omitempty"`
}

func TestRepository_GetSingle(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	sqlQuery := "select * from product"
	var model MockProduct

	productRows := sqlmock.NewRows([]string{"id", "name", "price"}).
		AddRow(1, "test", 100).
		AddRow(2, "test2", 300)

	mock.ExpectQuery("select").WillReturnRows(productRows)

	type fields struct {
		Conn   *sql.DB
		option queryOption
		sql    string
		args   []interface{}
	}
	type args struct {
		model interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{"test1", fields{Conn: db, sql: sqlQuery, args: []interface{}{}}, args{&model}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &Repository{
				Conn:   tt.fields.Conn,
				option: tt.fields.option,
				sql:    tt.fields.sql,
				args:   tt.fields.args,
			}
			if err := db.Get(tt.args.model); (err != nil) != tt.wantErr {
				t.Errorf("Repository.Get() error = %v, wantErr %v", err, tt.wantErr)
			}

			fmt.Println("model: ", utils.SimplyToJson(model), " >> ", model.Name)
		})
	}
}

func TestRepository_GetArray(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	sqlQuery := "select * from product"
	var model []MockProduct

	productRows := sqlmock.NewRows([]string{"id", "name", "price"}).
		AddRow(1, "test", 100).
		AddRow(2, "test2", 300)

	mock.ExpectQuery("select").WillReturnRows(productRows)

	type fields struct {
		Conn   *sql.DB
		option queryOption
		sql    string
		args   []interface{}
	}
	type args struct {
		model interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{"test-array", fields{Conn: db, sql: sqlQuery, args: []interface{}{}}, args{&model}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &Repository{
				Conn:   tt.fields.Conn,
				option: tt.fields.option,
				sql:    tt.fields.sql,
				args:   tt.fields.args,
			}
			if err := db.Get(tt.args.model); (err != nil) != tt.wantErr {
				t.Errorf("Repository.Get() error = %v, wantErr %v", err, tt.wantErr)
			}

			fmt.Println("model: ", utils.SimplyToJson(model), " >> ", model[0].Name)
		})
	}
}
