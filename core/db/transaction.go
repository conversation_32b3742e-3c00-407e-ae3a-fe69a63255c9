package db

import (
	"database/sql"
	"fmt"
	"runtime"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
)

type Transaction interface {
	Insert(table string, data map[string]interface{}) (sql.Result, error)
	InsertBatch(table string, data []map[string]interface{}) (sql.Result, error)
	Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error)
	Delete(table string, whereCond string, whereParams ...interface{}) (sql.Result, error)
}

type TxFn func(Transaction) error

type SqlTx struct {
	Tx *sql.Tx
}

func (s SqlTx) InsertBatch(table string, data []map[string]interface{}) (sql.Result, error) {
	query, values := insertBulkQuery(table, data)
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		// log.Debug("SQL Err: %v \n>>query : %s", err, getSQLRaw(query, values...))
		_, file, no, _ := runtime.Caller(1)
		log.Info("%s:%d >> Query Err (InsertBatch) : %v \n'%s'", file, no, err, query)
		panic(err)
	}
	return resp, err
}

func (s SqlTx) Insert(table string, data map[string]interface{}) (sql.Result, error) {
	// values := make([]interface{}, 0)
	// query := "INSERT INTO " + table + " ("
	// for col, val := range data {
	// 	query += col + ","
	// 	values = append(values, val)
	// }
	// query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	query, values := insertQuery(table, data)
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		// log.Debug("SQL Err: %v \n>>query : %s", err, getSQLRaw(query, values...))
		_, file, no, _ := runtime.Caller(1)
		log.Info("%s:%d >> Query Err Insert : %v \n'%s'", file, no, err, query)
		panic(err)
	}
	return resp, err
}

func (s SqlTx) Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	query, values := updateQuery(table, data, whereCond, whereParams...)
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		// log.Debug("[[ERROR]] query : %s", getSQLRaw(query, whereParams...))
		_, file, no, _ := runtime.Caller(1)
		log.Info("%s:%d >> Query Err (Update) : %v \n'%s'", file, no, err, query)
		panic(err)
	}
	return resp, err
}

func (s SqlTx) Delete(table string, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	query := deleteQuery(table, whereCond, whereParams...)
	resp, err := s.Tx.Exec(query, whereParams...)
	if err != nil {
		// log.Debug("[[ERROR]] query : %s", getSQLRaw(query, whereParams...))
		_, file, no, _ := runtime.Caller(1)
		log.Info("%s:%d >> Query Err (Delete) : %v \n'%s'", file, no, err, query)
		panic(err)
	}
	return resp, err
}

func BeginTrx() (SqlTx, error) {
	tx, err := GetConn().Begin()
	return SqlTx{tx}, err
}

func WithTransaction(fn TxFn) (err error) {
	tx, err := BeginTrx()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			// a panic occurred, rollback and repanic
			log.Info("panic... >> %v", err)
			err = fmt.Errorf("%v", p)
			if errRollback := tx.Tx.Rollback(); errRollback != nil {
				fmt.Println("rollback failed...", errRollback)
			}
			// panic(p)
		} else if err != nil {
			// something went wrong, rollback
			log.Info("rollback...")
			log.IfError(tx.Tx.Rollback())
		} else {
			// all good, commit
			log.IfError(tx.Tx.Commit())
		}
	}()
	err = fn(tx)
	return err
}
