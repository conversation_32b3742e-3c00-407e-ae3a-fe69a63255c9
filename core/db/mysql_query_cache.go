package db

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"time"

	"github.com/goccy/go-json"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/constant"
)

func (db *Repository) QueryCache(cacheKey string, expiration time.Duration, sql string, args ...interface{}) (result *SqlResult) {
	dataCache, err := db.getCache(cacheKey, args...)
	if err == nil && dataCache != nil {
		return &SqlResult{
			Data: dataCache,
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 90*time.Second)
	defer cancel()

	if env := os.Getenv("ENV"); env == "localhostx" || env == "developmentx" {
		result = db.SetQueryX(cacheKey, expiration, sql, args...)
		// result = db.QueryContext(ctx, sql, args...)
	} else {
		result = db.QueryContext(ctx, sql, args...)
	}

	//cache data
	if result.Error == nil {
		// key := defineCacheKey(cacheKey, args...)
		// go db.CacheDb.Set(key, utils.SimplyToJson(result.Data), expiration)
		// log.Info("save cache %v", key)
		go db.saveCache(result.Data, cacheKey, expiration, args...)
	}

	return result
}

func (db *Repository) getCache(cacheKey string, args ...interface{}) (interface{}, error) {
	if db.CacheDb == nil {
		fmt.Println("[WARNING] cache does'nt set for ", cacheKey)
		_, file, no, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%v:%v cache is not initialized", file, no))
		return nil, fmt.Errorf("cache not initialized")
	}

	timeStart := time.Now()
	key := defineCacheKey(cacheKey, args...)
	dataCache, err := db.CacheDb.Get(key)
	log.Info("from cache of %v | size %v | err: %v, took %v", utils.Substr(key, 25), len(dataCache), err, time.Since(timeStart))
	return dataCache, err
}

func (db *Repository) fromCache(model interface{}, cacheKey string, args ...interface{}) error {
	if db.CacheDb == nil {
		fmt.Println("[WARNING] cache does'nt set for ", cacheKey)
		_, file, no, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%v:%v cache is not initialized", file, no))
		return fmt.Errorf("cache not initialized")
	}

	timeStart := time.Now()
	key := defineCacheKey(cacheKey, args...)
	dataCache, err := db.CacheDb.Get(key)
	log.Info("from cache of %v | size %v | err: %v, took %v", utils.Substr(key, 25), len(dataCache), err, time.Since(timeStart))

	if err == nil && len(dataCache) > 0 {
		err := json.Unmarshal([]byte(dataCache), &model)
		// err = msgpack.Unmarshal(dataCache, &model)
		if log.IfError(err) {
			log.Info("error unmarshal cache %v | err: %v | data: %s", utils.Substr(key, 35), err, dataCache)
		}
		return err
	}
	return err
}

func (db *Repository) saveCache(data interface{}, cacheKey string, expiration time.Duration, args ...interface{}) error {
	if db.CacheDb == nil {
		return fmt.Errorf("cache not initialized")
	}
	if data == nil {
		return fmt.Errorf("can not save cache %v data is nil", cacheKey)
	}
	key := defineCacheKey(cacheKey, args...)
	// jsonData, err := msgpack.Marshal(data)
	// jsonData, err := json.Marshal(data)
	// log.IfError(err)
	err := db.CacheDb.Set(key, utils.SimplyToJson(data), expiration)
	if err != nil {
		log.Info("save cache %v | error: %v", utils.Substr(key, 35), err)
	}

	return err
}

func defineCacheKey(cacheKey string, args ...interface{}) string {
	return fmt.Sprintf("crmapi-%s_%s_%s", constant.GetEnv(), cacheKey, utils.ConcatArgs(args))
}
