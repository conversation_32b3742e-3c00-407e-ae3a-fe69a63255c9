package db

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/cloudsqlconn"
	"cloud.google.com/go/cloudsqlconn/mysql/mysql"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/reflectx"
	"github.com/joho/godotenv"
	"github.com/redis/go-redis/v9"
	logger "gitlab.com/uniqdev/backend/api-membership/core/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var db *sql.DB
var dbSqlx *sqlx.DB
var redisClient *redis.Client
var mongoClient *mongo.Client

func init() {
	fmt.Println("init db connection...")
	e := godotenv.Load() //Load .env file
	if e != nil {
		fmt.Print(e)
	}

	dbDrivername, source := getConnectionName()

	var err error
	db, err = sql.Open(dbDrivername, source)
	if err != nil {
		log.Fatalf("Could not open Db: %v", err)
	} else {
		fmt.Println("database open successfully")
	}

	dbSqlx, err = sqlx.Open(dbDrivername, source)
	if err != nil {
		fmt.Println("failed connecting db use sqlx", err)
	} else {
		// Create a new mapper which will use the struct field tag "json" instead of "db"
		dbSqlx.Mapper = reflectx.NewMapperFunc("json", strings.ToLower)
	}

	// read article: https://www.alexedwards.net/blog/configuring-sqldb
	// db.SetMaxIdleConns(5)
	// db.SetMaxOpenConns(5)

	// db.SetMaxOpenConns(7)
	// db.SetMaxIdleConns(7)
	// db.SetConnMaxLifetime(5 * time.Minute)

	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Minute * 5)

	//try to ping
	err = db.Ping()
	if err != nil {
		// log.Fatalf("failed to connect to database: %v", err)
		fmt.Println("PING TO DB ERROR")
	}
}

func getConnectionName() (string, string) {
	username := strings.TrimSpace(os.Getenv("db_user"))
	password := strings.TrimSpace(os.Getenv("db_password"))
	dbName := strings.TrimSpace(os.Getenv("db_name"))
	dbHost := strings.TrimSpace(os.Getenv("db_host"))
	dbComm := strings.TrimSpace(os.Getenv("db_communication"))
	dbPort := strings.TrimSpace(os.Getenv("db_port"))
	if dbComm == "" {
		dbComm = "tcp"
	}
	if dbPort == "" {
		dbPort = "3306"
	}

	fmt.Println("---------------- DB INFO ----------------")
	fmt.Println("database: ", dbName)
	fmt.Println("user    : ", username)
	fmt.Println("host    : ", dbHost)
	fmt.Println("protocol: ", dbComm)
	fmt.Println("-----------------------------------------")

	dbDrivername := "mysql"
	source := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, dbHost, dbPort, dbName)
	if dbComm == "unix" {
		source = fmt.Sprintf("%s:%s@unix(/cloudsql/%s)/%s", username, password, dbHost, dbName)
	} else if dbComm == "proxy" {
		//"myuser:mypass@cloudsql-mysql(project:region:instance)/mydb
		source = fmt.Sprintf("%s:%s@cloudsql-mysql(%s)/%s", username, password, dbHost, dbName)
		cloudSqlDriver := "cloudsql-mysql"
		_, err := mysql.RegisterDriver(cloudSqlDriver, cloudsqlconn.WithCredentialsFile("config/auth/gcloud-service-key.json"))
		if err != nil {
			fmt.Println("register cloudsql-mysql err", err)
		} else {
			dbDrivername = cloudSqlDriver
		}
		// call cleanup when you're done with the database connection
		// defer cleanup()
	}
	return dbDrivername, source
}

func GetConn() *sql.DB {
	return db
}

func GetDbx() *sqlx.DB {
	return dbSqlx
}

func GetRedisClient() *redis.Client {
	if redisClient == nil {
		redisClient = redis.NewClient(&redis.Options{
			Addr:        os.Getenv("REDIS_ENDPOINT"),
			Password:    os.Getenv("REDIS_PASSWORD"),
			Username:    os.Getenv("REDIS_USERNAME"),
			DialTimeout: 10 * time.Second,
			DB:          0, // use default DB
		})
	}

	rs, err := redisClient.Ping(context.Background()).Result()
	fmt.Println("ping redis: ", rs, err)
	if err != nil {
		fmt.Printf("can not ping redis: '%v' \n", os.Getenv("REDIS_ENDPOINT"))
		logger.IfError(fmt.Errorf("can not ping redis at %v", os.Getenv("REDIS_ENDPOINT")))
	}
	return redisClient
}

func GetMongoDbClient() *mongo.Client {
	if mongoClient == nil {
		var err error
		uri := os.Getenv("MONGODB_URI")
		mongoClient, err = mongo.Connect(context.Background(), options.Client().ApplyURI(uri))
		if err != nil {
			fmt.Printf("[MONGODB ERROR] can not connect to mongo %v \n", uri)
		} else {
			fmt.Println("mongodb initialized successfully")
		}
	}

	return mongoClient
}
