#development, staging, production
ENV=development

db_user = user_of_db
db_password = password_of_db
db_name = name_of_db
db_host = ip_of_db

ssl_cert = config/ssl/server.pem
ssl_key = config/ssl/server.key

auth_token = Bearer ycEadPhdwPe7353vgSHwbLqNeCUQ7R5M
behave_user = behave_crm_18236
behave_password = MG2M5wkKQWEvVa6bkpLnYn4vee74gKr2zT3LhdQKETFe2rVfQ9
server = localhost

GCP_PROJECT_ID=uniq-staging
firebase_web_api_key=AIzaSyDa1kpLnYn4vee74gKrxxxxx
FIREBASE_CRM_KEY=AIxxxxxxxxxxxxx

disable_cron=true

#list of services
api-messeger=https://apimesseger.uniq.id
api-crm=https://api-crm.uniq.id
API-BILLING=https://api-biling.uniq.id
API-NLP=