ERROR CODE


## ORDER
100         --- order id not match (ex must start with 'CRM')      
101         --- transaction id not found in database. transaction id sent by midtrans to our server when client process the payment.      
102         --- order list (product) is empty     

## MEMBER / AUTH
50          --- member not found     
51          --- member status is no active     
52          --- member status is banned     
53          --- user not registered at this business    
54          --- demo account (used for Google Play team)
55          --- sending otp exeeded rate limit 

## DEALS
70          --- deals not found     
71          --- point member not enough     
72          --- deals not active on this day     
73          --- transaction_id is not valid (might be not registered in system or already used)     
74          --- invalid amount of payment (user transfer less than supposed to be)      
75          --- exceed maximum redeem limit per member      


## REGISTER
30          --- user already registered at current business/admin      
31          --- user already registered at one of business/admin    
32          --- email already used/registered      
33          --- phone already used/registered   
34          --- invalid email address    


## TRANSACION
80          --- insufficient stock