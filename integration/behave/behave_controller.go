package behave

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"os"
	"strings"
	"sync"
	"time"
)

func WelcomeBehave(ctx *fasthttp.RequestCtx) {
	fmt.Fprint(ctx, "website unavailable")
}

func RequestBehaveToken(ctx *fasthttp.RequestCtx) {
	user := string(ctx.PostArgs().Peek("client_id"))
	pass := string(ctx.PostArgs().Peek("client_secret"))

	log.Info("behave request authentication token...")

	if user == os.Getenv("behave_user") && pass == os.Getenv("behave_password") {
		token := auth.InitJWTAuth().GenerateBehaveToken()
		ctx.SetContentType("application/json")
		_ = json.NewEncoder(ctx).Encode(token)
		log.Info("request behave token success...")
		return
	}

	log.Warn("request behave token failed... Unauthorized")
	ctx.Response.Header.Set("WWW-Authenticate", "Basic realm=Restricted")
	ctx.Error(fasthttp.StatusMessage(fasthttp.StatusUnauthorized), fasthttp.StatusUnauthorized)
}

func BehaveOrder(ctx *fasthttp.RequestCtx) {
	log.Info("New Order from behave : %s", string(ctx.PostBody()))
	var order models.BehaveOrder
	err := json.Unmarshal(ctx.PostBody(), &order)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 100, Message: "invalid post request body!"})
		return
	}

	tgl, _ := utils.MillisToDateTime(time.Now().Unix() + 25200)

	//validation
	//#1.validate store code
	storeCode := utils.ToInt(order.StoreCode)
	outlet, err := db.Query("SELECT outlet_id, admin_fkid FROM outlets WHERE outlet_id = ?", storeCode)
	log.IfError(err)

	if len(outlet) == 0 {
		log.Warn("store code not registered...")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 101, Message: "store code not registered"})
		return
	}

	//#2. make sure payment is sent
	if len(order.Payments) == 0 {
		log.Warn("invalid request - payment not sent")
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "payment is required"})
		return
	}

	//#3. at least 1 menu/item should sent
	if len(order.Menu) == 0 {
		log.Warn("invalid request - no item sent")
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "menu is required"})
		return
	}

	//check if store is open - in doing so find open shift id
	shift, err := db.Query("SELECT open_shift_id, shift_fkid from open_shift where outlet_fkid = ? and time_close is null", storeCode)
	utils.CheckErr(err)

	if len(shift) == 0 {
		//log.Warn("store is closed")
		//ctx.SetStatusCode(fasthttp.StatusForbidden)
		//_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 103, Message: "store is closed!"})
		//return
		log.Warn("store is closed... but continue")
	}

	//finding product detail Id
	productIds := make(map[string]interface{})
	var wg sync.WaitGroup
	for _, product := range order.Menu {
		wg.Add(1)
		id := product.PluID

		go func(productId string) {
			defer wg.Done()
			data, err := db.Query("SELECT product_detail_id from products_detail where product_fkid = ? and outlet_fkid = ?", productId, storeCode)
			if !utils.CheckErr(err) {
				productIds[productId] = data["product_detail_id"]
			}
			fmt.Println(productId, data)
		}(id)
	}

	//get bank id
	bankIds := make(map[string]interface{})
	for _, payment := range order.Payments {
		if strings.ToLower(payment.Type) != "cash" {
			//if name is null, take from type
			if payment.Name == "" {
				payment.Name = payment.Type
			}

			//force payment to App Order
			payment.Name = "App Order"

			wg.Add(1)
			go func(bankName string) {
				defer wg.Done()
				bank, err := db.Query("select bank_id from payment_media_bank where name = ? and admin_fkid = ?", bankName, outlet["admin_fkid"])
				log.IfError(err)
				if len(bank) == 0 {
					resp, err := db.Insert("payment_media_bank", map[string]interface{}{
						"name":          bankName,
						"no_rekening":   0,
						"admin_fkid":    outlet["admin_fkid"],
						"data_created":  tgl,
						"data_modified": tgl,
						"data_status":   "on",
					})
					log.IfError(err)
					id, _ := resp.LastInsertId()
					bankIds[bankName] = id
				} else {
					bankIds[bankName] = bank["bank_id"]
				}
			}(payment.Name)
		}
	}

	wg.Wait()

	notFound := ""
	for pluId, productDetailId := range productIds {
		if productDetailId == nil {
			notFound += pluId + ","
		}
	}

	if notFound != "" {
		log.Warn("invalid request - these product ids not found : %s", notFound)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 102, Message: "these product ids not found : " + notFound})
		return
	}

	taxes := make([]map[string]interface{}, 0)
	if order.Tax > 0 || order.Service > 0 {
		sql := `select gratuity_id,name,tax_category from gratuity where ((tax_category = 'service' AND name = 'service') OR (tax_category = 'tax' AND name = 'tax')) and admin_fkid = ?`
		taxOrServices, err := db.QueryArray(sql, outlet["admin_fkid"])
		if utils.CheckError(ctx, err) {
			return
		}

		isTaxFound := false
		isServiceFound := false
		for _, taxService := range taxOrServices {
			if order.Tax > 0 && strings.ToLower(utils.ToString(taxService["name"])) == "tax" && taxService["tax_category"] == "tax" && !isTaxFound {
				isTaxFound = true
				taxes = append(taxes, map[string]interface{}{
					"name":     "Tax",
					"id":       taxService["gratuity_id"],
					"total":    order.Tax,
					"type":     "nominal",
					"value":    order.Tax,
					"category": "tax",
				})
			}

			if order.Service > 0 && strings.ToLower(utils.ToString(taxService["name"])) == "service" && taxService["tax_category"] == "service" && !isServiceFound {
				isServiceFound = true
				taxes = append(taxes, map[string]interface{}{
					"name":     "Service",
					"id":       taxService["gratuity_id"],
					"total":    order.Service,
					"type":     "nominal",
					"value":    order.Service,
					"category": "service",
				})
			}

			if ((order.Service > 0 && isServiceFound) || order.Service == 0) && ((order.Tax > 0 && isTaxFound) || order.Tax == 0) {
				fmt.Println("break found all")
				break
			}
		}

		fmt.Println("service: ", order.Service, "isFound : ", isServiceFound, "--tax: ", order.Tax, "isFound: ", isTaxFound)

		if order.Service > 0 && !isServiceFound {
			resp, err := db.Insert("gratuity", map[string]interface{}{
				"name":          "Service",
				"tax_category":  "service",
				"tax_status":    "temp_deactive",
				"tax_type":      "nominal",
				"jumlah":        0,
				"admin_fkid":    outlet["admin_fkid"],
				"data_created":  time.Now().Unix() * 1000,
				"data_modified": time.Now().Unix() * 1000,
				"data_status":   "on",
			})
			if utils.CheckError(ctx, err) {
				return
			}

			serviceId, err := resp.LastInsertId()
			utils.CheckErr(err)
			taxes = append(taxes, map[string]interface{}{
				"name":     "Service",
				"id":       serviceId,
				"total":    order.Service,
				"type":     "nominal",
				"value":    order.Service,
				"category": "service",
			})
		}

		if order.Tax > 0 && !isTaxFound {
			resp, err := db.Insert("gratuity", map[string]interface{}{
				"name":          "Tax",
				"tax_category":  "tax",
				"tax_status":    "temp_deactive",
				"tax_type":      "nominal",
				"admin_fkid":    outlet["admin_fkid"],
				"jumlah":        0,
				"data_created":  time.Now().Unix() * 1000,
				"data_modified": time.Now().Unix() * 1000,
				"data_status":   "on",
			})
			if utils.CheckError(ctx, err) {
				return
			}

			serviceId, err := resp.LastInsertId()
			utils.CheckErr(err)
			taxes = append(taxes, map[string]interface{}{
				"name":     "Tax",
				"id":       serviceId,
				"total":    order.Service,
				"type":     "nominal",
				"value":    order.Service,
				"category": "tax",
			})
		}
	}

	sales := map[string]interface{}{
		"sales_id":        order.TrxID,
		"noNota":          order.TrxID,
		"displayNota":     order.TrxID,
		"payment":         "CARD",
		"customer":        order.Member.Name,
		"customersQty":    1,
		"data_status":     "on",
		"outletFkid":      storeCode,
		"status":          "Success",
		"open_shift_fkid": shift["open_shift_id"],
		"outletID":        storeCode,
		"date_created":    tgl,
		"date_modified":   tgl,
		"timeCreated":     time.Now().Unix() * 1000,
		"timeModified":    time.Now().Unix() * 1000,
		"grandTotal":      order.GrandTotal,
		"display_nota":    order.TrxID,
		"table":           "",
		"taxes":           taxes,
		"discount": map[string]interface{}{
			"discount":        order.Discount,
			"discountNominal": order.Discount,
			"discount_info":   "-",
		},
	}

	//save to sales
	//_, err = db.Insert("sales", sales)
	//if utils.CheckErr(err) {
	//	ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	//	return
	//}

	payments := make([]map[string]interface{}, 0)
	products := make([]map[string]interface{}, 0)
	menuNames := ""
	for _, menu := range order.Menu {
		menuNames += menu.Name + ", "
		product := map[string]interface{}{
			"sales_fkid":          order.TrxID,
			"time_created":        time.Now().Unix() * 1000,
			"product_fkid":        menu.PluID,
			"product_detail_fkid": productIds[menu.PluID],
			"qty":                 menu.Qty,
			"price":               menu.Price,
			"subTotal":            menu.Price * menu.Qty,
			"tmpId":               time.Now().UnixNano() / 1000000,
			"employee_fkid":       1,
			"product": map[string]interface{}{
				"name":              menu.Name,
				"product_detail_id": productIds[menu.PluID],
				"product_id":        menu.PluID,
			},
		}

		products = append(products, product)
		//_, err = db.Insert("sales_detail", product)
		//utils.CheckErr(err)
	}

	for _, payment := range order.Payments {
		pay := map[string]interface{}{
			"sales_fkid":   order.TrxID,
			"method":       "CARD",
			"total":        payment.Nominal,
			"pay":          payment.Nominal,
			"time_created": time.Now().Unix() * 1000,
			"bank_fkid":    bankIds[payment.Name],
			"bank": map[string]interface{}{
				"name":           payment.Name,
				"bank_id":        bankIds[payment.Name],
				"account_number": "",
			},
		}

		payments = append(payments, pay)
		//resp, err := db.Insert("sales_payment", pay)
		//utils.CheckErr(err)

		//id, _ := resp.LastInsertId()
		//_, err = db.Insert("sales_payment_bank", map[string]interface{}{
		//	"sales_payment_fkid": id,
		//	"bank_fkid":          bankIds[payment.Name],
		//	"account_number":     "",
		//})
		//utils.CheckErr(err)
	}

	pickupTime, err := time.Parse("2006-01-02 15:04:05", "2018-12-15 16:41:34")
	utils.CheckErr(err)

	sales["payments"] = payments
	sales["orderList"] = products
	sales["order_id"] = order.OrderId
	orderSalesId := fmt.Sprintf("%s%v", utils.BEHAVE_ORDER_PREFIX, order.TrxID)
	items, _ := json.Marshal(sales)
	_, err = db.Insert("order_sales", map[string]interface{}{
		"order_sales_id": orderSalesId,
		"order_type":     "pickup",
		"pickup_time":    (pickupTime.Unix() - 25200) * 1000,
		"time_order":     time.Now().Unix() * 1000,
		"time_modified":  time.Now().UnixNano() / 1000000,
		"status":         "pending",
		"outlet_fkid":    storeCode,
		"items":          string(items),
	})

	//error case :
	//duplicate primary key

	if err != nil {
		data, err := db.Query("select order_sales_id from order_sales where order_sales_id = ?", orderSalesId)
		log.IfError(err)

		if len(data) == 0 {
			log.Warn("duplicate transaction id of : %s", orderSalesId)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "duplicate transaction id"})
			return
		}
	}

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	result := map[string]interface{}{
		"order_id": order.TrxID,
	}

	log.Info("order with trxId %s success", order.TrxID)

	//send notif
	go sendNotifToCashier(storeCode, fmt.Sprintf("Item: %s", menuNames))

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func sendNotifToCashier(storeId int, message string) {
	devices, err := db.QueryArray("SELECT firebase_token FROM devices WHERE outlet_fkid = ? AND firebase_token IS NOT NULL AND device_status='on'", storeId)
	if utils.CheckErr(err) {
		return
	}

	regIds := make([]string, 0)
	for _, device := range devices {
		regIds = append(regIds, utils.ToString(device["firebase_token"]))
	}

	notifData := map[string]interface{}{
		"registration_ids": regIds,
		"data": map[string]interface{}{
			"message": message,
			"title":   "PESANAN BARU",
			"type":    "order_sales",
		},
	}

	req := utils.HttpRequest{
		Method: "POST",
		Url:    "https://fcm.googleapis.com/fcm/send",
		Header: map[string]interface{}{
			"Authorization": "key = AIzaSyBB1XqHd1OLQYrkrf3p2gSUZZxJQitmCmg",
			"Content-Type":  "application/json",
			"Accept":        "application/json",
		},
	}
	req.PostRequest.Body = notifData
	resp, err := req.ExecuteRequest()
	utils.CheckErr(err)
	fmt.Println("Send Notif Resp : ", string(resp))
}
