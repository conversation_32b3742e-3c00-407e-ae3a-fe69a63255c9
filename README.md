# API CRM

### Generate Mocking Files    
use **mockery** to generate mock files

```shell
mockery --all --dir module/transaction  --inpackage --output module/transaction/mocks
```

mockery --all --dir module  --inpackage --output mocks

## Profiling          
**OPTION 1**      
Run the app, then open in terminal:      
```
go tool pprof http://localhost:1619/debug/pprof/heap
```    
     
**OPTION 2**   
or you can create pprof file, to analyze it later     
```
curl --output myappprofile "localhost:1619/debug/pprof/profile?seconds=20"
```     

available profiles:      
 - profile
 - heap
 - symbol 
 - trace
 - cmdline    


 to analyze from terminal:     
 ```
 go tool pprof myappprofile
 ```   

 to analyze from web ui:      
 ```
go tool pprof -http localhost:3435 myappprofile
 ```     

 *note :*     
 you need to install graphviz in order to analyze with web ui