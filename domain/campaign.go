package domain

// enum('email','whatsapp','sms','push_notif')
const (
	CampaignMediaWa        = "whatsapp"
	CampaignMediaEmail     = "email"
	CampaignMediaPushNotif = "push_notification"
)

type Campaign struct {
	CampaignId     int            `json:"campaign_id,omitempty"`
	Name           string         `json:"name,omitempty"`
	DateSend       string         `json:"date_send,omitempty"`
	TimeSend       string         `json:"time_send,omitempty"`
	Title          string         `json:"title,omitempty"`
	Content        string         `json:"content,omitempty"`
	Member         []Member       `json:"member,omitempty"`
	Role           Role           `json:"role,omitempty"`
	DateTimeMillis int64          `json:"date_time_millis,omitempty"`
	Manual         CampaignManual `json:"manual,omitempty"`
}

type Member struct {
	MemberID int      `json:"member_id"`
	Media    []string `json:"media"`
}

type CampaignManual struct {
	Media   string   `json:"media,omitempty"`
	Address []string `json:"address,omitempty"`
}

type RoleDetail struct {
	Condition           string `json:"condition"`
	ConditionValue      string `json:"condition_value"`
	InputConditionValue string `json:"input_condition_value"`
}

type Role struct {
	Operator []string     `json:"operator"`
	Detail   []RoleDetail `json:"detail"`
}
