package domain

const (
	PointCollectionActionRegister = "register"
	PointCollectionActionFeedback = "feedback"
)

type PointCollectionEntity struct {
	PointCollectionID int    `json:"point_collection_id,omitempty"`
	Name              string `json:"name,omitempty"`
	PointType         string `json:"point_type,omitempty"`
	Point             int    `json:"point,omitempty"`
	MemberTypeFkid    int    `json:"member_type_fkid,omitempty"`
	MinTransaction    int    `json:"min_transaction,omitempty"`
	DateStart         int64  `json:"date_start,omitempty"`
	DateEnd           int64  `json:"date_end,omitempty"`
	TimeStart         string `json:"time_start,omitempty"`
	TimeEnd           string `json:"time_end,omitempty"`
	DaySunday         int    `json:"day_sunday,omitempty"`
	DayMonday         int    `json:"day_monday,omitempty"`
	DayTuesday        int    `json:"day_tuesday,omitempty"`
	DayWednesday      int    `json:"day_wednesday,omitempty"`
	DayThursday       int    `json:"day_thursday,omitempty"`
	DayFriday         int    `json:"day_friday,omitempty"`
	DaySaturday       int    `json:"day_saturday,omitempty"`
	AdminFkid         int    `json:"admin_fkid,omitempty"`
	DataCreated       int64  `json:"data_created,omitempty"`
	DataModified      int64  `json:"data_modified,omitempty"`
	DataStatus        int    `json:"data_status,omitempty"`
}

type PointCollectionFilter struct {
	PointType       string `json:"point_type,omitempty"`
	ParentPointType string `json:"parent_point_type,omitempty"`
	MemberTypeId    string `json:"member_type_id,omitempty"`
	OutletId        string `json:"outlet_id,omitempty"`
	Active          string `json:"active,omitempty"`
}

type PointCollectionRunner struct {
	AdminId  int64
	MemberId int64
	Action   string
	ActionId interface{}
}
