package domain

import "gitlab.com/uniqdev/backend/api-membership/models"

type Product struct{}

type Subcategory struct {
	Name                 string `json:"name,omitempty"`
	Position             int    `json:"position,omitempty"`
	ProductSubcategoryId int    `json:"product_subcategory_id,omitempty"`
}

type ProductRequest struct {
	ProductIds []int    `json:"product_ids,omitempty"`
	Includes   []string `json:"includes,omitempty"`
	OutletId   int      `json:"outlet_id,omitempty"`
}

type ProductResponse struct {
}

type ProductSubcategoryRequest struct {
	OutetIds []int `json:"outet_ids,omitempty"`
}

type ProductLinkMenuRequest struct {
	OutletId        int `json:"outlet_id,omitempty"`
	ProductDetailId int `json:"product_detail_id,omitempty"`
}

type UnitConvertinRequest struct {
}

type UnitConversionResponse struct {
	models.ProductUnitConversionEntity
	models.UnitEntity
}

type ProductAvailableFilter struct {
	AdminId int
	Day     string
}

type ProductGratuityFilter struct {
	GratuityId []int
}

type ProductImprovedRequest struct {
	TimeOffset int
	MemberId   int
	AdminId    int
	ProductId  int
}

type GratuityRequest struct {
	TaxStatus string
	OutletId  int
}

func (g *GratuityRequest) ToGratuityFilter() *GratuityFilter {
	return &GratuityFilter{
		TaxStatuses: []string{g.TaxStatus},
	}
}
