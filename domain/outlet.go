package domain

import (
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type Outlet struct{}

type OutletResponse struct {
	Name      string  `json:"name,omitempty"`
	Address   string  `json:"address,omitempty"`
	City      string  `json:"city,omitempty"`
	Country   string  `json:"country,omitempty"`
	OutletID  int     `json:"outlet_id,omitempty"` // Assuming outlet_id is an integer type in the database
	Phone     string  `json:"phone,omitempty"`
	Province  string  `json:"province,omitempty"`
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
}

type OutletDistanceRequest struct {
	OutletId  int
	Latitude  float64
	Longitude float64
}

type OutletDeliveryPriceRequest struct {
	OutletDistanceRequest
}

func (o OutletResponse) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"name":      o.Name,
		"address":   o.Address,
		"city":      o.City,
		"country":   o.Country,
		"outlet_id": o.OutletID,
		"phone":     o.Phone,
		"province":  o.Province,
		"latitude":  o.Latitude,
		"longitude": o.Longitude,
	}
}

type OutletUseCase interface {
	FetchOutlet(user UserSession, latLng string) ([]map[string]interface{}, error)
	FetchOutletByIds(outletIds ...int) ([]map[string]interface{}, error)

	GetOutletDistance(request OutletDistanceRequest, user UserSession) (map[string]interface{}, error)
	GetDeliveryPrice(request OutletDeliveryPriceRequest, user UserSession) (map[string]interface{}, error)

	FetchBanner(user UserSession) ([]map[string]interface{}, error)
}

type OutletRepository interface {
	FetchOutlet(adminId int) ([]map[string]interface{}, error)
	FetchOutletByIds(outletIds ...int) ([]OutletResponse, error)
	FetchWorkingHours(outletIds ...int) (*[]models.WorkingHourEntity, error)
	FetchOrderTypes(outletIds ...int) ([]map[string]interface{}, error)
	FetchOutletName(ids []int) (*[]models.OutletEntity, error)
	FetchBanner(adminId int) ([]map[string]interface{}, error)
	FetchDealsAsBanner(adminId int64) ([]map[string]interface{}, error)
}
