package domain

type User struct{}

type UserAddres struct {
	Label     string  `json:"label,omitempty"`
	Province  string  `json:"province,omitempty"`
	City      string  `json:"city,omitempty"`
	Address   string  `json:"address,omitempty"`
	Phone     string  `json:"phone,omitempty"`
	Latitute  float64 `json:"latitute,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
}

type UserRequestParam struct {
	AccessType []string
}

type RegisterRequest struct {
	Email          string `json:"email,omitempty"`
	Name           string `json:"name,omitempty"`
	Gender         int    `json:"gender,omitempty"`
	DateOfBirth    int64  `json:"date_of_birth,omitempty"`
	Province       string `json:"province,omitempty"`
	City           string `json:"city,omitempty"`
	Address        string `json:"address,omitempty"`
	AdditionalData string `json:"additional_data,omitempty"`
	Phone          string `json:"phone,omitempty"`
	IdToken        string `json:"id_token,omitempty"`
	PhoneVerified  int    `json:"phone_verified,omitempty"`
}

type MemberFilter struct {
	Phone string
	Email string
}
