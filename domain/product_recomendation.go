package domain

type RecommendationRequest struct {
	OutletId   int   `json:"outlet_id"`
	ProductId  int   `json:"product_id"`
	ProductIds []int `json:"product_ids"`
	MemberId   int   `json:"member_id"`
}

type RecommendationResponse struct {
	ProductId    int    `json:"product_id"`
	ProductName  string `json:"product_name"`
	ProductImage string `json:"product_image"`
}

type ProductRecommendation struct {
	ProductId   int    `json:"product_id"`
	ProductName string `json:"product_name"`
}

func (p *ProductRecommendation) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"product_id":   p.ProductId,
		"product_name": p.ProductName,
	}
}
