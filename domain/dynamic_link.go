package domain

type DynamicLinkRequest struct {
	DynamicLinkInfo DynamicLinkInfo `json:"dynamicLinkInfo"`
	Suffix          Suffix          `json:"suffix"`
}
type AndroidInfo struct {
	AndroidPackageName  string `json:"androidPackageName,omitempty"`
	AndroidFallbackLink string `json:"android_fallback_link,omitempty"`
}
type IosInfo struct {
	IosBundleID string `json:"iosBundleId"`
}
type DynamicLinkInfo struct {
	AndroidInfo     AndroidInfo `json:"androidInfo"`
	DomainURIPrefix string      `json:"domainUriPrefix"`
	IosInfo         IosInfo     `json:"iosInfo"`
	Link            string      `json:"link"`
}
type Suffix struct {
	Option string `json:"option"`
}
