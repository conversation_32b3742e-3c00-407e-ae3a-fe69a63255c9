package domain

type Cart struct{}

type CartRequestQuery struct {
	OutletId        string `json:"outlet_id"`
	ProductDetailId int
}

type CartUpdateBody struct {
	ProductDetailId int64 `json:"product_detail_id,omitempty"`
	Qty             int   `json:"qty,omitempty"`
}

type CartUseCase interface {
	FetchCart(userSession UserSession, param CartRequestQuery) ([]map[string]interface{}, error)
	AddCart(productDetailId int, userSession UserSession) error
	RemoveCart(productDetailIds []int, userSession UserSession) error
	UpdateCart(productDetailId, qty int, userSession UserSession) error
	UpdateCartBatch(body []CartUpdateBody, user UserSession) error
	CountCart(userSession UserSession) (map[string]interface{}, error)
}

type CartRepository interface {
	FetchCart(userSession UserSession, param CartRequestQuery) ([]map[string]interface{}, error)
	AddCart(productDetailId, qty int, userSession UserSession) error
	RemoveCart(productDetailIds []int, userSession UserSession) (int64, error)
	UpdateCart(productDetailId, qty int, userSession UserSession) error
	CountCart(userSession UserSession) (map[string]interface{}, error)
	//FetchProductByIds(productDetailIds []interface{}) ([]map[string]interface{}, error)
}
