package domain

const (
	OrderStatusAccept = "accept"
	OrderStatusCancel = "cancel"
)

type TimeoutOrderFilter struct {
	AdminId          int
	TimeoutThreshold int      //timeout in minute
	Status           []string //list of order status
}

type TimeoutOrderResponse struct {
	OrderSalesID  string `json:"order_sales_id,omitempty"`
	ID            int    `json:"id,omitempty"`
	DiffMinutes   string `json:"diff_minutes,omitempty"`
	AdminFkid     int    `json:"admin_fkid,omitempty"`
	FirebaseToken string `json:"firebase_token,omitempty"`
	MemberFkid    int    `json:"member_fkid,omitempty"`
	Status        string `json:"status,omitempty"`
}

type OrderSalesUpdateData struct {
	OrderSalesId string
	Status       string
	Message      string
	AdminId      int
}
