package domain

type Gallery struct {
	Caption     string   `json:"caption,omitempty"`
	Detail      []Detail `json:"detail,omitempty"`
	Name        string   `json:"name,omitempty"`
	TimeCreated int      `json:"time_created,omitempty"`
	Categories  []string `json:"categories,omitempty"`
	CategoryIds []int64  `json:"category_ids,omitempty"`
}

type Tags struct {
	PositionX float64 `json:"position_x"`
	PositionY float64 `json:"position_y"`
	Type      string  `json:"type"`
	Value     string  `json:"value"`
}

type Detail struct {
	ImageURL string `json:"image_url"`
	ImageId  string `json:"image_id"`
	Tags     []Tags `json:"tags"`
}

type GalleryCategory struct {
	Name string
	Id   int64
}

type GalleryRepositoryFilter struct {
	Categories  []string `json:"categories,omitempty"`
	CategoryIds []int
	Active      bool `json:"active,omitempty"`
}

type GalleryUseCase interface {
	FetchGallery(galleryId int, user UserSession, page int, search string, categoryIds []int) ([]map[string]interface{}, error)
	AddBookmark(galleryId int, userSession UserSession) error
	RemoveBookmark(galleryId int, userSession UserSession) error
	AddGallery(input Gallery, userSession UserSession) error
	UpdateGallery(galleryId int, input Gallery, userSession UserSession) error
	RemoveGallery(galleryId int, userSession UserSession) error
	FetchBookmark(userSession UserSession) ([]map[string]interface{}, error)
	FetchBookmarkTopItem(userSession UserSession) ([]map[string]interface{}, error)
	FetchGalleryCategory(query GalleryRepositoryFilter, user UserSession) ([]map[string]interface{}, error)
	UpdateGalleryCategory(id int, name string, user UserSession) error
	DeleteGalleryCategory(id int, user UserSession) error
	AddGalleryCategory(categoryName string, user UserSession) error
}

type GalleryRepository interface {
	Filter(filter GalleryRepositoryFilter) GalleryRepository
	FetchGallery(galleryId int, adminId int64, page int, search string, categoryIds []int) ([]map[string]interface{}, error)
	AddBookmark(galleryId int, userSession UserSession) error
	RemoveBookmark(galleryId int, session UserSession) error
	AddGallery(input Gallery, session UserSession) error
	RemoveGallery(galleryId int, adminId int64) error
	FetchProductByIds(productIds []int, user UserSession) ([]map[string]interface{}, error)
	FetchBookmark(userSession UserSession) ([]map[string]interface{}, error)
	FetchMemberAccess(userSession UserSession) (map[string]interface{}, error)
	FetchBookmarkTopItem(user UserSession) ([]map[string]interface{}, error)
	FetchBookmarkByGalleryIds(galleryIds []int, userSession UserSession) ([]map[string]interface{}, error)
	UpdateGallery(galleryId int, input Gallery, adminId int64) error
	FetchGalleryCategory(adminId int64) ([]map[string]interface{}, error)
	AddGalleryCategory(categoryName string, adminId int64) (int64, error)
	UpdateGalleryCategory(id int, name string, user UserSession) error
	DeleteGalleryCategory(id int, user UserSession) error
}
