package domain

import (
	"encoding/json"
	"fmt"
	"runtime"
)

type App struct{}

type CrmApp struct {
	CrmAppID      int    `json:"crm_app_id"`
	AdminFkid     int    `json:"admin_fkid"`
	AppInfo       string `json:"app_info"`
	AppConfig     string `json:"app_config"`
	PrivacyPolicy string `json:"privacy_policy"`
	TermCondition string `json:"term_condition"`
	DataCreated   int64  `json:"data_created"`
	DataModified  int64  `json:"data_modified"`
}

type CrmAppInfo struct {
	Ios     Ios     `json:"ios,omitempty"`
	Android Android `json:"android,omitempty"`
	Web     Web     `json:"web,omitempty"`
}
type Ios struct {
	BundleID   string `json:"bundle_id,omitempty"`
	AppStoreId string `json:"app_store_id,omitempty"`
}
type Android struct {
	PackageName string `json:"package_name"`
}

type Web struct {
	Url string `json:"url,omitempty"`
}

func (c CrmApp) ToAppInfo() CrmAppInfo {
	var crmAppInfo CrmAppInfo
	if c.AppInfo == "" {
		return crmAppInfo
	}

	err := json.Unmarshal([]byte(c.AppInfo), &crmAppInfo)
	if err != nil {
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%v can not convert json: %v ", file, line, err)
	}
	return crmAppInfo
}

type AppUseCase interface {
	FetchAppConfig(user UserSession) (map[string]interface{}, error)
	FetchFAQs(user UserSession) ([]map[string]interface{}, error)
}
type AppRepository interface {
	FetchAppInfoAndConfig(user UserSession) (CrmApp, error)
	FetchFAQs(adminID int) ([]map[string]interface{}, error)
}
