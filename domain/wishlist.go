package domain

type Wishlist struct{}
type WishlistUseCase interface {
	FetchWishList(userSession UserSession, format string) ([]map[string]interface{}, error)
	AddWishList(productId int, user UserSession) (map[string]interface{}, error)
	RemoveWishList(wishListId int, user UserSession) error
	UpdateWishListCategory(id int, categoryId int64, user UserSession) error
	CountWishList(userSession UserSession) (map[string]interface{}, error)
	FetchWishListTopItem(userSession UserSession) ([]map[string]interface{}, error)

	FetchWishListCategory(user UserSession) ([]map[string]interface{}, error)
	AddWishListCategory(name string, user UserSession) (map[string]interface{}, error)
}
type WishlistRepository interface {
	FetchWishList(userSession UserSession) ([]map[string]interface{}, error)
	AddWishList(productId int, user UserSession) (int64, error)
	RemoveWishList(wishListId int, user UserSession) error
	CountWishList(userSession UserSession) (map[string]interface{}, error)
	FetchWishListTopItem(userSession UserSession) ([]map[string]interface{}, error)
	UpdateWishListCategory(id int, categoryId int64, user UserSession) error

	AddWishListCategory(name string, user UserSession) (int64, error)
	FetchWishListCategory(user UserSession) ([]map[string]interface{}, error)
}
