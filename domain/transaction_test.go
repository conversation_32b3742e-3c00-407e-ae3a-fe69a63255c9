package domain

import (
	"encoding/json"
	"reflect"
	"testing"

	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func TestTransaction_ToSalesModel(t *testing.T) {
	type fields struct {
		OrderList []OrderList `json:"orderList,omitempty"`
	}

	order1Str := `{"orderList": [{"qty": 1,"product_detail_fkid": 15700, "product_detail_id": 15700, "price": 0, "product":{"product_detail_id":15700}}]}`
	var order1 fields
	err := json.Unmarshal([]byte(order1Str), &order1)
	if err != nil {
		t.Error(err)
	}

	var sales1 models.Sales
	err = json.Unmarshal([]byte(order1Str), &sales1)
	if err != nil {
		t.Error(err)
	}

	tests := []struct {
		name   string
		fields fields
		want   models.Sales
	}{
		{"test1", order1, sales1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := Transaction{
				OrderList: tt.fields.OrderList,
			}
			if got := tr.ToSalesModel(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Transaction.ToSalesModel() = %v, want %v", utils.SimplyToJson(got.OrderList), utils.SimplyToJson(tt.want.OrderList))
			}
		})
	}
}
