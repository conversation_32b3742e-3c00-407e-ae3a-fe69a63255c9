package main

import (
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"cloud.google.com/go/profiler"
	_ "github.com/joho/godotenv/autoload"
	"github.com/pkg/profile"
	"github.com/valyala/fasthttp"
	"github.com/valyala/fasthttp/pprofhandler"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/job"
	_ "gitlab.com/uniqdev/backend/api-membership/job"
	httpApp "gitlab.com/uniqdev/backend/api-membership/module/app/delivery/http"
	mysqlApp "gitlab.com/uniqdev/backend/api-membership/module/app/repository/mysql"
	usecaseApp "gitlab.com/uniqdev/backend/api-membership/module/app/usecase"
	httpAuth "gitlab.com/uniqdev/backend/api-membership/module/auth/delivery/http"
	mysqlAuth "gitlab.com/uniqdev/backend/api-membership/module/auth/repository/mysql"
	usecaseAuth "gitlab.com/uniqdev/backend/api-membership/module/auth/usecase"
	"gitlab.com/uniqdev/backend/api-membership/module/cache"
	httpCampaign "gitlab.com/uniqdev/backend/api-membership/module/campaign/delivery/http"
	mysqlCampaign "gitlab.com/uniqdev/backend/api-membership/module/campaign/repository/mysql"
	usecaseCampaign "gitlab.com/uniqdev/backend/api-membership/module/campaign/usecase"
	httpCart "gitlab.com/uniqdev/backend/api-membership/module/cart/delivery/http"
	mysqlCart "gitlab.com/uniqdev/backend/api-membership/module/cart/repository/mysql"
	usecaseCart "gitlab.com/uniqdev/backend/api-membership/module/cart/usecase"
	httpGallery "gitlab.com/uniqdev/backend/api-membership/module/gallery/delivery/http"
	mysqlGallery "gitlab.com/uniqdev/backend/api-membership/module/gallery/repository/mysql"
	usecaseGallery "gitlab.com/uniqdev/backend/api-membership/module/gallery/usecase"
	httpHelper "gitlab.com/uniqdev/backend/api-membership/module/helper/delivery/http"
	mysqlHelper "gitlab.com/uniqdev/backend/api-membership/module/helper/repository/mysql"
	usecaseHelper "gitlab.com/uniqdev/backend/api-membership/module/helper/usecase"
	httpMembership "gitlab.com/uniqdev/backend/api-membership/module/membership/delivery/http"
	mysqlMembership "gitlab.com/uniqdev/backend/api-membership/module/membership/repository/mysql"
	usecaseMembership "gitlab.com/uniqdev/backend/api-membership/module/membership/usecase"
	httpOutlet "gitlab.com/uniqdev/backend/api-membership/module/outlet/delivery/http"
	mysqlOutlet "gitlab.com/uniqdev/backend/api-membership/module/outlet/repository/mysql"
	usecaseOutlet "gitlab.com/uniqdev/backend/api-membership/module/outlet/usecase"
	httpPayment "gitlab.com/uniqdev/backend/api-membership/module/payment/delivery/http"
	mysqlPayment "gitlab.com/uniqdev/backend/api-membership/module/payment/repository/mysql"
	usecasePayment "gitlab.com/uniqdev/backend/api-membership/module/payment/usecase"
	httpPoint "gitlab.com/uniqdev/backend/api-membership/module/point_collection/delivery/http"
	mysqlPoint "gitlab.com/uniqdev/backend/api-membership/module/point_collection/repository/mysql"
	usecasePoint "gitlab.com/uniqdev/backend/api-membership/module/point_collection/usecase"
	httpProduct "gitlab.com/uniqdev/backend/api-membership/module/product/delivery/http"
	mysqlProduct "gitlab.com/uniqdev/backend/api-membership/module/product/repository/mysql"
	usecaseProduct "gitlab.com/uniqdev/backend/api-membership/module/product/usecase"
	httpPromotion "gitlab.com/uniqdev/backend/api-membership/module/promotion/delivery/http"
	mysqlPromotion "gitlab.com/uniqdev/backend/api-membership/module/promotion/repository/mysql"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion/repository/service"
	usecasePromotion "gitlab.com/uniqdev/backend/api-membership/module/promotion/usecase"
	httpSelfOrder "gitlab.com/uniqdev/backend/api-membership/module/self_order/delivery/http"
	mysqlSelfOrder "gitlab.com/uniqdev/backend/api-membership/module/self_order/repository/mysql"
	usecaseSelfOrder "gitlab.com/uniqdev/backend/api-membership/module/self_order/usecase"
	httpTransaction "gitlab.com/uniqdev/backend/api-membership/module/transaction/delivery/http"
	mysqlTransaction "gitlab.com/uniqdev/backend/api-membership/module/transaction/repository/mysql"
	usecaseTransaction "gitlab.com/uniqdev/backend/api-membership/module/transaction/usecase"
	httpUser "gitlab.com/uniqdev/backend/api-membership/module/user/delivery/http"
	mysqlUser "gitlab.com/uniqdev/backend/api-membership/module/user/repository/mysql"
	usecaseUser "gitlab.com/uniqdev/backend/api-membership/module/user/usecase"
	httpWishList "gitlab.com/uniqdev/backend/api-membership/module/wishlist/delivery/http"
	mysqlWishList "gitlab.com/uniqdev/backend/api-membership/module/wishlist/repository/mysql"
	usecaseWishList "gitlab.com/uniqdev/backend/api-membership/module/wishlist/usecase"
	"gitlab.com/uniqdev/backend/api-membership/routes"
)

func main() {
	fmt.Println("--start--")
	//utils.Kill(1619)
	//validate some files
	//utils.FileMustExist(auth.RSA_PUBLIC_KEY, auth.RSA_PRIVATE_KEY)
	// defer profile.Start(profile.MemProfile, profile.ProfilePath("."), profile.NoShutdownHook)

	//certPath := os.Getenv("ssl_cert")
	//keyPath := os.Getenv("ssl_key")

	if bugsnagKey := os.Getenv("BUGSNAG_API_KEY"); bugsnagKey != "" {
		hook, err := log.NewBugsnagHook(bugsnagKey)
		if err != nil {
			fmt.Println("error creating bugsnag hook: ", err)
		} else {
			log.AddHook(hook)
		}
	}

	// Create a new Slack hook with options if webhook URL is configured
	if webhookURL := os.Getenv("SLACK_WEBHOOK_URL"); webhookURL != "" {
		env := os.Getenv("server")
		if tag := os.Getenv("SERVER_TAG"); tag != "" {
			env = fmt.Sprintf("%v-%v", env, strings.ToLower(tag))
		}
		slackHook, err := log.NewSlackHook(
			webhookURL,
			log.WithChannel("#errors"),
			log.WithUsername("Error Bot"),
			log.WithIconEmoji(":rotating_light:"),
			log.WithField(map[string]string{
				"ENV": env,
			}),
		)
		if err == nil {
			log.AddHook(slackHook)
		}
	}

	cfg := google.Profiler()
	pofilerStarted := false

	// Profiler initialization, best done as early as possible.
	if err := profiler.Start(cfg); err != nil {
		fmt.Println("start profiler err: ", err)
	} else {
		pofilerStarted = true
		fmt.Println("profiler started...")
	}

	router := routes.InitRouter()
	//log.Fatal(fasthttp.ListenAndServeTLS(":"+port, certPath, keyPath, router.Handler))
	router.HandleOPTIONS = true
	//log.Fatal(fasthttp.ListenAndServe(":"+port, CORS(router.Handler)))
	//log.Fatal(fasthttp.ListenAndServe(":"+port, router.Handler))

	router.GET("/debug/pprof/:profile", pprofhandler.PprofHandler)

	dbConn := db.GetConn()
	redis := db.GetRedisClient()
	mongo := db.GetMongoDbClient()

	cacheImplement := cache.NewCacheDbRedis(redis)
	cacheImplementMongo := cache.NewCacheDbMongo(mongo)

	eitherCache := cacheImplementMongo
	if mongo == nil {
		eitherCache = cacheImplement
	}

	billingService := service.NewBillingService()

	appRepo := mysqlApp.NewAppRepository(dbConn, eitherCache)
	appUseCase := usecaseApp.NewAppUseCase(appRepo)
	httpApp.NewHttpAppHandler(router, appUseCase)

	authRepo := mysqlAuth.NewMysqlAuthRepository(dbConn)
	authUseCase := usecaseAuth.NewAuthUseCase(authRepo, appRepo)
	httpAuth.NewHttpAuthHandler(router, authUseCase)

	selfOrderRepo := mysqlSelfOrder.NewMysqlSelfOrderRepository(dbConn)
	selfOrderUc := usecaseSelfOrder.NewSelfOrderUseCase(selfOrderRepo)
	httpSelfOrder.NewHttpSelfOrderHandler(router, selfOrderUc)

	//contentRepo := mysqlContent.NewMysqlContentRepository(dbConn)
	//contentUseCase := usecaseContent.NewContentUseCase(contentRepo)
	//httpContent.NewHttpContentHandler(router, contentUseCase)

	productRepo := mysqlProduct.NewMysqlProductRepository(dbConn, cacheImplement)
	transactionRepo := mysqlTransaction.NewMysqlTransactionRepository(dbConn, eitherCache)

	outletRepo := mysqlOutlet.NewMysqlOutletRepository(dbConn, cacheImplement)
	outletUseCase := usecaseOutlet.NewOutletUseCase(outletRepo, transactionRepo)
	httpOutlet.NewHttpOutletHandler(router, outletUseCase)

	galleryRepo := mysqlGallery.NewMysqlGalleryRepository(dbConn)
	galleryUseCase := usecaseGallery.NewGalleryUseCase(galleryRepo)
	httpGallery.NewHttpGalleryHandler(router, galleryUseCase)

	cartRepo := mysqlCart.NewMysqlCartRepository(dbConn)
	cartUseCase := usecaseCart.NewCartUseCase(cartRepo, productRepo, outletUseCase)
	httpCart.NewHttpCartHandler(router, cartUseCase)

	wishListRepo := mysqlWishList.NewMysqlWishlistRepository(dbConn)
	wishListUseCase := usecaseWishList.NewWishlistUseCase(wishListRepo, productRepo)
	httpWishList.NewHttpWishlistHandler(router, wishListUseCase)

	userRepo := mysqlUser.NewMysqlUserRepository(dbConn, cacheImplement)

	helperRepo := mysqlHelper.NewMysqlHelperRepository(dbConn)
	helperUseCase := usecaseHelper.NewHelperUseCase(helperRepo)
	httpHelper.NewHttpHelperHandler(router, helperUseCase)

	membershipRepo := mysqlMembership.NewMysqlMembershipRepository(dbConn)
	membershipUseCase := usecaseMembership.NewMembershipUseCase(membershipRepo)
	httpMembership.NewHttpMembershipHandler(router, membershipUseCase)

	campaignRepo := mysqlCampaign.NewMysqlCampaignRepository(dbConn)
	campaignUseCase := usecaseCampaign.NewCampaignUseCase(campaignRepo)
	httpCampaign.NewHttpCampaignHandler(router, campaignUseCase)

	pointCollectionRepo := mysqlPoint.NewMysqlPointCollectionRepository(dbConn)
	pointCollectionUseCase := usecasePoint.NewPointCollectionUseCase(pointCollectionRepo, userRepo, campaignRepo)
	httpPoint.NewHttpPointCollectionHandler(router, pointCollectionUseCase)

	promotionRepo := mysqlPromotion.NewMysqlPromotionRepository(dbConn, cacheImplement)
	promotionUseCase := usecasePromotion.NewPromotionUseCase(promotionRepo, campaignRepo, helperUseCase, membershipRepo, userRepo, appRepo)
	httpPromotion.NewHttpPromotionHandler(router, promotionUseCase)

	userUseCase := usecaseUser.NewUserUseCase(userRepo, authUseCase, promotionUseCase)
	httpUser.NewHttpUserHandler(router, userUseCase)

	transactionUseCase := usecaseTransaction.NewTransactionUseCase(transactionRepo, productRepo, pointCollectionUseCase, appRepo, userRepo, billingService, pointCollectionRepo, campaignRepo)
	httpTransaction.NewHttpTransactionHandler(router, transactionUseCase)

	paymentRepo := mysqlPayment.NewMysqlPaymentRepository(dbConn)
	paymentUseCase := usecasePayment.NewPaymentUseCase(paymentRepo, promotionUseCase)
	httpPayment.NewHttpPaymentHandler(router, paymentUseCase)

	productUseCase := usecaseProduct.NewProductUseCase(productRepo, campaignRepo, outletRepo)
	httpProduct.NewHttpProductHandler(router, productUseCase)

	routes.CreateRoutes(router, promotionUseCase)

	// cacheImplement.Set("test", "myvalue", 0)

	fmt.Println("args: ", os.Args, "len:", len(os.Args))
	if len(os.Args) > 2 && os.Args[1] == "--test" {
		switch os.Args[2] {
		case "promo":
			promotionUseCase.RunPromotionRole()
			// promotionUseCase.RunPromotionReminder(2)
		case "sentiment":
			transactionUseCase.SetSentimentFeedback()
		case "feedback":
			transactionUseCase.SendFeedbackReport()
		case "campaign":
			campaignUseCase.Test()
		case "expiry":
			membershipUseCase.ProcessCalendarBasedExpiry()
			// membershipUseCase.ProcessActivityBasedExpiry()
		}
		panic(fmt.Sprintf("stop for testing... %v", os.Args[2]))
	}

	// running cron job
	job.RunCronJob(promotionUseCase, productUseCase, transactionUseCase, membershipUseCase)

	s := &fasthttp.Server{
		Handler:            auth.CORS(router.Handler),
		MaxRequestBodySize: 100 << 20, // 100MB
	}

	errs := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			fmt.Println(">> PANIC << ")
			fmt.Println(r)
		} else if errs != nil {
			fmt.Println(errs)
		}

		//write profiling
		env := os.Getenv("ENV")
		if env == "development" {
			basePath := fmt.Sprintf("config/profiler/%s", time.Now().Format("01_02_2006_15_04"))
			os.MkdirAll(basePath, os.ModePerm)
			profile.Start(profile.MemProfile, profile.ProfilePath(basePath), profile.MemProfileRate(1), profile.NoShutdownHook).Stop()
		}

		fmt.Println("\n\n------- SERVICE ENDED ---------")
	}()

	if pofilerStarted {
		// 	go func() {
		// 		t := time.Tick(time.Second * 5)
		// 		for {
		// 			<-t
		// 			// debug.FreeOSMemory()
		// 			runtime.Gosched()
		// 		}
		// 	}()
	}

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	go func() {
		port := os.Getenv("PORT")
		env := os.Getenv("server")
		if port == "" {
			port = "1619"
		}

		fmt.Printf("\n\n|----------- API CRM Started-----------|\n"+
			"port  : %s\n"+
			"env   : %s\n"+
			"utc   : %s\n"+
			"|-----------------------------|\n\n", port, env, time.Now().UTC())

		errs <- s.ListenAndServe(":" + port)
		// errs <- fasthttp.ListenAndServe(":"+port, router.Handler)
	}()

	fmt.Printf("exit: %v", <-errs)
}
