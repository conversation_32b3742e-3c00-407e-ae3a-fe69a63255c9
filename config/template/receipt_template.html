<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width"/>
    <title>Receipt Note</title>

    <style>
        .invoice-box {
            max-width: 800px;
            margin: auto;
            padding: 10px;
            border: 1px solid #eee;
            box-shadow: 0 0 10px rgba(0, 0, 0, .15);
            font-size: 13px;
            line-height: 24px;
            font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
            color: #555;
        }

        .invoice-box table {
            width: 100%;
            line-height: inherit;
            text-align: left;
        }

        .invoice-box table td {
            padding: 5px;
            vertical-align: top;
        }

        .invoice-box table tr td:nth-child(3) {
            text-align: right;
        }

        .invoice-box table tr.top table td {
            padding-bottom: 20px;
        }

        .invoice-box table tr.top table td.title {
            font-size: 45px;
            line-height: 45px;
            color: #333;
        }

        .invoice-box table tr.information table td {
            padding-bottom: 40px;
        }

        .invoice-box table tr.heading td {
            background: #eee;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }

        .invoice-box table tr.details td {
            padding-bottom: 20px;
        }

        .invoice-box table tr.item td {
            border-top: 1px solid #eee;
            border-bottom: none;
        }

        .invoice-box table tr.tax {
            line-height: 12px;
        }

        .invoice-box table tr.extra td {
            border-top: 12px #333333;
            border-bottom: 1px solid #eee;
        }

        .invoice-box table tr.item.last td {
            border-bottom: none;
        }

        .invoice-box table tr.total td:nth-child(3) {
            border-top: 2px solid #eee;
            font-weight: bold;
        }

        @media only screen and (max-width: 600px) {
            .auto-resize-square {
                margin: auto;
            }

            .invoice-box table tr.top table td {
                width: 100%;
                display: block;
                text-align: center;
            }

            .invoice-box table tr.information table td {
                width: 100%;
                display: block;
                text-align: center;
            }
        }

        /** RTL **/
        .rtl {
            direction: rtl;
            font-family: Tahoma, 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
        }

        .rtl table {
            text-align: right;
        }

        .rtl table tr td:nth-child(3) {
            text-align: left;
        }

        .tab {
            margin-left: 10px;
        }

        img {
            max-width: 100%;
            max-height: 100%;
        }

        .auto-resize-square {
            height: 95px;
            width: 95px;
        }
    </style>
</head>

<body>
<div class="invoice-box">
    <table cellpadding="0" cellspacing="0">
        <tr class="heading">
            <td>
                ITEM
            </td>
            <td>
                QTY
            </td>
            <td>
                SUBTOTAL
            </td>
        </tr>

        {{range .Items}}
            <tr class="item">
                <td> {{.Product}} </td>
                <td> {{.Qty}} </td>
                <td>{{.SubTotal}} </td>
            </tr>
            {{range .SubItems}}
                <tr>
                    <td>
                        --{{.Product}}
                    </td>
                    <td> {{.Qty}} </td>
                    <td>
                        {{.SubTotal}}
                    </td>
                </tr>
            {{end}}
            {{range .Promotions}}
                <tr>
                    <td>
                        <i>(promo {{.PromoType}})</i>
                    </td>
                    <td></td>
                    <td>
                        -{{.PromoValue}}
                    </td>
                </tr>
            {{end}}
        {{end}}

        {{range .TaxAndDisc}}
            <tr class="tax">
                <td colspan="2" align="right">
                    {{.Name}}
                </td>
                <td align="right">
                    {{.Total}}
                </td>
            </tr>
        {{end}}
        <tr class="total">
            <td colspan="2" align="right">
                <b>Grand Total</b>
            </td>
            <td align="right">
                <b>{{.GrandTotal}}</b>
            </td>
        </tr>
    </table>
</div>
</body>
</html>
