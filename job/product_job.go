package job

import (
	"fmt"
	"os"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
)

func CronNotifyProduct(ctx *fasthttp.RequestCtx) {
	if os.Getenv("server") == "localhost" || os.Getenv("server") == "development" {
		ctx.Write([]byte("progress is starting..."))
		go NotifyAvailableProducts()
	} else {
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
	}
}

func NotifyAvailableProducts() {
	sql := `
select min(p.name)            as product_name,
       min(o.name)            as outlet_name,
       cnp.product_detail_fkid,
       cnp.member_fkid,
       min(cnp.admin_fkid)    as admin_fkid,
       mm.name                as member_name,
       mm.email               as member_email,
       mm.phone               as member_phone,
       min(mm.firebase_token) as firebase_token
from crm_notify_product cnp
         join products_detail pd on cnp.product_detail_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         join outlets o on pd.outlet_fkid = o.outlet_id
         join (
    select m.name, m.email, m.phone, md.firebase_token, md.admin_fkid, m.member_id
    from members m
             join members_detail md on m.member_id = md.member_fkid
) mm on mm.member_id = cnp.member_fkid and mm.admin_fkid = cnp.admin_fkid
where pd.stock_qty >= cnp.qty
group by cnp.product_detail_fkid, member_fkid `

	data, err := db.QueryArray(sql)
	if log.IfError(err) {
		return
	}

	log.Debug("notifyProduct - %d data to be sent...", len(data))

	for _, notify := range data {
		sendMedias := map[string]interface{}{
			"push_notif": notify["firebase_token"],
			"email":      notify["member_email"],
			"whatsapp":   notify["member_phone"],
		}
		for media, receiver := range sendMedias {
			_, err = db.Insert("scheduled_message", map[string]interface{}{
				"title":        "Item yang sudah kamu tunggu-tunggu sudah ready",
				"message":      fmt.Sprintf("Hay %s, item %s sudah bisa di pesan lo di %s, \nBuruan di order ya sebelum kehabisan stok!", notify["member_name"], notify["product_name"], notify["outlet_name"]),
				"media":        media,
				"receiver":     receiver,
				"sent_via":     "WA_USER",
				"admin_fkid":   notify["admin_fkid"],
				"data_created": time.Now().Unix() * 1000,
				"time_deliver": time.Now().Unix() * 1000,
			})
			if log.IfError(err) {
				break
			}
		}

		_, err = db.GetConn().Exec("delete from crm_notify_product where member_fkid = ? and product_detail_fkid = ?", notify["member_fkid"], notify["product_detail_fkid"])
		if log.IfError(err) {
			break
		}
	}
}
