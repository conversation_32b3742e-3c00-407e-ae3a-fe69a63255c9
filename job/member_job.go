package job

import (
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
)

func RunPointExpiry(membershipUC membership.MembershipUseCase) error {
	// Process calendar based expiry
	if err := membershipUC.ProcessCalendarBasedExpiry(); log.IfError(err) {
		log.Error("Failed to process calendar based expiry: %v", err)
		return err
	}
	return nil
}

func RunUpdateMemberDetails(membershipUC membership.MembershipUseCase) error {
	if err := membershipUC.UpdateMemberDetails(); log.IfError(err) {
		log.Error("Failed to update member details: %v", err)
		return err
	}
	log.Info("Successfully updated member details")
	return nil
}

func RunActivityBasedExpiry(membershipUC membership.MembershipUseCase) error {
	// Process activity based expiry
	if err := membershipUC.ProcessActivityBasedExpiry(); log.IfError(err) {
		log.Error("Failed to process activity based expiry: %v", err)
		return err
	}
	return nil
}
