package job

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func RunPubSubSubscriber() {

}

func ReceiveOrderStatusChange(ctx *fasthttp.RequestCtx) {
	var pubsub models.PubSub
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&pubsub)
	if log.IfError(err) {
		return
	}

	data, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	if log.IfError(err) {
		return
	}

	log.Info("pubsub receive data: %s", string(data))
	var dataModel map[string]interface{}
	if log.IfError(json.Unmarshal(data, &dataModel)) {
		return
	}

	if dataModel["order_sales_id"] == nil {
		log.Info("order_sales_id not sent... dataModel: %v", dataModel)
		return
	}

	status := utils.ToString(dataModel["status"])
	if array.Contain([]string{"shipment_created", "shipment_updated", "ready"}, status) {
		notifyMember(utils.ToString(dataModel["order_sales_id"]), status)
	}
	go HandleOrderStatusChange(utils.ToString(dataModel["order_sales_id"]), dataModel["creator"])

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

func HandleOrderStatusChange(orderId string, creator interface{}) {
	log.Info("HandleOrderStatusChange %v", orderId)
	if orderId == "" {
		log.Info("can not HandleOrderStatusChange, orderId is empty!")
		return
	}
	orderStatusLog, err := db.Query("select * from order_sales_status_log where order_sales_id = ? order by id desc limit 1", orderId)
	log.IfError(err)

	status := utils.ToString(orderStatusLog["status"])

	v1.AddToFirebase(orderId, creator)
	if status == "cancel" || status == "reject" || status == "payment_reject" {
		order, err := db.Query("select items from order_sales where order_sales_id = ?", orderId)
		if log.IfError(err) {
			return
		}

		var sales models.Sales
		log.IfError(json.Unmarshal([]byte(utils.ToString(order["items"])), &sales))

		v1.UpdatePromotionBuy(sales, "available")

		if status == "reject" || status == "payment_reject" {
			notifyMember(orderId, status)
		}
	} else if status == "accept" {
		notifyMember(orderId, status)
	}
}

func notifyMember(orderId, status string) {
	sql := `
select md.firebase_token, os.member_fkid, o.admin_fkid
from order_sales os
         join outlets o on os.outlet_fkid = o.outlet_id
         join members_detail md on md.member_fkid = os.member_fkid and md.admin_fkid = o.admin_fkid 
where os.order_sales_id = ?`
	order, err := db.Query(sql, orderId)
	log.IfError(err)

	orderLog, err := db.Query("select message from order_sales_status_log where order_sales_id = ? and status = ? order by id desc limit 1", orderId, status)
	log.IfError(err)
	reason := strings.TrimSpace(utils.ToString(orderLog["message"]))
	if reason != "" {
		reason = fmt.Sprintf(`: "%s"`, reason)
	}

	title := "Order Status Berubah"
	msg := fmt.Sprintf("status %v telah berubah menjadi %v", orderId, status)

	if status == "reject" {
		title = "Pesanan Ditolak"
		msg = fmt.Sprintf("pesanan %s telah ditolak %s", orderId, reason)
	} else if status == "accept" {
		title = "Pesanan Terkonfirmasi"
		msg = fmt.Sprintf("pesanan %s telah dikonfirmasi. Silahkan lakukan pembayaran", orderId)
		updateGrandTotal(orderId)
	} else if status == "shipment_created" {
		shipment, err := db.Query("select shipping_id from order_sales_shipments where order_sales_id = ?", orderId)
		log.IfError(err)

		if len(shipment) == 0 {
			log.IfError(fmt.Errorf("no shipment data for order : %s", orderId))
			return
		}

		title = "Pesanan Dikirim"
		msg = fmt.Sprintf("pesanan %s telah dikirim dengan no resi %s", orderId, shipment["shipping_id"])
	} else if status == "shipment_updated" {
		title = "Perubahan No Resi"
		msg = fmt.Sprintf("nomor resi untuk pesanan %s telah di perbarui", orderId)
	} else if status == "payment_reject" {
		title = "Pembayaran Ditolak"
		msg = fmt.Sprintf("pembayaran untuk pesanan %s di tolak %s", orderId, reason)
	} else if status == "ready" {
		title = "Pesanan Telah Siap"
		msg = fmt.Sprintf("Order Anda untuk %v telah siap!!!", orderId)
	}

	if title == "" {
		log.Info("no notification for status '%s'", status)
		return
	}

	log.Info("notify member %v, orderId: %v, status: '%v'", order["member_fkid"], orderId, status)
	v1.SendNotification(models.UserNotification{
		Title:             title,
		Message:           msg,
		NotificationToken: utils.ToString(order["firebase_token"]),
		NotificationType:  "order_online",
		UserId:            utils.ToString(order["member_fkid"]),
		AdminId:           utils.ToInt(order["admin_fkid"]),
		NotificationData: map[string]interface{}{
			"order_sales_id": orderId,
		},
	})
}

func updateGrandTotal(orderId string) {
	sql := `select shipping_charge from order_sales_shipments where order_sales_id = ? limit 1`
	shipping, err := db.Query(sql, orderId)
	log.IfError(err)

	orderSale, err := db.Query("select grand_total_payment from order_sales where order_sales_id = ?", orderId)
	log.IfError(err)

	log.Info("update order_sales %v to add shipping charge of: %v, prev gradnTotal: %v", orderId, shipping, orderSale)
	_, err = db.Update("order_sales", map[string]interface{}{
		"grand_total_payment": cast.ToInt(orderSale["grand_total_payment"]) + cast.ToInt(shipping["shipping_charge"]),
	}, "order_sales_id = ?", orderId)
	log.IfError(err)
}

func checkingExpiredTransaction() {
	sql := `
SELECT os.order_sales_id, os.id,
       Timediff(Now(), From_unixtime(ossl.time_created / 1000))                    AS
       time_diff,
       Time_format(Timediff(Now(), From_unixtime(ossl.time_created / 1000)), '%H') AS
       diff_hour,
       o.admin_fkid,
       md.firebase_token, md.member_fkid, ossl2.status
FROM   order_sales os
       JOIN outlets o
         ON o.outlet_id = os.outlet_fkid
       JOIN members_detail md
         ON os.member_fkid = md.member_fkid
            AND o.admin_fkid = md.admin_fkid
            join (
            select max(id) as id, order_sales_id, max(time_created) as time_created from order_sales_status_log
group by order_sales_id
            ) ossl on ossl.order_sales_id=os.order_sales_id
  join order_sales_status_log ossl2 on ossl2.id = ossl.id
  where ossl2.status in ('accept', 'arrived')
HAVING diff_hour between 23 and 24`
	orders, err := db.QueryArray(sql)
	log.IfError(err)

	env := os.Getenv("server")
	if env == "demo" {
		env = "staging"
	}

	for _, order := range orders {
		//if utils.ToInt(order["diff_hour"]) < 24 && utils.ToString(order["status"]) != "accept" {
		//	continue
		//}

		notificationTitle := ""
		notificationMsg := ""

		if utils.ToInt(order["diff_hour"]) < 24 && utils.ToString(order["status"]) == "accept" {
			notificationTitle = "Waktu Pembayaran Segera Berakhir"
			notificationMsg = fmt.Sprintf("waktu pembayaran untuk pesanan %s akan segera berakhir dalam waktu %d jam", order["order_sales_id"], 24-utils.ToInt(order["diff_hour"]))

			//if reminder already sent, dont send again
			schedMsg, err := db.Query(" select id from scheduled_message where message = ?", notificationMsg)
			log.IfError(err)
			if schedMsg["id"] != nil {
				continue
			}
		} else if utils.ToInt(order["diff_hour"]) >= 24 {
			status := "cancel"
			msg := "melebihi batas waktu pembayaran"

			if utils.ToString(order["status"]) == "arrived" {
				status = "received"
				msg = "set by system"
			} else if utils.ToString(order["status"]) == "accept" {
				notificationTitle = "Transaksi Dibatalkan"
				notificationMsg = fmt.Sprintf("pesanan %s telah dibatalkan karena melebihi batas waktu pembayaran", order["order_sales_id"])
			} else {
				continue
			}

			_, err = db.Update("order_sales", map[string]interface{}{
				"status": status,
			}, "order_sales_id = ?", order["order_sales_id"])
			log.IfError(err)

			_, err = db.Insert("order_sales_status_log", map[string]interface{}{
				"order_sales_id":   order["order_sales_id"],
				"order_sales_fkid": order["id"],
				"status":           status,
				"admin_id":         order["admin_fkid"],
				"message":          msg,
				"time_created":     time.Now().UnixNano() / 1000000,
			})
			log.IfError(err)

			v1.PublishOrderStatusUpdate(utils.ToString(order["order_sales_id"]), status)
		}

		//notify member
		v1.SendNotification(models.UserNotification{
			Title:             notificationTitle,
			Message:           notificationMsg,
			NotificationToken: utils.ToString(order["firebase_token"]),
			NotificationType:  "order_online",
			UserId:            utils.ToString(order["member_fkid"]),
			AdminId:           utils.ToInt(order["admin_fkid"]),
			NotificationData: map[string]interface{}{
				"order_sales_id": order["order_sales_id"],
			},
		})
	}
}

func RemoveTransactionFromFirebaseDb() {
	ctx := context.Background()
	app := firebase.GetFirebaseApp()
	if app == nil {
		log.Info("firebase app is not initialized")
		return
	}
	dbClient, err := app.Database(ctx)
	if log.IfError(err) {
		return
	}

	env := os.Getenv("server")
	path := fmt.Sprintf("%s/crm_transaction/", env)
	log.Info("deleting firebase db.... ref: %s", path)

	log.IfError(dbClient.NewRef(path).Delete(ctx))
}

func ResetMemberPoint() {
	//schedule for date : 21 March 2022
	scheduledFor := "2022-03-20"
	if time.Now().Format("2016-01-02") != scheduledFor {
		return
	}

	log.IfError(fmt.Errorf("ResetMemberPoint Run, at: %s", time.Now().Format("2016-01-02")))
	adminId := 2

	membersDetail, err := db.QueryArray("select * from members_detail where admin_fkid = ?", adminId)
	log.IfError(err)
	log.Info("total member detail: %d", len(membersDetail))
	log.Info("[RESET] members_detail: %s", utils.SimplyToJson(membersDetail))

	_, err = db.Update("members_detail", map[string]interface{}{
		"total_point": 0,
	}, "admin_fkid = ?", adminId)
	log.IfError(err)

	membersTypeHistory, err := db.QueryArray("select * from members_type_history where admin_fkid = ?", adminId)
	log.IfError(err)
	log.Info("total membersTypeHistory: %d", len(membersTypeHistory))
	log.Info("[RESET] members_type_history: %s", utils.SimplyToJson(membersTypeHistory))

	_, err = db.Delete("members_type_history", "admin_fkid = ?", adminId)
	log.IfError(err)
}
