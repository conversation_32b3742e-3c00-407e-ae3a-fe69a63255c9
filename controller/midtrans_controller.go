package controller

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/payment/midtrans"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"strings"
	"time"
)

func Charge(ctx *fasthttp.RequestCtx) {
	//'https://app.midtrans.com/snap/v1/transactions' :
	//'https://app.sandbox.midtrans.com/snap/v1/transactions';

	url := "https://app.sandbox.midtrans.com/snap/v1/transactions"
	serverKey := "SB-Mid-server-LQWLVNscYO21EWS2i_yxijCp"
	jsonBody := make(map[string]interface{})
	err := json.Unmarshal(ctx.PostBody(), &jsonBody)
	utils.CheckErr(err)

	request := utils.HttpRequest{
		Method:      "POST",
		Url:         url,
		PostRequest: utils.PostRequest{Body: jsonBody},
		Header: map[string]interface{}{
			"Content-Type":  "application/json",
			"Accept":        "application/json",
			"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(serverKey)),
		},
	}
	body, err := request.ExecuteRequest()

	if err != nil {
		fmt.Println("Error - ", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	result := make(map[string]interface{})
	json.Unmarshal(body, &result)
	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(result)
}

func PaymentNotification(ctx *fasthttp.RequestCtx) {
	jsonBody := make(map[string]interface{})
	jsonStr := string(ctx.PostBody())
	err := json.Unmarshal(ctx.PostBody(), &jsonBody)
	log.IfError(err)
	fmt.Println("\nMIDTRANS JSON --> \n", jsonStr)

	var paymentNotif models.PaymentNotification
	err = json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&paymentNotif)
	if log.IfError(err) {
		fmt.Println("Parse json from MidTrans failed ", string(ctx.PostBody()))
		return
	}

	//check status to midtrans
	paymentStatus, err := midtrans.Default().CheckPaymentStatus(paymentNotif.TransactionID)
	log.IfError(err)

	if paymentStatus.StatusCode == midtrans.PAYMENT_NOT_FOUND {
		fmt.Printf("transaction id : %s doesnt active", paymentNotif.TransactionID)
		return
	}

	orderId := paymentNotif.OrderID[:3]
	if strings.ToLower(orderId) == "pos" {
		//uniqpos.xyz/public/midtrans/payment_notification_new
		//http://uniqdev.iamsuara.com/ari/billing/payment/payment_notification_new
		//http://jeroan.uniqpos.xyz/billing/payment/payment_notification_new
		fmt.Println("sending to web billing (POS)")
		request := utils.HttpRequest{
			Method:      "POST",
			Url:         "https://crm.uniqpos.xyz/billing/payment/payment_notification",
			PostRequest: utils.PostRequest{Body: jsonBody},
		}
		_, err := request.ExecuteRequest()

		if err != nil {
			fmt.Println("Sending to WEB webhook error", err)
		} else {
			fmt.Println("sending to web webhook success")
		}
	} else {
		//_, err := db.GetConn().Exec("INSERT INTO payment_notification VALUES (?,?,?)  ON DUPLICATE KEY UPDATE info = ?",
		//	paymentNotif.TransactionID, paymentNotif.OrderID, jsonStr, jsonStr)
		_, err := db.Insert("payment_notification", map[string]interface{}{
			"transaction_id":     paymentNotif.TransactionID,
			"order_id":           paymentNotif.OrderID,
			"data_created":       time.Now().UnixNano() / 1000000,
			"transaction_status": paymentNotif.TransactionStatus,
			"info":               jsonStr,
		})
		log.IfError(err)

		go v1.HandlePaymentChange(paymentNotif.OrderID)

		if paymentNotif.OrderID[:6] == "CRMDLS" {
			status := paymentNotif.TransactionStatus
			if status == "settlement" {
				status = "paid"
			}
			_, err = db.Update("promotion_buy_payment", map[string]interface{}{
				"status": status,
			}, "transaction_id = ?", paymentNotif.TransactionID)
			log.IfError(err)
		}
	}
}
