package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/generate"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func GetToken(ctx *fasthttp.RequestCtx) {
	userId := ctx.FormValue("user_id")
	userPswd := ctx.FormValue("user_secret")
	token := ctx.Request.Header.Peek("Authorization")

	pubKey := ctx.Request.Header.Peek("Public-Key")
	fmt.Println("pubkey : ", pubKey)

	admin, err := db.Query("SELECT admin_fkid, email, password FROM admin WHERE email = ?", userId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	apiResp := models.ApiResponse{Status: false, Message: "Invalid Credential!"}
	ctx.SetStatusCode(fasthttp.StatusUnauthorized)

	if len(admin) > 0 {
		tokenStr := strings.TrimSpace(strings.Replace(string(token), "Bearer", "", -1))
		tokenDec := utils.Decrypt(tokenStr, utils.UID_PASSPHRASE)
		isAdminIdEqual := utils.CheckPasswordHash(utils.ToString(admin["admin_fkid"]), tokenDec)
		isPasswordEqual := utils.CheckPasswordHash(string(userPswd), admin["password"].(string))

		if isAdminIdEqual && isPasswordEqual {
			auth := auth.InitJWTAuth()
			uid := utils.Encrypt(utils.ToString(admin["admin_id"]), utils.UID_PASSPHRASE)
			authToken := auth.GenerateToken("uid", uid)

			apiResp.Status = true
			apiResp.Data = authToken
			apiResp.Message = "Success"

			ctx.SetStatusCode(fasthttp.StatusOK)
		} else {
			if !isAdminIdEqual {
				//auth token not valid
				apiResp.Code = 2
			} else if !isPasswordEqual {
				//password invalid
				apiResp.Code = 3
			}
		}
	} else {
		//user not found
		apiResp.Code = 1
	}

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(apiResp)
}

func GetTokenWithPhoneAuth(ctx *fasthttp.RequestCtx) {
	ctx.SetContentType("application/json")
	authToken := ctx.Request.Header.Peek("Authorization")
	idToken := ctx.PostArgs().Peek("id_token")
	pubKey := ctx.Request.Header.Peek("Public-Key")
	adminId := utils.Decrypt(string(pubKey), utils.UID_PUBKEY)
	reqStart := time.Now()
	userAgent := ctx.Request.Header.Peek("user-agent")
	platform := ctx.Request.Header.Peek("platform")

	log.Info("start request token... adminId: '%s', platform: %s, userAgent: %s", adminId, platform, userAgent)

	if authToken == nil {
		log.Warn("request token with no auth token")
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	if status, msg := utils.IsValidPostInput(ctx, "id_token"); !status {
		log.Debug(msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	//error code 52: banner -> just for temporary
	if idToken == nil || string(idToken) == "" || len(string(idToken)) <= 10 {
		log.Info("can not verify empty idToken: %s", idToken)
		// ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "can not verify empty token", Code: 52})
		return
	}

	// phone, email, err := utils.IsValidIdToken(string(idToken))
	idTokenResult, err := firebase.VerifyIdToken(string(idToken))
	if log.IfError(err) {
		log.Warn("request token failed, id token is invalid: " + err.Error())
		log.Info("idToken: %v", string(idToken))
		// ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error(), Code: 52})
		return
	}

	// log.Info("id_token: %s", idToken)

	param := idTokenResult.Phone
	authType := "phone"
	if idTokenResult.Phone == "" && idTokenResult.Email != "" {
		authType = "email"
		param = idTokenResult.Email
	}

	sql := `
select member_id,
       email,
       email_verified,
	   phone_verified,
       name,
       gender,
       phone,
       date_of_birth,
       address,
       city,
       province,
       postal_code
from members m
WHERE $key = ?
LIMIT 1`
	sql = strings.Replace(sql, "$key", authType, 1)
	members, err := db.Query(sql, param)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(members) == 0 {
		log.Info("user with '%s' not found: %v", authType, param)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "User not found!", Code: 50})
		return
	}

	if (authType == "phone" || idTokenResult.Provider == firebase.SignInProviderPassword) && cast.ToInt(members["phone_verified"]) == 0 {
		//update, set phone to verified
		db.Update("members", map[string]interface{}{
			"phone_verified": 1,
		}, "member_id = ?", members["member_id"])
	} else if authType == "email" && cast.ToInt(members["phone_verified"]) == 0 {
		log.Info("user phone '%s' not verified", idTokenResult.Phone)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 56, Message: "phone number not verified", Data: members})
		return
	}

	//Get member detail
	memberDetail, err := db.QueryArray("SELECT * FROM members_detail WHERE member_fkid = ?", members["member_id"])
	log.IfError(err)

	log.Info("member %s registered at %d businesses", idTokenResult.Phone, len(memberDetail))
	var memberAdmin map[string]interface{}

	verifyAdminIdStart := time.Now()

	//verify auth token sent from client
	tokenStr := strings.TrimSpace(strings.Replace(string(authToken), "Bearer", "", -1))
	tokenDec := utils.Decrypt(tokenStr, utils.UID_PASSPHRASE)

	if adminId == "" {
		//--> old way, can took long time due to bcrypt lib
		log.Info("user %s has no pub-key, use old way to verify...", idTokenResult.Phone)
		for _, detail := range memberDetail {
			isAdminIdEqual := utils.CheckPasswordHash(utils.ToString(detail["admin_fkid"]), tokenDec)
			if isAdminIdEqual {
				log.Debug("found member_detail : %s", utils.SimplyToJson(detail))
				memberAdmin = detail
				adminId = cast.ToString(detail["admin_fkid"])
				break
			}
		}
	} else {
		for _, detail := range memberDetail {
			if adminId == utils.ToString(detail["admin_fkid"]) {
				checkHashStart := time.Now()
				//isAdminIdEqual := utils.CheckPasswordHash(utils.ToString(detail["admin_fkid"]), tokenDec)
				isAdminIdEqual := true
				if isAdminIdEqual {
					log.Debug("found member_detail : %v", detail)
					memberAdmin = detail
				}
				fmt.Printf("check hash took : %v \n", time.Since(checkHashStart))
				break
			}
		}
	}

	log.Info("verify id admin took : %v", time.Since(verifyAdminIdStart))

	if len(memberAdmin) == 0 {
		//if memberAdmin is empty, can be mean user not registered at the business
		//if adminId != "" {
		//}else{
		//
		//}
		//admin, err := db.Query("select business_name from admin where admin_id = ?", adminId)
		//log.IfError(err)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "you are not registered at this business!", Code: 53})
		//_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "Invalid credential!"})
		return
	}

	if utils.ToString(memberAdmin["status"]) == "0" {
		log.Info("member '%v' status is not active", members["member_id"])
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "User status is not active", Code: 51})
		return
	} else if utils.ToString(memberAdmin["status"]) == "2" {
		log.Info("member '%v' status is banned", members["member_id"])
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "User is banned", Code: 52})
		return
	}

	log.Info("memberId: %v | adminId: %v | status: %v", members["member_id"], adminId, memberAdmin["status"])

	//fetch user access
	memberAccess, err := db.Query("SELECT access_type from members_access where member_fkid = ? and admin_fkid = ?", members["member_id"], adminId)
	log.IfError(err)

	auth := auth.InitJWTAuth()
	uid := utils.Encrypt(utils.ToString(memberAdmin["admin_fkid"]), utils.UID_PASSSERVER)
	mid := utils.Encrypt(utils.ToString(members["member_id"]), utils.UID_PASSSERVER)

	authTokenNew := auth.GenerateToken("uid", uid, "mid", mid, "access", cast.ToString(memberAccess["access_type"]))
	go updateLastLogin(adminId, cast.ToString(members["member_id"]))

	log.Info("request token took : %v", time.Since(reqStart))

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "Success", Data: authTokenNew})
}

func updateLastLogin(adminId, memberId string) {
	db.Update("members_detail", map[string]interface{}{
		"last_login": time.Now().UnixMilli(),
	}, "admin_fkid = ? and member_fkid = ?", adminId, memberId)
}

func LoginSMS(ctx *fasthttp.RequestCtx) {
	ctx.SetContentType("application/json")
	authToken := ctx.Request.Header.Peek("Authorization")
	idToken := ctx.PostArgs().Peek("id_token")
	pubKey := ctx.Request.Header.Peek("Public-Key")
	adminId := utils.Decrypt(string(pubKey), utils.UID_PUBKEY)
	log.Info("login, auth: %s", string(authToken))

	if authToken == nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	if status, msg := utils.IsValidPostInput(ctx, "id_token"); !status {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		fmt.Printf("error getting Auth client: %v\n", err)
	}

	isDemoAccount := string(idToken) == fmt.Sprintf("%s:%s", utils.DemoAccountPhone, utils.DemoAccountPassword)
	log.Info("isdemo account: %v", isDemoAccount)

	token, err := client.VerifyIDToken(ctxBack, string(idToken))
	if err != nil && !isDemoAccount {
		fmt.Printf("error verifying ID token: %v\n", err)
		fmt.Printf("id_token: %v\n", string(idToken))
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: fmt.Sprintf("%v", err)})
		return
	}

	// if token.Claims["phone_number"] == nil {
	// 	log.Warn("Login failed. token doesnt contain phone number")
	// 	//_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "Server can not get your phone number!"})
	// 	//return
	// }

	var email, phone string
	if isDemoAccount {
		phone = "*************"
	} else {
		email = utils.ToString(token.Claims["email"])
		phone = utils.ToString(token.Claims["phone_number"])
	}

	phone = strings.TrimPrefix(phone, "+")
	log.Info("auth phone : '%s', email: %v", phone, email)

	sql := `
select member_id,
       email,
       email_verified,
	   phone_verified,
       name,
       gender,
       phone,
       date_of_birth,
       address,
       city,
       province,
       postal_code
from members m
WHERE phone = ? or email = ?
LIMIT 1`
	members, err := db.Query(sql, phone, email)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(members) == 0 {
		log.Info("user '%v'/'%v' not found", phone, email)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "User not found!", Code: 50})
		return
	}

	log.Info("'%v' isPhoneVerified: %v", email, cast.ToInt(members["phone_verified"]) == 0)
	if email != "" && cast.ToInt(members["phone_verified"]) == 0 {
		if os.Getenv("ENV") == "development" {
			log.IfError(fmt.Errorf("'%s' should not login with email, phone not verified", email))
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "phone number not verified", Code: 51})
			return
		}
	}

	//Get member detail
	log.Info("memberId : %v, phone: %v, email: %v | phone verified: %v", members["member_id"], members["phone"], members["email"], members["phone_verified"])
	memberDetail, err := db.QueryArray("SELECT * FROM members_detail WHERE member_fkid = ?", members["member_id"])
	log.IfError(err)

	var memberAdmin map[string]interface{}
	tokenStr := strings.TrimSpace(strings.Replace(string(authToken), "Bearer", "", -1))
	tokenDec := utils.Decrypt(tokenStr, utils.UID_PASSPHRASE)

	for _, detail := range memberDetail {
		//verify auth token sent from client
		isAdminIdEqual := adminId == utils.ToString(detail["admin_fkid"])
		if adminId == "" {
			isAdminIdEqual = utils.CheckPasswordHash(utils.ToString(detail["admin_fkid"]), tokenDec)
		}
		if isAdminIdEqual {
			log.Info("found member_detail: %v", detail)
			memberAdmin = detail
			break
		}
	}

	if len(memberAdmin) > 0 {
		if utils.ToString(memberAdmin["status"]) == "0" {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "User status is not active", Code: 51})
			return
		} else if utils.ToString(memberAdmin["status"]) == "2" {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "User is banned", Code: 52})
			return
		}

		//get member type
		memberType, err := db.Query("select p.name from members_type mt join products p on mt.product_fkid = p.product_id where type_id = ?", memberAdmin["type_fkid"])
		log.IfError(err)

		//if login with phone, and not verified, set to verified
		if phone != "" && cast.ToInt(members["phone_verified"]) == 0 {
			log.Info("should verify phone number of %v", phone)
			_, err = db.Update("members", map[string]interface{}{
				"phone_verified": 1,
			}, "member_id = ?", members["member_id"])
			log.IfError(err)
		}

		auth := auth.InitJWTAuth()
		uid := utils.Encrypt(utils.ToString(memberAdmin["admin_fkid"]), utils.UID_PASSSERVER)
		mid := utils.Encrypt(utils.ToString(members["member_id"]), utils.UID_PASSSERVER)
		authToken := auth.GenerateToken("uid", uid, "mid", mid)

		utils.RemoveField(members, "admin_fkid")

		members["barcode"] = utils.Encrypt(utils.ToString(members["member_id"]), utils.KEY_MEMBER_BARCODE)
		// members["barcode_url"] = fmt.Sprintf("https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=%s", members["barcode"])
		members["barcode_url"] = generate.QrCode(cast.ToString(members["barcode"]), 100)

		members["member_type"] = memberType["name"]
		members["register_date"] = memberAdmin["register_date"]
		members["total_point"] = memberAdmin["total_point"]
		resp := map[string]interface{}{
			"user":  members,
			"token": authToken,
		}
		log.Info("%v memberBarcode is '%v'", members["member_id"], members["barcode"])
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "Success", Data: resp})
	} else {
		log.Info("token decrypt: '%s' ", tokenDec)
		log.Info("idtoken: '%s' ", idToken)
		log.Info("user '%s' / '%s' not registered at this business - auth : %s", phone, email, authToken)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "You are not registered at this business!", Code: 53})
	}
}
