package v2

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/messaging"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func LostPhoneNumber(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	email := string(ctx.PostArgs().Peek("email"))
	phone := string(ctx.PostArgs().Peek("phone"))

	if ok, msg := utils.IsValidPostInput(ctx, "email", "phone"); !ok {
		log.Warn(msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	if !utils.IsValidEmailAddress(email) {
		log.Warn("'%s' is invalid email address", email)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid email address"})
		return
	}

	if !strings.HasPrefix(phone, "62") && !strings.HasPrefix(phone, "08") {
		log.Warn("'%s' is invalid phone number", phone)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid phone number"})
		return
	}

	phoneWithCode := phone
	if strings.HasPrefix(phone, "08") {
		phoneWithCode = "62" + phone[1:]
	}

	//validate phone number (if its already used)
	member, err := db.Query("select phone, email from members where phone = ? or phone = ? limit 1", phone, phoneWithCode)
	log.IfError(err)
	if len(member) > 0 {
		errMsg := "phone number already used"
		log.Warn("phone '%s' already used", phone)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: errMsg})
		return
	}

	if os.Getenv("ENV") != "production" {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "phone number can not be changed at the moment"})
		return
	}

	//check if email address is already registered
	sql := `select member_id
from members m
         join members_detail md on m.member_id = md.member_fkid
where m.email = ?
  and md.admin_fkid = ?
limit 1 `
	member, err = db.Query(sql, email, adminId)
	log.IfError(err)
	if len(member) == 0 {
		log.Warn("email '%s' not registered in system", email)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "email not registered"})
		return
	}

	msgTemplate := `untuk memverifikasi akun email anda, masukan kode berikut ini : <br/>
<h2> %d </h2><br/>
kode dapat digunankan maksimal sampai jam %v <br/><br/>
<i>jika anda merasa tidak melakukan permintaan kode, abaikan email ini</i><br/><br/></br>`

	//generate code, this code will send to email
	code := utils.RandomNumber(100000, 999999)
	timeExpired := time.Now().Add(30 * time.Minute)

	err = messaging.SendEmail(email, fmt.Sprintf(msgTemplate, code, timeExpired.Add(7*time.Hour).Format("15:04 01/02/2006")), "Change Phone Number Verification")
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	key := utils.Encrypt(fmt.Sprintf("%d%d%d#%s", code, timeExpired.Unix(), member["member_id"], phoneWithCode), utils.KEY_EMAIL_CODE)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"key":        key,
		"expired_at": timeExpired.Unix() * 1000,
	}})
}

func ValidateLostPhoneNumber(ctx *fasthttp.RequestCtx) {
	key := string(ctx.PostArgs().Peek("key"))
	code := utils.ToInt(ctx.PostArgs().Peek("code"))

	if ok, msg := utils.IsValidPostInput(ctx, "key", "code"); !ok {
		log.Warn(msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	keyDecoded := utils.Decrypt(key, utils.KEY_EMAIL_CODE)
	separator := strings.Index(keyDecoded, "#")
	if len(keyDecoded) < 23 || separator <= 0 {
		log.Debug("key is invalid, after decode : %v", keyDecoded)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid key"})
		return
	}

	originCode := utils.ToInt(keyDecoded[:6])
	expired := utils.ToInt64(keyDecoded[6:16])
	memberIdDecoded := utils.ToInt64(keyDecoded[16:separator])
	newPhone := keyDecoded[separator+1:]

	if expired < time.Now().Unix() {
		log.Warn("verification failed, expired")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "expired"})
		return
	}

	if code != originCode {
		log.Warn("verification failed, unmatch code, key : '%s' | code : '%s'", key, code)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid verification code"})
		return
	}

	if len(newPhone) < 10 {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	resp, err := db.Update("members", map[string]interface{}{
		"phone": newPhone,
	}, "member_id = ?", memberIdDecoded)

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	row, _ := resp.RowsAffected()
	if row > 0 {
		go func(id int64) {
			_, err = db.Update("members", map[string]interface{}{
				"email_verified": 1,
			}, "member_id = ?", id)
			log.IfError(err)
		}(memberIdDecoded)
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}
