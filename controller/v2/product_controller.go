package v2

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/choose"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/istime"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/memory"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/cache"
)

func GetProductWithAvailability(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	log.Debug("%s : %s", string(ctx.Method()), ctx.URI().String())
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := cast.ToInt(ctx.Request.Header.Peek("mid"))
	productId := ctx.UserValue("id")
	fmt.Println("GetProductWithAvailability V1")

	if !utils.IsNumber(productId) {
		tmpProdId := utils.Decrypt(utils.ToString(productId), utils.KEY_PRODUCT_ID)
		if utils.IsNumber(tmpProdId) {
			productId = tmpProdId
		}
	}

	var wg sync.WaitGroup
	productChan := make(chan []map[string]interface{}, 1)
	outletChan := make(chan []map[string]interface{}, 1)
	workingHoursChan := make(chan []map[string]interface{}, 1)
	productSubcategoryConfigChan := make(chan []map[string]interface{}, 1)

	sql := `
select product_id,
       p.name,
       photo,
       barcode,
       p.min_qty_order,
	   pd.stock_qty,
       pd.price_sell,
       pd.outlet_fkid,
       pd.outlet_fkid as outlet_id, 
       pd.price_sell,
       pd.product_detail_id,
       ps.product_subcategory_id,
       ps.name                                                                   as subcategory,
       u.name                                                                    as unit,
       u.description                                                             as unit_description,
       p.description,
       IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', pd.stock) as availability, 
	   if(p.stock_management = 1, if(pd.stock_qty <= 0 OR pd.stock_qty < p.min_qty_order, 'unavailable', 'available'), pd.stock) as stock_status,
	   IF(p.stock_management = 1 and pd.stock_qty < p.min_qty_order, 'unavailable', pd.stock) as stock_status_v1, 
	   if(pd.stock = 'unavailable', pd.stock,
          if((p.app_show = 0 or cpac.total_available > 0) and cpaa.hour_start is null,
             'unavailable', 'available'))                                            as stock_status_old,

       pdv.*,
       cpac.total_available,
       coalesce(cpaa.hour_start, '00:00:00')                                     as hour_start,
       coalesce(cpaa.hour_end, '23:59:59')                                       as hour_end
		$select
from products p
         join products_detail pd on p.product_id = pd.product_fkid
         join outlets o on pd.outlet_fkid = o.outlet_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         join unit u on p.unit_fkid = u.unit_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
         left join (select count(*) total_available, product_fkid
                    from crm_products_available
                    group by product_fkid) cpac
                   on cpac.product_fkid = p.product_id
         left join (select product_fkid, day, hour_start, hour_end
                    from crm_products_available
                    where hour_start <= from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and hour_end > from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and convert(day USING utf8)=(lower(dayname(from_unixtime(unix_timestamp() + ?))))) cpaa
                   on cpaa.product_fkid = p.product_id
		$join
where p.admin_fkid = ?
  and p.app_show = 1
  and o.app_show = 1
  and p.data_status = 'on'
  and o.data_status = 'on'
  and (pd.active = 'on_all' or pd.active = 'on_sales')
  and pd.data_status = 'on'
  $where_cond
order by ps.name, p.name `

	args := utils.ArrayOf(timeOffset, timeOffset, timeOffset)

	fmt.Println(">> memberId: ", memberId)
	if memberId > 0 {
		sql = strings.Replace(sql, "$select", ",if((cpw.crm_product_wishlist_id) > 0, true, false) as is_in_wishlist", 1)
		sql = strings.Replace(sql, "$join", "left join crm_product_wishlist cpw on cpw.product_fkid=p.product_id and cpw.member_fkid = ?", 1)
		args = append(args, memberId)
	}

	args = append(args, adminId)

	if productId != nil {
		sql = strings.Replace(sql, "$where_cond", " and p.product_id = ? ", 1)
		args = append(args, productId)
	} else {
		sql = strings.Replace(sql, "$where_cond", " and if(cpac.total_available > 0, cpaa.hour_start is not null, true)  ", 1)
	}

	sql = strings.Replace(sql, "$where_cond", "", 1)
	sql = strings.Replace(sql, "$select", "", 1)
	sql = strings.Replace(sql, "$join", "", 1)

	wg.Add(1)
	go db.QueryArrayGo(&wg, productChan, sql, args...)

	wg.Add(1)
	go db.QueryArrayGo(&wg, outletChan, "SELECT outlet_id, name FROM outlets WHERE admin_fkid = ? and app_show=1 and data_status= 'on' ", adminId)

	sql = `SELECT
	ow.*
FROM
	outlets_workinghour ow
	JOIN outlets o ON o.outlet_id = ow.outlet_fkid
WHERE
	o.admin_fkid = ?
	AND o.app_show = 1
	AND o.data_status = 'on'`

	wg.Add(1)
	go db.QueryArrayGo(&wg, workingHoursChan, sql, adminId)

	sql = `SELECT csc.subcategory_fkid, csc.position from crm_subcategory_config csc 
	join products_subcategory ps on ps.product_subcategory_id=csc.subcategory_fkid
	where ps.admin_fkid=?`

	wg.Add(1)
	go db.QueryArrayGo(&wg, productSubcategoryConfigChan, sql, adminId)

	wg.Wait()

	dateNow := time.Now().Add(7 * time.Hour)
	today := strings.ToLower(dateNow.Weekday().String())
	tNow, err := time.Parse("15:4:5", fmt.Sprintf("%d:%d:%d", dateNow.Hour(), dateNow.Minute(), dateNow.Day()))
	log.IfError(err)

	workingHours := <-workingHoursChan
	outletMap := make(map[string]string)
	enableOrderByOutlet := make(map[string]interface{})
	for _, outlet := range <-outletChan {
		outletMap[utils.ToString(outlet["outlet_id"])] = utils.ToString(outlet["name"])

		isEnableOrder := true
		for _, wh := range workingHours {
			if utils.ToString(wh["outlet_fkid"]) == utils.ToString(outlet["outlet_id"]) {
				isEnableOrder = false
				if cast.ToString(wh["day"]) == today {
					tOpen, _ := time.Parse("15:04:05", cast.ToString(wh["time_open"]))
					tClose, _ := time.Parse("15:04:05", cast.ToString(wh["time_close"]))
					if tNow.After(tOpen) && tNow.Before(tClose) {
						isEnableOrder = true
					}
					break
				}
			}
		}

		enableOrder := map[string]interface{}{
			"status": "enable",
		}

		if !isEnableOrder {
			enableOrder = map[string]interface{}{
				"status": "disable",
				"reason": "closed",
			}
		}

		enableOrderByOutlet[utils.ToString(outlet["outlet_id"])] = enableOrder
	}

	products := <-productChan

	subcategoryConfigs := <-productSubcategoryConfigChan
	subcategoryConfMap := array.FlatMapArray(subcategoryConfigs, "subcategory_fkid")

	result := make([]map[string]interface{}, 0)
	variants := make([]map[string]interface{}, 0)
	available := make([]map[string]interface{}, 0)
	availability := "unavailable"

	for i, product := range products {
		if product["photo"] == nil || product["photo"] == "" {
			// product["photo"] = utils.DEFAULT_IMAGE
		} else if !strings.HasPrefix(utils.ToString(product["photo"]), "https://") {
			product["photo"] = fmt.Sprintf("%s/assets/images/products/%s/%s", os.Getenv("base_url"), adminId, product["photo"])
		}

		product["subcategory_position"] = nil
		if conf, ok := subcategoryConfMap[cast.ToString(product["product_subcategory_id"])]; ok {
			product["subcategory_position"] = conf["position"]
		}

		products[i]["is_in_wishlist"] = cast.ToInt(products[i]["is_in_wishlist"]) == 1

		if i == len(products)-1 || product["product_id"] != products[i+1]["product_id"] || product["outlet_fkid"] != products[i+1]["outlet_fkid"] {
			available = append(available, map[string]interface{}{
				"name":              outletMap[utils.ToString(product["outlet_id"])],
				"outlet_id":         product["outlet_fkid"],
				"price_sell":        product["price_sell"],
				"stock_status":      product["stock_status"],
				"product_detail_id": product["product_detail_id"],
				"enable_order":      enableOrderByOutlet[utils.ToString(product["outlet_id"])],
			})
			if product["stock_status"] == "available" {
				availability = "available"
			}
		}

		if product["variant_id"] != nil {
			variants = append(variants, utils.MergeMaps(utils.TakesOnly(product, "variant_id", "variant_name", "variant_sku", "variant_barcode", "outlet_id", "outlet_fkid", "price_sell", "stock_status", "product_detail_id", "stock_qty"),
				utils.MapOf("enable_order", enableOrderByOutlet[utils.ToString(product["outlet_id"])])))
		}

		if i == len(products)-1 || product["product_id"] != products[i+1]["product_id"] {
			variantsMod := make([]map[string]interface{}, 0)
			for _, variant := range variants {
				isVariantExist := false
				for _, vm := range variantsMod {
					if strings.TrimSpace(utils.ToString(vm["name"])) == strings.TrimSpace(utils.ToString(variant["variant_name"])) {
						isVariantExist = true
						variantAvailable := vm["available"].([]map[string]interface{})
						variantAvailable = append(variantAvailable, utils.MergeMaps(
							utils.TakesOnly(variant, "outlet_id", "variant_id", "price_sell", "stock_status", "product_detail_id", "enable_order"),
							utils.MapOf("name", outletMap[utils.ToString(variant["outlet_id"])])),
						)
						vm["available"] = variantAvailable
					}
				}

				if !isVariantExist {
					variantsMod = append(variantsMod, map[string]interface{}{
						"name":       variant["variant_name"],
						"variant_id": variant["variant_id"],
						"available": []map[string]interface{}{
							utils.MergeMaps(
								utils.TakesOnly(variant, "outlet_id", "price_sell", "stock_status", "product_detail_id", "enable_order"),
								utils.MapOf("name", outletMap[utils.ToString(variant["outlet_id"])]),
							),
						},
					})
				}
			}

			product["variant"] = variantsMod
			if len(variants) == 0 {
				product["available"] = available
			}

			product["availability"] = availability
			product["price"] = product["price_sell"]
			utils.RemoveField(product, "variant_sku", "variant_name", "variant_id", "variant_barcode", "outlet_fkid", "price_sell", "product_detail_id", "stock_status")
			result = append(result, product)
			variants = make([]map[string]interface{}, 0)
			available = make([]map[string]interface{}, 0)
			availability = "unavailable"
		}
	}

	response := models.ApiResponse{Status: true, Data: result}
	if productId != nil {
		if len(result) > 0 {
			response.Data = result[0]
		} else {
			response.Message = "not found"
			response.Data = map[string]interface{}{}
		}
	}
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetProductWithAvailabilityImproved(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := cast.ToInt(ctx.Request.Header.Peek("mid"))
	productId := ctx.UserValue("id")
	include := string(ctx.QueryArgs().Peek("include"))
	memBefore := memory.Consumed()

	log.Debug("%s : %s - include: %s", string(ctx.Method()), ctx.URI().String(), include)

	if !utils.IsNumber(productId) {
		tmpProdId := utils.Decrypt(utils.ToString(productId), utils.KEY_PRODUCT_ID)
		if utils.IsNumber(tmpProdId) {
			productId = tmpProdId
		}
	}

	reqParam := domain.ProductImprovedRequest{
		TimeOffset: timeOffset,
		MemberId:   memberId,
		AdminId:    cast.ToInt(adminId),
		ProductId:  cast.ToInt(productId),
	}

	var wg sync.WaitGroup

	wg.Add(1)
	var productsModel []models.ProductAvailability

	useV2 := string(ctx.QueryArgs().Peek("v")) == "2"
	ip := ctx.Request.Header.Peek("X-Forwarded-For")
	if useV2 || choose.Between(0.3) || string(ip) == "************" || os.Getenv("ENV") == "development" {
		go func(wg *sync.WaitGroup) {
			defer wg.Done()
			productsModel = fetchProductV2(reqParam)
		}(&wg)
	} else {
		go fetchProduct(reqParam, &productsModel, &wg)
	}

	// wg.Add(1)
	// go db.QueryArrayGo(&wg, outletChan, "SELECT outlet_id, name FROM outlets WHERE admin_fkid = ? and app_show=1 and data_status= 'on' ", adminId)

	wg.Add(1)
	var outletList []models.OutletEntity
	go func(wg *sync.WaitGroup, adminId string, model interface{}) {
		defer wg.Done()
		logTime := log.TimeInit()
		repo := db.Repository{Conn: db.GetConn(), LogQuery: os.Getenv("ENV") == "localhost"}
		repo.Prepare("SELECT outlet_id, name FROM outlets WHERE admin_fkid = ? and app_show=1 and data_status= 'on'", adminId).Get(model)
		logTime.AddLog("log-product, fetch outlet").Print()
	}(&wg, adminId, &outletList)

	sql := `SELECT
	ow.*
FROM
	outlets_workinghour ow
	JOIN outlets o ON o.outlet_id = ow.outlet_fkid
WHERE
	o.admin_fkid = ?
	AND o.app_show = 1
	AND o.data_status = 'on'`

	// wg.Add(1)
	// go db.QueryArrayGo(&wg, workingHoursChan, sql, adminId)

	wg.Add(1)
	var workingHourList []models.WorkingHourEntity
	go func(wg *sync.WaitGroup, sql string, args interface{}, model interface{}) {
		defer wg.Done()
		logTime := log.TimeInit()
		repo := db.Repository{Conn: db.GetConn(), LogQuery: os.Getenv("ENV") == "localhost"}
		repo.Prepare(sql, args).Get(model)
		logTime.AddLog("log-product, fetch working hour").Print()
	}(&wg, sql, adminId, &workingHourList)

	sql = `SELECT csc.subcategory_fkid, csc.position from crm_subcategory_config csc 
	join products_subcategory ps on ps.product_subcategory_id=csc.subcategory_fkid
	where ps.admin_fkid=?`

	// wg.Add(1)
	// go db.QueryArrayGo(&wg, productSubcategoryConfigChan, sql, adminId)

	wg.Add(1)
	var subcategoryConfigs []models.SubcategoryConfigEntity
	go func(wg *sync.WaitGroup, sql string, args interface{}, model interface{}) {
		defer wg.Done()
		logTime := log.TimeInit()
		repo := db.Repository{Conn: db.GetConn(), LogQuery: os.Getenv("ENV") == "localhost"}
		repo.Prepare(sql, args).Get(model)
		logTime.AddLog("log-product, fetch subcategory").Print()
	}(&wg, sql, adminId, &subcategoryConfigs)

	var taxes []models.ProductGratuity
	if strings.ContainsAny(include, "tax") {
		wg.Add(1)
		go fetchTaxes(&taxes, cast.ToInt(productId), cast.ToInt(adminId), &wg)
	}

	wg.Wait()

	dateNow := time.Now().Add(7 * time.Hour)
	today := strings.ToLower(dateNow.Weekday().String())
	tNow, err := time.Parse("15:4:5", fmt.Sprintf("%d:%d:%d", dateNow.Hour(), dateNow.Minute(), dateNow.Day()))
	log.IfError(err)

	taxByProduct := make(map[int][]models.GratuityEntity)
	for _, tax := range taxes {
		if _, ok := taxByProduct[tax.ProductDetailFkid]; !ok {
			taxByProduct[tax.ProductDetailFkid] = make([]models.GratuityEntity, 0)
		}
		taxByProduct[tax.ProductDetailFkid] = append(taxByProduct[tax.ProductDetailFkid], tax.GratuityEntity)
	}

	// workingHours := <-workingHoursChan
	outletMap := make(map[string]string)
	enableOrderByOutlet := make(map[string]models.EnableOrder)
	for _, outlet := range outletList {
		outletMap[utils.ToString(outlet.OutletID)] = outlet.Name

		isEnableOrder := true
		for _, wh := range workingHourList {
			if wh.OutletFkid == outlet.OutletID {
				isEnableOrder = false
				if wh.Day == today {
					tOpen, _ := time.Parse("15:04:05", wh.TimeOpen)
					tClose, _ := time.Parse("15:04:05", wh.TimeClose)
					if tNow.After(tOpen) && tNow.Before(tClose) {
						isEnableOrder = true
					}
					break
				}
			}
		}

		enableOrder := models.EnableOrder{Status: "enable"}

		if !isEnableOrder {
			enableOrder = models.EnableOrder{
				Status: "disable",
				Reason: "closed",
			}
		}

		enableOrderByOutlet[utils.ToString(outlet.OutletID)] = enableOrder
	}

	subcategoryConfMap := make(map[int]models.SubcategoryConfigEntity)
	for _, config := range subcategoryConfigs {
		subcategoryConfMap[config.SubcategoryFkid] = config
	}

	result := make([]models.ProductAvailability, 0)
	variants := make([]map[string]interface{}, 0)
	available := make([]models.Available, 0)
	availability := "unavailable"

	log.Info("sizeOf productsModel %v", len(productsModel))
	for i, product := range productsModel {
		if product.Photo == "" {
			// product["photo"] = utils.DEFAULT_IMAGE
		} else if !strings.HasPrefix(utils.ToString(product.Photo), "https://") {
			product.Photo = fmt.Sprintf("%s/assets/images/products/%s/%s", os.Getenv("base_url"), adminId, product.Photo)
		}

		product.SubcategoryPosition = 0
		if conf, ok := subcategoryConfMap[product.ProductSubcategoryID]; ok {
			product.SubcategoryPosition = conf.Position
		}

		if i == len(productsModel)-1 || product.ProductID != productsModel[i+1].ProductID || product.OutletFkid != productsModel[i+1].OutletFkid {
			available = append(available, models.Available{
				EnableOrder:     enableOrderByOutlet[utils.ToString(product.OutletID)],
				Name:            outletMap[utils.ToString(product.OutletFkid)],
				OutletID:        product.OutletFkid,
				PriceSell:       product.PriceSell,
				ProductDetailID: product.ProductDetailID,
				StockStatus:     product.StockStatus,
				StockQty:        product.StockQty,
			})

			if product.StockStatus == "available" {
				availability = "available"
			}
		}

		if product.VariantID > 0 {
			variants = append(variants, utils.MergeMaps(utils.TakesOnly(product.ToMap(), "variant_id", "variant_name", "variant_sku", "variant_barcode", "outlet_id", "outlet_fkid", "price_sell", "stock_status", "product_detail_id", "stock_qty"),
				utils.MapOf("enable_order", enableOrderByOutlet[utils.ToString(product.OutletID)])))
		}

		if i == len(productsModel)-1 || product.ProductID != productsModel[i+1].ProductID {
			variantsMod := make([]models.ProductVariant, 0)
			for _, variant := range variants {
				isVariantExist := false
				for _, vm := range variantsMod {
					if strings.TrimSpace(utils.ToString(vm.Name)) == strings.TrimSpace(utils.ToString(variant["variant_name"])) {
						isVariantExist = true
						enableOrder := variant["enable_order"].(models.EnableOrder)
						vm.Available = append(vm.Available, models.Available{
							EnableOrder:     enableOrder,
							Name:            outletMap[utils.ToString(variant["outlet_id"])],
							OutletID:        cast.ToInt(variant["outlet_id"]),
							PriceSell:       cast.ToInt(variant["price_sell"]),
							ProductDetailID: cast.ToInt(variant["product_detail_id"]),
							StockStatus:     cast.ToString(variant["stock_status"]),
							StockQty:        cast.ToInt(variant["stock_qty"]),
						})
					}
				}

				if !isVariantExist {
					enableOrder := variant["enable_order"].(models.EnableOrder)
					variantsMod = append(variantsMod, models.ProductVariant{
						Available: []models.Available{
							{
								EnableOrder:     enableOrder,
								Name:            outletMap[utils.ToString(variant["outlet_id"])],
								OutletID:        cast.ToInt(variant["outlet_id"]),
								PriceSell:       cast.ToInt(variant["price_sell"]),
								ProductDetailID: cast.ToInt(variant["product_detail_id"]),
								StockStatus:     cast.ToString(variant["stock_status"]),
								StockQty:        cast.ToInt(variant["stock_qty"]),
							},
						},
						Name:      cast.ToString(variant["variant_name"]),
						VariantID: cast.ToInt(variant["variant_id"]),
					})
				}
			}

			product.Variant = variantsMod
			if len(variants) == 0 {
				product.Available = available
			}

			product.Availability = availability
			product.Price = product.PriceSell

			if tax, ok := taxByProduct[product.ProductDetailID]; ok {
				product.Tax = tax
			}
			result = append(result, product)

			//reseting
			variants = make([]map[string]interface{}, 0)
			available = make([]models.Available, 0)
			availability = "unavailable"
		}
	}

	response := models.ApiResponse{Status: true, Data: result}
	if productId != nil {
		if len(result) > 0 {
			response.Data = result[0]
		} else {
			response.Message = "not found"
			response.Data = map[string]interface{}{}
		}
	}

	memAfter := memory.Consumed()
	fmt.Printf("total memory: %.3fkb\n", float64(memAfter-memBefore))
	_ = json.NewEncoder(ctx).Encode(response)
}

func fetchProductV2(param domain.ProductImprovedRequest) []models.ProductAvailability {
	cacheImplement := cache.NewCacheDbRedis(db.GetRedisClient())

	log.Info("------------ fetchProductV2 -----------")
	sql := `
select product_id,
       p.name,
       photo,
       barcode,
       p.min_qty_order,
       pd.price_sell,
       pd.outlet_fkid,
       pd.outlet_fkid as outlet_id, 
       pd.product_detail_id,
       ps.product_subcategory_id,
       ps.name                                                                   as subcategory,
       u.name                                                                    as unit,
       u.description                                                             as unit_description,
       p.description,
	   pd.stock_qty,
       IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', pd.stock) as availability, 
	   if(p.stock_management = 1, if(pd.stock_qty <= 0 OR pd.stock_qty < p.min_qty_order, 'unavailable', 'available'), pd.stock) as stock_status,	   
	   IF(p.stock_management = 1 and pd.stock_qty < p.min_qty_order, 'unavailable', pd.stock) as stock_status_v1, 
       pdv.variant_barcode, pdv.variant_id, pdv.variant_sku, pdv.variant_name
		$select
from products p
         join products_detail pd on p.product_id = pd.product_fkid
         join outlets o on pd.outlet_fkid = o.outlet_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         join unit u on p.unit_fkid = u.unit_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id    
		$join
where p.admin_fkid = ?
  and p.app_show = 1
  and o.app_show = 1
  and p.data_status = 'on'
  and o.data_status = 'on'
  and (pd.active = 'on_all' or pd.active = 'on_sales')
  and pd.data_status = 'on'
  $where_cond `

	args := utils.ArrayOf() //param.TimeOffset, param.TimeOffset, param.TimeOffset

	fmt.Println(">> memberId: ", param.MemberId)
	// if param.MemberId > 0 {
	// 	sql = strings.Replace(sql, "$select", ",if((cpw.crm_product_wishlist_id) > 0, true, false) as is_in_wishlist", 1)
	// 	sql = strings.Replace(sql, "$join", "left join crm_product_wishlist cpw on cpw.product_fkid=p.product_id and cpw.member_fkid = ?", 1)
	// 	args = append(args, param.MemberId)
	// } else {
	// 	sql = strings.Replace(sql, "$select", ", false as is_in_wishlist", 1)
	// }

	args = append(args, param.AdminId)

	if param.ProductId > 0 {
		sql = strings.Replace(sql, "$where_cond", " and p.product_id = ? ", 1)
		args = append(args, param.ProductId)
	} else {
		// sql = strings.Replace(sql, "$where_cond", " and if(cpac.total_available > 0, cpaa.hour_start is not null, true)  ", 1)
	}

	sql = strings.Replace(sql, "$where_cond", "", 1)
	sql = strings.Replace(sql, "$select", "", 1)
	sql = strings.Replace(sql, "$join", "", 1)

	cacheKey := fmt.Sprintf("%v-product-v2-%s:%v", os.Getenv("ENV"), utils.ConcatArgs(args), param.MemberId)
	productCache, err := cacheImplement.Get(cacheKey)

	if err != nil && !(err == redis.Nil || strings.Contains(err.Error(), "unknown port")) {
		log.IfError(err)
	}
	if err == nil && productCache != "" {
		var result []models.ProductAvailability
		if !log.IfError(json.Unmarshal([]byte(productCache), &result)) {
			return result
		}
	}

	logTime := log.TimeInit()
	repo := db.Repository{Conn: db.GetConn(), LogQuery: os.Getenv("ENV") == "localhost"}

	var wgLocal sync.WaitGroup

	dbRepo := repo.Prepare(sql, args...)
	var productsModel []models.ProductAvailability

	wgLocal.Add(1)
	go func(dbRepo *db.Repository, productsModel *[]models.ProductAvailability, wg *sync.WaitGroup) {
		defer wg.Done()
		logTime := log.TimeInit()

		err := dbRepo.Get(productsModel)
		log.IfError(err)
		logTime.AddLog("log-product, fetch product v2 (only)").Print()
	}(dbRepo, &productsModel, &wgLocal)

	//load availability product
	availableProducts := make(map[int]models.ProductAvailableEntity)
	wgLocal.Add(1)
	go func(param domain.ProductImprovedRequest, wg *sync.WaitGroup) {
		defer wg.Done()
		logTime := log.TimeInit()
		sql := `SELECT cpa.product_fkid, hour_start, hour_end 
		from crm_products_available cpa
		join products p on p.product_id=cpa.product_fkid
		 where p.admin_fkid= ? and day = ? `

		day := strings.ToLower(time.Now().Weekday().String())
		rows, err := db.GetConn().Query(sql, param.AdminId, day)
		if log.IfError(err) {
			return
		}

		defer rows.Close()
		for rows.Next() {
			var available models.ProductAvailableEntity
			err = rows.Scan(&available.ProductFkid, &available.HourStart, &available.HourEnd)
			if err != nil {
				break
			}
			availableProducts[available.ProductFkid] = available
		}
		logTime.AddLog("fetchProductV2, load availability product").Print()
	}(param, &wgLocal)

	//load total availability setting per-product
	productsAvailabilitySet := make(map[int]bool)
	wgLocal.Add(1)
	go func(param domain.ProductImprovedRequest, wg *sync.WaitGroup) {
		defer wg.Done()
		logTime := log.TimeInit()

		query := `SELECT
		product_fkid
	FROM
		crm_products_available cpa
		JOIN products p ON p.product_id = cpa.product_fkid
		where p.admin_fkid=?
	GROUP BY
		product_fkid`

		rows, err := db.GetConn().Query(query, param.AdminId)
		if log.IfError(err) {
			return
		}

		defer rows.Close()
		for rows.Next() {
			var id int
			err = rows.Scan(&id)
			if err != nil {
				break
			}
			productsAvailabilitySet[id] = true
		}
		logTime.AddLog("fetchProductV2, load total availability setting").Print()
	}(param, &wgLocal)

	productWishlistMap := make(map[int]bool)
	if param.MemberId > 0 {
		wgLocal.Add(1)
		go func(param domain.ProductImprovedRequest, wg *sync.WaitGroup) {
			defer wg.Done()
			query := `SELECT product_fkid from crm_product_wishlist where member_fkid=?`

			rows, err := db.GetConn().Query(query, param.MemberId)
			if log.IfError(err) {
				return
			}

			defer rows.Close()
			for rows.Next() {
				var id int
				err = rows.Scan(&id)
				if err != nil {
					break
				}
				productWishlistMap[id] = true
			}
		}(param, &wgLocal)
	}

	wgLocal.Wait()
	logTime.AddLog("log-product, fetch product v2 (all)").Print()

	if len(productsModel) == 0 {
		return []models.ProductAvailability{}
	}

	result := make([]models.ProductAvailability, 0)
	for _, product := range productsModel {
		//check availability
		isAvailable := true
		if hours, ok := availableProducts[product.ProductID]; ok {
			isAvailable = istime.Between(hours.HourStart, hours.HourEnd, 7)
		} else {
			isAvailable = !productsAvailabilitySet[product.ProductID]
		}
		if isAvailable {
			product.HourStart = utils.TakeIf(product.HourStart != "", product.HourStart, "00:00:00").(string)
			product.HourEnd = utils.TakeIf(product.HourEnd != "", product.HourEnd, "23:59:00").(string)
			product.IsInWishlist = productWishlistMap[product.ProductID]
			result = append(result, product)
		}
	}

	//sort data
	sort.Slice(result, func(i, j int) bool {
		return (result)[i].ProductID < (result)[j].ProductID
	})

	logTime.AddLog("sort product").Print()

	//save to cache
	cacheImplement.Set(cacheKey, utils.SimplyToJson(result), 35*time.Minute)
	return result
}

// needs much improvement
func fetchProduct(param domain.ProductImprovedRequest, productsModel *[]models.ProductAvailability, wg *sync.WaitGroup) {
	defer wg.Done()
	log.Info("------------ fetchProduct v1 -----------")
	sql := `
select product_id,
       p.name,
       photo,
       barcode,
       p.min_qty_order,
       pd.price_sell,
       pd.outlet_fkid,
       pd.outlet_fkid as outlet_id, 
       pd.product_detail_id,
       ps.product_subcategory_id,
       ps.name                                                                   as subcategory,
       u.name                                                                    as unit,
       u.description                                                             as unit_description,
       p.description,
       IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', pd.stock) as availability, 
	   if(p.stock_management = 1, if(pd.stock_qty <= 0 OR pd.stock_qty < p.min_qty_order, 'unavailable', 'available'), pd.stock) as stock_status,
	   IF(p.stock_management = 1 and pd.stock_qty < p.min_qty_order, 'unavailable', pd.stock) as stock_status_v1, 
	   if(pd.stock = 'unavailable', pd.stock,
          if((p.app_show = 0 or cpac.total_available > 0) and cpaa.hour_start is null,
             'unavailable', 'available'))                                            as stock_status_old,

       pdv.*,
       cpac.total_available,
       coalesce(cpaa.hour_start, '00:00:00')                                     as hour_start,
       coalesce(cpaa.hour_end, '23:59:59')                                       as hour_end
		$select
from products p
         join products_detail pd on p.product_id = pd.product_fkid
         join outlets o on pd.outlet_fkid = o.outlet_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         join unit u on p.unit_fkid = u.unit_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
         left join (select count(*) total_available, product_fkid
                    from crm_products_available					
                    group by product_fkid) cpac
                   on cpac.product_fkid = p.product_id
         left join (select product_fkid, day, hour_start, hour_end
                    from crm_products_available
                    where hour_start <= from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and hour_end > from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and convert(day USING utf8)=(lower(dayname(from_unixtime(unix_timestamp() + ?))))) cpaa
                   on cpaa.product_fkid = p.product_id
		$join
where p.admin_fkid = ?
  and p.app_show = 1
  and o.app_show = 1
  and p.data_status = 'on'
  and o.data_status = 'on'
  and (pd.active = 'on_all' or pd.active = 'on_sales')
  and pd.data_status = 'on'
  $where_cond `

	args := utils.ArrayOf(param.TimeOffset, param.TimeOffset, param.TimeOffset)

	fmt.Println(">> memberId: ", param.MemberId)
	if param.MemberId > 0 {
		sql = strings.Replace(sql, "$select", ",if((cpw.crm_product_wishlist_id) > 0, true, false) as is_in_wishlist", 1)
		sql = strings.Replace(sql, "$join", "left join crm_product_wishlist cpw on cpw.product_fkid=p.product_id and cpw.member_fkid = ?", 1)
		args = append(args, param.MemberId)
	} else {
		sql = strings.Replace(sql, "$select", ", false as is_in_wishlist", 1)
	}

	args = append(args, param.AdminId)

	if param.ProductId > 0 {
		sql = strings.Replace(sql, "$where_cond", " and p.product_id = ? ", 1)
		args = append(args, param.ProductId)
	} else {
		sql = strings.Replace(sql, "$where_cond", " and if(cpac.total_available > 0, cpaa.hour_start is not null, true)  ", 1)
	}

	sql = strings.Replace(sql, "$where_cond", "", 1)
	sql = strings.Replace(sql, "$select", "", 1)
	sql = strings.Replace(sql, "$join", "", 1)

	cacheKey := fmt.Sprintf("%v-product-v1-%s", os.Getenv("ENV"), utils.ConcatArgs(args))
	cacheImplement := cache.NewCacheDbRedis(db.GetRedisClient())
	productCache, err := cacheImplement.Get(cacheKey)
	log.Info("%v - getCacheErr: %v, size cache: %v", cacheKey, err, len(productCache))
	if err != nil && !(err == redis.Nil || strings.Contains(err.Error(), "unknown port")) {
		log.IfError(err)
	}
	if err == nil && productCache != "" {
		if !log.IfError(json.Unmarshal([]byte(productCache), &productsModel)) {
			if productsModel != nil {
				log.Info("use cache... size %v, cahceSize: %v", len(*productsModel), len(productCache))
				return
			}
		}
	}

	logTime := log.TimeInit()
	repo := db.Repository{Conn: db.GetConn(), LogQuery: os.Getenv("ENV") == "localhost"}

	repo.Prepare(sql, args...).Get(productsModel)
	logTime.AddLog("log-product, fetch product")

	if productsModel != nil && len(*productsModel) > 0 {
		sort.Slice(*productsModel, func(i, j int) bool {
			return (*productsModel)[i].ProductID < (*productsModel)[j].ProductID
		})
	}

	//save to cache
	cacheImplement.Set(cacheKey, utils.SimplyToJson(productsModel), 35*time.Minute)

	logTime.AddLog("sort product").Print()
}

func fetchTaxes(taxes *[]models.ProductGratuity, productId, adminId int, wg *sync.WaitGroup) {
	defer wg.Done()
	sql := `SELECT pdt.product_detail_fkid,
	g.gratuity_id,
	g.name,
	tax_category,
	tax_status,
	tax_type,
	jumlah
FROM   products_detail_taxdetail pdt
	JOIN gratuity g
	  ON pdt.tax_fkid = g.gratuity_id
	$JOIN
WHERE  g.data_status = 'on'
	AND pdt.data_status= 'on'
	AND g.admin_fkid = ?
	AND g.tax_status = 'permanent'
	$WHERE `

	whereSql, joinSql := "", ""
	args := make([]interface{}, 0)
	args = append(args, adminId)
	if productId > 0 {
		whereSql = " AND pd.product_fkid = ? "
		args = append(args, productId)

		joinSql = " JOIN products_detail pd ON pd.product_detail_id = pdt.product_detail_fkid "
	}
	sql = strings.Replace(sql, "$WHERE", whereSql, 1)
	sql = strings.Replace(sql, "$JOIN", joinSql, 1)

	logTime := log.TimeInit()
	repo := db.Repository{Conn: db.GetConn(), LogQuery: os.Getenv("ENV") == "localhost"}
	err := repo.Prepare(sql, args...).Get(taxes)
	log.IfError(err)
	logTime.AddLog("log-product, fetch taxes")

	if taxes != nil && len(*taxes) > 0 {
		sort.Slice(*taxes, func(i, j int) bool {
			return (*taxes)[i].ProductDetailFkid < (*taxes)[j].ProductDetailFkid
		})
	}

	logTime.AddLog("sort tax").Print()
}

// func fetchTaxesImproved(taxes *[]models.ProductGratuity, productId, adminId int, wg *sync.WaitGroup) {

// }
