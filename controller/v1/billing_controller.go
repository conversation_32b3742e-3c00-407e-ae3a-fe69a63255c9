package v1

import (
	"encoding/json"
	"os"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func GetSubscriptionStatus(ctx *fasthttp.RequestCtx) {
	if os.Getenv("ENV") != "production" {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
			"status": "available",
		}})
		return
	}

	adminId := string(ctx.Request.Header.Peek("uid"))

	sql := `SELECT id 
FROM   system_subscribe
WHERE  admin_fkid = ?
       AND service_time_start < Unix_timestamp() * 1000
       AND service_time_expired > Unix_timestamp() * 1000
       AND feature = 'crm' `
	subscriptions, err := db.Query<PERSON><PERSON>y(sql, adminId)
	log.IfError(err)

	status := "unavailable"
	if len(subscriptions) > 0 {
		status = "available"
	}

	log.Info("subscription status of admin: '%s' is %s --> id subscription: %v", adminId, status, subscriptions)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"status": status,
	}})
}
