package v1

import (
	"encoding/json"
	"fmt"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

const regionUrl string = "https://uniq-187911.web.app"

func GetProvince(ctx *fasthttp.RequestCtx) {
	country := ctx.UserValue("country")
	adminId := ctx.Request.Header.Peek("uid")

	if utils.ToString(country) != "indonesia" {
		return
	}

	req := utils.HttpRequest{
		Method: "GET",
		Url:    fmt.Sprintf("%s/provinsi.json", regionUrl),
	}
	resp, err := req.ExecuteRequest()

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	result := make([]models.Province, 0)
	err = json.Unmarshal(resp, &result)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//filter based on outlet location
	outletLocations := fetchOutletLocation(cast.ToInt(adminId))
	if len(outletLocations) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
		return
	}

	//filter
	resultFilter := make([]models.Province, 0)
	for _, location := range outletLocations {
		for _, province := range result {
			if location == province.Nama {
				resultFilter = append(resultFilter, province)
				break
			}
		}
	}

	//in case data not found, return all
	if len(resultFilter) == 0 {
		resultFilter = result
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: resultFilter})
}

func fetchOutletLocation(adminId int) []string {
	if adminId == 10 {
		return []string{"DI YOGYAKARTA", "JAWA TENGAH", "DKI JAKARTA", "JAWA TIMUR", "JAWA BARAT"}
	}
	return []string{}
}

func GetKabupaten(ctx *fasthttp.RequestCtx) {
	country := ctx.UserValue("country")
	provinceId := utils.ToString(ctx.UserValue("provinceId"))
	result := make([]map[string]interface{}, 0)

	if utils.ToString(country) != "indonesia" {
		return
	}

	if provinceId == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		log.Warn("request data kabupaten with empty province id")
		return
	}

	req := utils.HttpRequest{
		Method: "GET",
		Url:    fmt.Sprintf("%s/kabupaten/%s.json", regionUrl, provinceId),
	}
	resp, err := req.Execute()

	if resp.StatusCode == 404 {
		log.Warn("no data kab found with prov id %s", provinceId)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "no data kabupaten found with province id : " + provinceId, Data: result})
		return
	}

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	err = json.Unmarshal([]byte(resp.Body), &result)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
