package v1

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func GetInbox(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	page := ctx.UserValue("page")
	inboxId := ctx.UserValue("id")

	sql := `SELECT notification_id, title, message, is_read, data_created, notification_type, notification_data
from system_notification
WHERE receiver_id = @memberId
  and admin_fkid = @adminId
  $WHERE 
ORDER BY data_created DESC `

	whereQuery := ""
	if inboxId != nil {
		whereQuery = " and notification_id = @notifId "
	}

	sql = strings.Replace(sql, "$WHERE", whereQuery, 1)

	if page != nil {
		MAX := 15
		pageInt := utils.ToInt(page)
		if pageInt == 0 {
			pageInt = 1
		}
		offset := MAX * (pageInt - 1)
		sql += fmt.Sprintf(" LIMIT %d OFFSET %d", MAX, offset)
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"memberId": memberId,
		"adminId":  adminId,
		"notifId":  inboxId,
	})

	data, err := db.QueryArray(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	for _, inbox := range data {
		if inbox["notification_data"] != nil && len(utils.ToString(inbox["notification_data"])) > 7 {
			var notificationData map[string]interface{}
			err = json.Unmarshal([]byte(utils.ToString(inbox["notification_data"])), &notificationData)
			if !log.IfError(err) {
				inbox["notification_data"] = notificationData
			}
		}
	}

	if inboxId != nil && len(data) > 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data[0]})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data})
}

func CountUnreadInbox(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	data, err := db.Query("SELECT count(*) as unread from system_notification WHERE receiver_id = ? and admin_fkid = ? and is_read = 0", memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data["unread"]})
}

func UpdateInboxStatus(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	notifId := string(ctx.PostArgs().Peek("notif_id"))

	res, err := db.Update("system_notification", map[string]interface{}{
		"is_read": 1,
	}, "notification_id = ? and receiver_id = ?", notifId, memberId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	msg := "success"
	rows, _ := res.RowsAffected()
	if rows <= 0 {
		msg = "no data effected"
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: msg})
}
