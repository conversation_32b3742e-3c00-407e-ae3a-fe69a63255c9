package v1

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/payment/midtrans"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

// GetPaymentInfoForSimulation for simulation purpose
func GetPaymentInfoForSimulation(ctx *fasthttp.RequestCtx) {
	if os.Getenv("server") == "production" {
		ctx.SetStatusCode(fasthttp.StatusNotFound)
		return
	}

	orderId := ctx.UserValue("orderId")
	paymentNotification, err := db.Query("select transaction_id from payment_notification where order_id = ? order by payment_notification_id desc limit 1", orderId)
	log.IfError(err)

	ctx.SetContentType("application/json")
	if len(paymentNotification) > 0 {
		urlQr := midtrans.TmpQrUrl[utils.ToString(orderId)]
		if urlQr == "" {
			//urlQr := fmt.Sprintf("https://api.sandbox.veritrans.co.id/v2/qris/%s/qr-code", paymentNotification["transaction_id"])
			urlQr = fmt.Sprintf("https://api.sandbox.veritrans.co.id/v2/qris/shopeepay/sppq_%s/qr-code", paymentNotification["transaction_id"])
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "to verify, go to: https://simulator.sandbox.midtrans.com/qris/index", Data: urlQr})
	} else {
		ctx.SetStatusCode(fasthttp.StatusNotFound)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: fmt.Sprintf("no payment found for: %s", orderId)})
	}
}

// NO LONGER USED:
func HandlePaymentNotification(ctx *fasthttp.RequestCtx) {
	log.Info("receive payment pubsub: %s", string(ctx.PostBody()))

	//handle from pubsub
	var pubsub models.PubSub
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&pubsub)
	if log.IfError(err) {
		return
	}

	data, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	if log.IfError(err) {
		return
	}

	log.Info("pubsub receive data: %s", string(data))
	var payload map[string]interface{}
	if log.IfError(json.Unmarshal(data, &payload)) {
		return
	}

	log.Info("receive from pubsub, payload: %v", payload)
	fmt.Println("payload data type: ", reflect.TypeOf(payload["payload"]))
	if payloadData, ok := payload["payload"].(map[string]interface{}); ok {
		orderId := strings.TrimSpace(cast.ToString(payloadData["order_id"]))
		log.Info("order id: '%s'", orderId)
		if orderId != "" {
			HandlePaymentChange(orderId)
		}

	}
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func HandlePaymentChange(orderId string) {
	log.Info("handle payment change of: '%s'", orderId)
	if !strings.HasPrefix(orderId, PrefixId) {
		log.Info("'%s' is not id format for order", orderId)
	}

	order, err := db.Query("select status from order_sales where order_sales_id = ?", orderId)
	log.IfError(err)
	if len(order) == 0 {
		log.Info("no order_sales with id: %s", orderId)
		return
	}

	if cast.ToString(order["status"]) == "payment_verified" {
		log.Info("order for '%s' already verified the payment", orderId)
		return
	}

	//get payment notification
	payment, err := db.Query("select transaction_status, info from payment_notification where order_id = ? order by data_created desc limit 1", orderId)
	if log.IfError(err) {
		return
	}

	log.Info("transaction status of '%s' is : '%v'", orderId, payment["transaction_status"])
	if strings.ToLower(cast.ToString(payment["transaction_status"])) == "paid" {
		payment["transaction_status"] = "settlement"
	}

	if payment["transaction_status"] != "settlement" {
		var transactionInfo map[string]interface{}
		err = json.Unmarshal([]byte(cast.ToString(payment["info"])), &transactionInfo)
		log.IfError(err)

		log.Info("transaction status from info: %v", transactionInfo["transaction_status"])
		if transactionInfo["transaction_status"] != nil {
			payment["transaction_status"] = transactionInfo["transaction_status"]
		}
	}

	if payment["transaction_status"] == "settlement" {
		sql := `
select os.id, o.admin_fkid, md.firebase_token, md.member_fkid
from order_sales os
         join outlets o on os.outlet_fkid = o.outlet_id
         join members_detail md on os.member_fkid = md.member_fkid and o.admin_fkid = md.admin_fkid
where os.order_sales_id = ? `
		order, err := db.Query(sql, orderId)
		log.IfError(err)

		_, err = db.Update("order_sales", map[string]interface{}{
			"status": "payment_verified",
		}, "order_sales_id = ?", orderId)
		log.IfError(err)

		_, err = db.Insert("order_sales_status_log", map[string]interface{}{
			"order_sales_id":   orderId,
			"order_sales_fkid": order["id"],
			"status":           "payment_verified",
			"admin_id":         order["admin_fkid"],
			"time_created":     time.Now().Unix() * 1000,
		})
		log.IfError(err)

		//publish using pubsub
		//for now, lets direct call api
		//resp, err := utils.HttpRequest{
		//	Method: "POST",
		//	Url:    "http://api-pos:8000/v1/add_sales/order_sales",
		//	PostRequest: utils.PostRequest{Form: map[string]string{
		//		"order_sales_id": orderId,
		//	}},
		//}.Execute()
		//log.IfError(err)
		//log.Info("sending to api-pos resp : %v", resp)
		PublishOrderStatusUpdate(orderId, "payment_verified")

		SendNotification(models.UserNotification{
			Title:             "Pembayaran Terverifikasi",
			Message:           fmt.Sprintf("Pembayaran untuk pesanan %s telah diverifikasi", orderId),
			NotificationToken: utils.ToString(order["firebase_token"]),
			NotificationType:  "order_online",
			UserId:            utils.ToString(order["member_fkid"]),
			AdminId:           utils.ToInt(order["admin_fkid"]),
			NotificationData: map[string]interface{}{
				"order_sales_id": order["order_sales_id"],
			},
		})
	}
}

func PublishOrderStatusUpdate(orderId string, status string) {
	data := map[string]interface{}{
		"order_sales_id": orderId,
		"status":         status,
	}
	env := os.Getenv("server")
	if env == "demo" {
		env = "staging"
	}
	topicId := fmt.Sprintf("crm-order-status-%s", env)
	log.IfError(google.Publish(topicId, data))
}

func AddToFirebase(orderId string, creator interface{}) {
	sql := `select os.id, os.order_sales_id, o.admin_fkid, os.member_fkid, os.status , os.outlet_fkid 
from order_sales os
join outlets o on o.outlet_id=os.outlet_fkid 
 where order_sales_id = ?`
	order, err := db.Query(sql, orderId)
	if log.IfError(err) || len(order) == 0 {
		return
	}

	totalLogData, err := db.Query("select count(*) as total from order_sales_status_log where order_sales_id = ?", orderId)
	log.IfError(err)
	totalLog := utils.ToInt(totalLogData["total"])

	ctx := context.Background()
	app := firebase.GetFirebaseApp()
	if app == nil {
		log.Info("firebase app is not initialized")
		return
	}
	dbClient, err := app.Database(ctx)
	if log.IfError(err) {
		return
	}

	env := os.Getenv("server")
	path := fmt.Sprintf("%s/crm_transaction/%s/", env, utils.ToString(order["admin_fkid"]))
	log.Info("path: %s", path)

	action := "update"
	if utils.ToString(order["status"]) == "pending" && totalLog <= 1 {
		action = "insert"
	}

	if creator == nil {
		creator = fmt.Sprintf("member:%s", utils.ToString(order["member_fkid"]))

		//'pending','cancel','accept','reject','payment_verification','payment_verified','payment_reject','ready','taken','arrived','received','complaint'
		statusByAdmin := []string{"reject", "accept", "payment_verified", "arrived"}
		for _, statusAdmin := range statusByAdmin {
			if utils.ToString(order["status"]) == statusAdmin {
				creator = fmt.Sprintf("admin:%s", utils.ToString(order["admin_fkid"]))
				break
			}
		}
	}

	_, err = dbClient.NewRef(path).Push(ctx, map[string]interface{}{
		"id":           utils.ToString(order["id"]),
		"invoice_id":   order["order_sales_id"],
		"status":       order["status"],
		"action":       action,
		"time_created": time.Now().Unix() * 1000,
		"creator":      creator,
		"outlet_id":    cast.ToString(order["outlet_fkid"]),
	})
	log.IfError(err)
}
