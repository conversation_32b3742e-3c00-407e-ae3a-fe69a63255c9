package v1

import (
	"encoding/json"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func GetAppSetting(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))

	data, err := db.Query("SELECT * FROM setting_app_mobile WHERE admin_fkid = ? LIMIT 1", adminId)
	utils.CheckErr(err)

	utils.RemoveField(data, "admin_fkid", "setting_app_mobile_id", "data_created", "data_modified")
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data})
}
