package v1

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/language"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/messaging"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/generate"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/user/usecase"
)

type UserControllerHandler struct {
	ucPromotion promotion.UseCase
}

func NewUserControllerHandler(promo promotion.UseCase) *UserControllerHandler {
	return &UserControllerHandler{
		ucPromotion: promo,
	}
}

func CheckExistingUserByPhone(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	phone := string(ctx.QueryArgs().Peek("phone"))
	email := string(ctx.QueryArgs().Peek("email"))
	// phoneWithCode := string(ctx.QueryArgs().Peek("phone"))
	var resp models.ApiResponse

	phone = utils.FormatPhoneNumber(phone)

	if phone == utils.DemoAccountPhone {
		resp.Code = 54
		resp.Status = true
		resp.Message = "this is demo account"
		log.Info("phone '%v' is demo account!", phone)
		_ = json.NewEncoder(ctx).Encode(resp)
		return
	}

	sql := `SELECT GROUP_CONCAT(phone) as phone, GROUP_CONCAT(email) as email, GROUP_CONCAT(member_id) as member_id FROM members WHERE phone = @phone `
	if email != "" {
		sql += ` or email = @email `
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"phone": phone,
		"email": email,
	})
	member, err := db.Query(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(member) > 0 && (member["email"] != nil || member["phone"] != nil) {
		//check member status
		sql = `SELECT status FROM members_detail where member_fkid=? and admin_fkid=? `
		memberDetail, err := db.Query(sql, member["member_id"], adminId)
		log.Info("member status %v at %v --> %v", member["member_id"], adminId, memberDetail)
		if !log.IfError(err) && (cast.ToInt(memberDetail["status"]) == 2) {
			resp.Code = 52
			resp.Message = "Member account is banned!"
			_ = json.NewEncoder(ctx).Encode(resp)
			return
		}

		utils.RemoveField(member, "admin_fkid", "password")
		resp.Code = 1
		resp.Status = true
		respDetail := map[string]string{
			"phone": "unused",
			"email": "unused",
		}
		used := make([]string, 0)
		if email != "" && strings.Contains(utils.ToString(member["email"]), email) {
			respDetail["email"] = "used"
			used = append(used, "email")
		}
		if phone != "" && strings.Contains(utils.ToString(member["phone"]), phone) {
			respDetail["phone"] = "used"
			used = append(used, "phone")
		}

		resp.Message = strings.Join(used, ",") + " already used"
		resp.Data = respDetail
	} else {
		resp.Message = "user not found"
	}

	headers := make(map[string]interface{})
	ctx.Request.Header.VisitAll(func(key, value []byte) {
		headers[string(key)] = string(value)
	})

	log.Info("phone '%s' : %s | header: %s", phone, resp.Message, utils.SimplyToJson(headers))
	_ = json.NewEncoder(ctx).Encode(resp)
}

func (u *UserControllerHandler) RegisterWithPhone(ctx *fasthttp.RequestCtx) {
	ctx.SetContentType("application/json")

	// adminId := string(ctx.Request.Header.Peek("uid"))
	email := string(ctx.PostArgs().Peek("email"))
	name := string(ctx.PostArgs().Peek("name"))
	gender := string(ctx.PostArgs().Peek("gender"))
	dateOfBirth := string(ctx.PostArgs().Peek("date_of_birth"))
	province := string(ctx.PostArgs().Peek("province"))
	city := string(ctx.PostArgs().Peek("city"))
	address := string(ctx.PostArgs().Peek("address"))
	additionalData := ctx.PostArgs().Peek("additional_data")
	idToken := ctx.PostArgs().Peek("id_token")
	pubKey := string(ctx.Request.Header.Peek("Public-Key"))
	adminFkid := utils.Decrypt(pubKey, utils.UID_PUBKEY)
	phone := string(ctx.PostArgs().Peek("phone"))

	log.Info("#Register - Name : %s | Email : %s", name, email)
	log.Info("#DATA : %s", ctx.Request.PostArgs().String())
	log.Info("user agent: %s", ctx.UserAgent())

	headers := make(map[string]string)
	ctx.Request.Header.VisitAll(func(key, value []byte) {
		headers[string(key)] = string(value)
	})
	log.Info("header: %v", utils.SimplyToJson(headers))

	//"gender", "date_of_birth",
	if status, msg := utils.IsValidPostInput(ctx, "email", "name", "id_token"); !status {
		log.Debug(msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	if gender != "0" && gender != "1" {
		gender = ""
	}

	log.Info("additional data : '%s'", string(additionalData))
	var addDataMap map[string]interface{}
	if additionalData != nil {
		err := json.Unmarshal(additionalData, &addDataMap)
		if log.IfError(err) {
			ctx.SetStatusCode(fasthttp.StatusForbidden)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid additional_data"})
			return
		}
	}

	if pubKey == "" {
		log.Debug("pubkey is empty")
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	if !utils.IsValidEmailAddress(email) {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 34, Message: "invalid email address"})
		return
	}

	//check id token from firebase
	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		fmt.Printf("error getting Auth client: %v\n", err)
	}

	token, err := client.VerifyIDToken(ctxBack, string(idToken))
	if err != nil {
		log.Info("error verifying ID token: %v, token: %s", err, idToken)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: fmt.Sprintf("id_token invalid. %v", err)})
		return
	}

	log.Info("token claims: %v", utils.SimplyToJson(token.Claims))
	tokenClaim, err := json.Marshal(token.Claims)
	log.IfError(err)

	var tokenClaimData struct {
		AuthTime      int    `json:"auth_time,omitempty"`
		Email         string `json:"email,omitempty"`
		EmailVerified bool   `json:"email_verified,omitempty"`
		Firebase      struct {
			Identities struct {
				Email []string `json:"email,omitempty"`
				Phone []string `json:"phone,omitempty"`
			} `json:"identities,omitempty"`
			SignInProvider string `json:"sign_in_provider,omitempty"`
		} `json:"firebase,omitempty"`
		PhoneNumber string `json:"phone_number,omitempty"`
		UserID      string `json:"user_id,omitempty"`
	}

	err = json.Unmarshal(tokenClaim, &tokenClaimData)
	log.IfError(err)

	phoneVerified := 0
	if tokenClaimData.Firebase.SignInProvider == "phone" {
		phone = tokenClaimData.PhoneNumber
		phoneVerified = 1
	} else if tokenClaimData.Firebase.SignInProvider == "email" {
		email = tokenClaimData.Email
	} else {
		if token.Claims["email"] != nil {
			email = utils.ToString(token.Claims["email"])
		}
		if claimPhone := cast.ToString(token.Claims["phone_number"]); claimPhone != "" {
			phone = claimPhone
			phoneVerified = 1
		}
	}

	phone = strings.TrimPrefix(phone, "+")

	fmt.Println("phone:", phone, "email:", email)
	if phone == "" && email == "" {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "can not get phone or email from your idToken"})
		return
	}

	phone = utils.FormatPhoneNumber(phone)

	//check existing user
	sql := `
SELECT m.phone, m.email, group_concat(md.admin_fkid) as admin_fkids, group_concat(business_name) as business
from members m
    LEFT join members_detail md on m.member_id = md.member_fkid
LEFT join admin a on md.admin_fkid = a.admin_id
where m.phone = ? or m.email = ?
group by member_id`
	data, err := db.Query(sql, phone, email)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("total existing member: %v (%v|%v)", len(data), phone, email)
	if len(data) > 0 {
		if utils.ToString(data["phone"]) == phone && utils.ToString(data["email"]) == email {
			adminFkIds := strings.Split(utils.ToString(data["admin_fkids"]), ",")
			isRegisteredAtThisAdmin := false
			for _, id := range adminFkIds {
				if id == adminFkid {
					isRegisteredAtThisAdmin = true
					break
				}
			}

			if isRegisteredAtThisAdmin {
				log.Info("'%s' is registered at this business", phone)
				_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 30, Message: "You're already registered at this business!"})
				return
			} else {
				log.Info("'%s' is registered at : %s", phone, data["business"])
				_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 31, Message: fmt.Sprintf("You are already registered at : %s", data["business"]), Data: map[string]interface{}{
					"business": data["business"],
				}})
				return
			}
		}
		msg := "Email already used"
		code := 32
		if utils.ToString(data["phone"]) == phone {
			msg = "Phone number already used"
			code = 33
		}
		log.Debug("(%s|%s) %s", phone, email, msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg, Code: code})
		return
	}

	//look for default member type
	defaultType, err := db.Query("SELECT type_id FROM members_type where point_target=0 and spent_target=0 "+
		"and price=0 and admin_fkid = ? ", adminFkid)
	log.IfError(err)

	if len(defaultType) == 0 {
		log.Info("admin : %s has no default member type", adminFkid)
		log.IfError(fmt.Errorf("adminId %v has no default member type", adminFkid))
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "This business has no default member type"})
		return
	}

	var memberId, memberDetailId int64
	err = db.WithTransaction(func(tx db.Transaction) error {
		dataMember := map[string]interface{}{
			"email":          email,
			"name":           name,
			"gender":         gender,
			"phone":          phone,
			"date_of_birth":  dateOfBirth,
			"city":           city,
			"province":       province,
			"address":        address,
			"phone_verified": phoneVerified,
			"expired_date":   time.Now().Unix() * 1000,
			"data_created":   time.Now().Unix() * 1000,
		}

		res, err := tx.Insert("members", utils.RemvoveEmptyFields(dataMember))

		if err != nil {
			return err
		}

		memberId, _ = res.LastInsertId()
		log.Info("new member: %v | %v", memberId, utils.SimplyToJson(dataMember))

		res, err = tx.Insert("members_detail", map[string]interface{}{
			"member_fkid":   memberId,
			"admin_fkid":    adminFkid,
			"type_fkid":     defaultType["type_id"],
			"register_date": time.Now().Unix() * 1000,
		})
		if err != nil {
			return err
		}

		memberDetailId, _ = res.LastInsertId()

		if additionalData != nil {
			for dataKey, dataVal := range addDataMap {
				if strings.TrimSpace(utils.ToString(dataVal)) == "" {
					log.Warn("value for data '%s' is empty", dataKey)
					continue
				}
				_, err = tx.Insert("members_detail_data", map[string]interface{}{
					"member_detail_fkid": memberDetailId,
					"data_key":           dataKey,
					"data_value":         dataVal,
				})
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	// go sendEmailConfirmation(adminFkid, utils.ToString(memberId), email, name)
	go AutoEarnPoint(adminFkid, memberId, memberDetailId)
	// go job.RunPromotionRole()
	go func(adminId int) {
		time.Sleep(3 * time.Second) // wait, in order to be able to get push notif token
		u.ucPromotion.RunPromotionRole(domain.PromotionRoleParam{AdminId: adminId})
	}(cast.ToInt(adminFkid))

	log.Info("'%d' registered with id : '%d'", email, memberId)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"member_id": memberId,
	}})
}

func AutoEarnPoint(adminId string, memberId int64, memberDetailId int64) {
	//check from database first
	sql := `SELECT point_collection_id, name, point from point_collection 
	where point_type='register' and admin_fkid = @adminId
	and date_start <= UNIX_TIMESTAMP()*1000 
	and date_end >= UNIX_TIMESTAMP()*1000
	and time_start <= time(FROM_UNIXTIME(UNIX_TIMESTAMP()+@timeOffset))
	and time_end >= time(FROM_UNIXTIME(UNIX_TIMESTAMP()+@timeOffset))
	and @dayName=1
	and data_status=1
	limit 1 `

	timeOffset := 25200
	day := "day_" + time.Now().Add(time.Duration(timeOffset)*time.Second).Weekday().String()
	day = strings.ToLower(day)
	log.Info("day: %v", day)
	sql = strings.Replace(sql, "@dayName", day, 1)

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    adminId,
		"timeOffset": timeOffset,
	})

	data, err := db.Query(sql, params...)
	log.IfError(err)

	totalPoint := cast.ToInt(data["point"])
	log.Info("point collection register from db: (%v) %v --> %d", data["point_collection_id"], data["name"], totalPoint)
	if len(data) > 0 {
		return
	}

	if len(data) == 0 && (os.Getenv("server") == "development" || (os.Getenv("server") == "staging" && (adminId == "10" || adminId == "7"))) {
		totalPoint = 10
	}

	if totalPoint == 0 {
		log.Info("adminId : '%s' no need to generate auto point", adminId)
		return
	}

	_, err = db.Update("members_detail", map[string]interface{}{
		"total_point": totalPoint,
	}, "members_detail_id = ? and member_fkid = ?", memberDetailId, memberId)

	if !log.IfError(err) {
		time.Sleep(15 * time.Second) // wait, in order to be able to get push notif token
		messaging.PushNotification(messaging.Notification{
			Title:    "Kamu dapat bonus " + language.Point(utils.ToInt(adminId)),
			Message:  fmt.Sprintf("Selamat kamu dapat %d %s gratis", totalPoint, language.Point(utils.ToInt(adminId))),
			MemberId: memberId,
			AdminId:  adminId,
		})
	}
}

func JoinWithOtherBusiness(ctx *fasthttp.RequestCtx) {
	idToken := ctx.PostArgs().Peek("id_token")
	pubKey := string(ctx.Request.Header.Peek("Public-Key"))
	adminFkid := utils.Decrypt(pubKey, utils.UID_PUBKEY)

	if pubKey == "" {
		log.Info("can not join to this business, undefined public-key")
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	//check id token from firebase
	// ctxBack := context.Background()
	// client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	// if log.IfErrorSetStatus(ctx, err) {
	// 	return
	// }

	// token, err := client.VerifyIDToken(ctxBack, string(idToken))
	// if err != nil {
	// 	log.Info("error verifying ID token: %v\n", err)
	// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: fmt.Sprintf("id_token invalid. %v", err)})
	// 	return
	// }

	// if token.Claims["phone_number"] == nil {
	// 	log.Info("phone number is not recognized from token. token clain : %v", token.Claims)
	// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "server can not get your phone number!"})
	// 	return
	// }

	// phone := utils.ToString(token.Claims["phone_number"])
	// if strings.HasPrefix(phone, "+") {
	// 	phone = phone[1:]
	// }

	phone, email, err := utils.IsValidIdToken(string(idToken))
	if err != nil {
		log.Warn("request token failed, id token is invalid: " + err.Error())
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	param := phone
	authType := "phone"
	if phone == "" && email != "" {
		authType = "email"
		param = email
	}

	fmt.Println("Phone : ", phone, "Emai: ", email)

	sql := `SELECT member_id,group_concat(md.admin_fkid) as admin_fkids FROM members m 
	left join members_detail md on m.member_id = md.member_fkid 
	WHERE m.$key = ? group by member_id `
	sql = strings.Replace(sql, "$key", authType, 1)
	member, err := db.Query(sql, param)
	log.IfError(err)

	if len(member) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "Phone number not registered!"})
		return
	}

	adminFkIds := strings.Split(utils.ToString(member["admin_fkids"]), ",")
	for _, id := range adminFkIds {
		if id == adminFkid {
			log.Info("%v already registered at: %v", phone, id)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "You already registered at this business!"})
			return
		}
	}

	//look for default member type
	defaultType, err := db.Query("SELECT type_id FROM members_type where point_target=0 and spent_target=0 "+
		"and price=0 and admin_fkid = ? ", adminFkid)
	log.IfError(err)

	if len(defaultType) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "This business has no default member type"})
		return
	}

	resp, err := db.Insert("members_detail", map[string]interface{}{
		"member_fkid":   member["member_id"],
		"admin_fkid":    adminFkid,
		"type_fkid":     defaultType["type_id"],
		"register_date": time.Now().Unix() * 1000,
	})
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	memberDetailId, _ := resp.LastInsertId()

	//auto earn point after registration
	go AutoEarnPoint(adminFkid, utils.ToInt64(member["member_id"]), memberDetailId)

	log.Info("registering %s to admin %s is successful -- memberDetailId: %v", phone, adminFkid, memberDetailId)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func GetMemberDetail(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))

	sql := `select member_id,
       email,
       email_verified,
       m.name,
       gender,
       phone,
       date_of_birth,
       address,
       city,
       province,
       postal_code,
       register_date,
       total_point,
	   md.total_spend as total_spent,
	   m.data_created,
	   md.type_fkid,
       p.name as member_type,
		 md.members_detail_id,
		 ma.access_type,
		 CAST((SELECT sum(coalesce(point_lost, 0)) from members_type_history where member_fkid=m.member_id and admin_fkid=md.admin_fkid) AS SIGNED) as total_point_lost,
		 CAST((SELECT sum(coalesce(spend_lost, 0)) from members_type_history where member_fkid=m.member_id and admin_fkid=md.admin_fkid) AS SIGNED) as total_spend_lost
from members m
         join members_detail md on md.member_fkid = m.member_id
         join members_type mt on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
		 left join (select access_type,member_fkid from members_access where admin_fkid = ?) ma on ma.member_fkid=m.member_id
WHERE member_id = ?
  and md.admin_fkid = ?
LIMIT 1`

	data, err := db.Query(sql, adminId, memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//fetch total spent
	// sql = `SELECT sum(grand_total) as total from sales s
	// join outlets o on o.outlet_id=s.outlet_fkid
	// where member_fkid =  ?
	// and status='Success'
	// and o.admin_fkid = ? `
	// salesSpent, err := db.Query(sql, memberId, adminId)
	// log.IfError(err)

	go updateSpend(memberId, adminId)

	dataDetail, err := db.QueryArray("select data_key, data_value from members_detail_data where member_detail_fkid = ?", data["members_detail_id"])
	log.IfError(err)

	memberDetail := make(map[string]interface{})
	for _, detail := range dataDetail {
		memberDetail[utils.ToString(detail["data_key"])] = detail["data_value"]
	}

	// data["total_spent"] = utils.ToInt(salesSpent["total"])
	data["additional_data"] = memberDetail
	data["barcode"] = utils.Encrypt(utils.ToString(data["member_id"]), utils.KEY_MEMBER_BARCODE)
	data["barcode_url"] = generate.QrCode(cast.ToString(data["barcode"]), 100) //fmt.Sprintf("https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=%s", data["barcode"])
	log.Info("member id : '%v' -- qrCode: '%s'", data["member_id"], data["barcode"])

	if utils.ToInt(data["email_verified"]) == 1 {
		data["email_verification_status"] = "verified"
	} else {
		//data["email_verification_status"] = "pending"
		//lastVerify := utils.ToInt64(data["last_verify_email"])
		//if lastVerify == 0 {
		//	lastVerify = utils.ToInt64(data["data_created"])
		//}
		//
		//lastVerifyTime := time.Unix(lastVerify/1000, 0)
		//diff := lastVerifyTime.Sub(time.Now())
		//if math.Abs(diff.Hours()) > float64(24*3) { //if more than 3 days
		data["email_verification_status"] = "unverified"

		status, err := usecase.FetchEmailVerificationStatusFromFirebase(utils.ToString(data["email"]))
		if err == nil && status {
			data["email_verification_status"] = "verified"
			_, err = db.Update("members", map[string]interface{}{
				"email_verified": 1,
			}, "member_id = ?", utils.ToString(data["member_id"]))
			log.IfError(err)
		}
	}

	utils.RemoveField(data, "admin_fkid")
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "sucess", Data: data})
}

func updateSpend(memberId, adminId string) {
	sql := `SELECT sum(grand_total) as total from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where member_fkid =  ?
	and status='Success'
	and o.admin_fkid = ? `
	salesSpent, err := db.Query(sql, memberId, adminId)
	if log.IfError(err) {
		return
	}

	_, err = db.Update("members_detail", map[string]interface{}{
		"total_spend": salesSpent["total"],
	}, "member_fkid = ? and admin_fkid = ?", memberId, adminId)
	log.IfError(err)
}

func GetPoint(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))

	data, err := db.Query("SELECT total_point FROM members_detail WHERE member_fkid = ? AND admin_fkid = ? LIMIT 1", memberId, adminId)
	if utils.CheckError(ctx, err) {
		return
	}

	var totalPoint interface{} = 0
	if data["total_point"] != nil {
		totalPoint = data["total_point"]
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "sucess", Data: totalPoint})
}

func UpdateFirebaseToken(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	firebase := string(ctx.PostArgs().Peek("firebase_token"))
	userAgent := string(ctx.UserAgent())
	logTime := log.TimeInit()

	if firebase == "null" {
		firebase = ""
	}

	headers := make(map[string]interface{})
	ctx.Request.Header.VisitAll(func(key, value []byte) {
		headers[string(key)] = string(value)
	})
	log.Info("headers (%v): %v", memberId, utils.SimplyToJson(headers))

	// log.Info("platform (%v): %v", memberId, ctx.Request.Header.Peek("Platform"))

	device := "android"
	if strings.Contains(userAgent, "dart") {
		device = "ios"
	}

	platform := strings.ToLower(string(ctx.Request.Header.Peek("Platform")))
	if platform != "" {
		device = platform
	}

	// log.Info("user agent: %v", userAgent)
	currentToken, err := db.Query("select firebase_token from members_detail  where member_fkid = ? AND admin_fkid = ? ", memberId, adminId)
	log.IfError(err)
	logTime.AddLog("get current token")

	resp, err := db.Update("members_detail", map[string]interface{}{
		"firebase_token": firebase,
		"last_device":    device,
	}, "member_fkid = ? AND admin_fkid = ?", memberId, adminId)
	if utils.CheckError(ctx, err) {
		return
	}
	logTime.AddLog("update firebase")

	msg := "no row effected"
	rows, _ := resp.RowsAffected()
	if rows > 0 {
		msg = "success"
		go updateNotifUser(firebase, memberId, adminId, device, cast.ToString(currentToken["firebase_token"]), headers)
	}

	logTime.Print()
	fmt.Println("update token, id : ", memberId, device, firebase)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: msg})
}

func updateNotifUser(firebase, memberId, adminId, device, currentToken string, headers map[string]interface{}) {
	memberDetail, err := db.Query("select members_detail_id from members_detail where member_fkid = ? and admin_fkid = ?", memberId, adminId)
	log.IfError(err)

	//remove all tokens
	if firebase == "" || firebase == "null" {
		log.Info("removing token %s (%s)", memberId, adminId)
		_, err = db.Delete("user_notification_token", "user_type = ? and token = ? and app = ?", "member", currentToken, "crm")
		log.IfError(err)
	} else {

		deviceInfo := array.TakeOnly(headers, "Systemversion", "Systemname", "Uid", "X-Real-Ip")

		//insert into user_notif_token
		_, err = db.Insert("user_notification_token", map[string]interface{}{
			"user_type":    "member",
			"user_id":      memberDetail["members_detail_id"],
			"device":       device,
			"app":          "crm",
			"token":        firebase,
			"device_info":  utils.SimplyToJson(deviceInfo),
			"data_created": time.Now().Unix() * 1000,
			"data_updated": time.Now().Unix() * 1000,
		})
		if err != nil && !strings.Contains(err.Error(), "Duplicate entry") {
			log.IfError(err)
		}
	}
}

func RequestSecretID(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	//get member_detail_id
	memberDetail, err := db.Query("SELECT members_detail_id as id FROM members_detail WHERE member_fkid = ? AND admin_fkid = ?", memberId, adminId)
	if utils.CheckError(ctx, err) {
		return
	}

	memberDetailId := utils.ToString(memberDetail["id"])

	//give padding, if needed
	secretId := utils.AddPadding(memberDetailId, 3)
	//change number
	secretId = utils.ChangeNumber(secretId)

	//adding milisecond
	nano := time.Now().Nanosecond()
	millisec := nano / 1000000
	if millisec > 1000 {
		millisec -= 1
	} else if millisec < 100 {
		millisec += 100
	}
	secretId += utils.ToString(millisec)

	expired := 5
	timeMillis := time.Now().UnixNano() / 1000000
	timeExpired := time.Now().Add(time.Minute*time.Duration(expired)).UnixNano() / 1000000

	_, err = db.Update("members_detail", map[string]interface{}{
		"secret_id":         secretId,
		"secret_id_expired": timeExpired,
	}, "members_detail_id = ?", memberDetailId)

	if utils.CheckError(ctx, err) {
		return
	}

	log.Debug("secret code for %s : %s", memberId, secretId)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"secret_id":              secretId,
		"expired":                timeExpired,
		"expired_value":          expired,
		"expired_value_duration": "minute",
		"time_millis":            timeMillis,
	}})
}

func (u *UserControllerHandler) UpdateProfile(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	name := string(ctx.PostArgs().Peek("name"))
	province := string(ctx.PostArgs().Peek("province"))
	city := string(ctx.PostArgs().Peek("city"))
	address := string(ctx.PostArgs().Peek("address"))
	postalCode := string(ctx.PostArgs().Peek("postal_code"))
	dateBirth := string(ctx.PostArgs().Peek("date_birth"))
	gender := string(ctx.PostArgs().Peek("gender"))
	email := string(ctx.PostArgs().Peek("email"))
	idToken := string(ctx.PostArgs().Peek("id_token"))
	additionalData := ctx.PostArgs().Peek("additional_data")

	//if status, msg := utils.IsValidPostInput(ctx, "name", "date_birth", "gender"); !status {
	//	log.Debug(msg)
	//	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
	//	return
	//}

	if gender != "0" && gender != "1" {
		gender = ""
	}

	newData := map[string]interface{}{
		"name":          name,
		"address":       address,
		"date_of_birth": dateBirth,
		"gender":        gender,
		"province":      province,
		"city":          city,
		"postal_code":   postalCode,
	}

	log.Info("update member: %v | %v", memberId, newData)

	//newData will only contain data with non empty string
	//so remove everything not sent by client
	for k, v := range newData {
		if strings.TrimSpace(utils.ToString(v)) == "" {
			delete(newData, k)
		}
	}

	if len(newData) == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "no data were given"})
		return
	}

	log.Info("additional data : '%s'", string(additionalData))
	var addDataMap map[string]interface{}
	if additionalData != nil {
		err := json.Unmarshal(additionalData, &addDataMap)
		if log.IfError(err) {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid additional_data"})
			return
		}
	}

	sql := `select member_id,
       email,
       email_verified,
       m.name,
       gender,
       phone,
       date_of_birth,
       address,
       city,
       province,
       postal_code,
       register_date,
       total_point,
       p.name as member_type,
       md.members_detail_id,
       md.status
from members m
         join members_detail md on md.member_fkid = m.member_id
         join members_type mt on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
WHERE member_id = ?
  and md.admin_fkid = ?
LIMIT 1 `
	member, err := db.Query(sql, memberId, adminId)
	log.IfError(err)

	if len(member) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 50, Message: "data not found"})
		return
	}

	if member["status"] == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 51, Message: "member is inactive"})
		return
	}

	if gender != "" && gender != "1" && gender != "0" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "gender should use 1 (man) or 0 (female)"})
		return
	}

	if idToken != "" {
		log.Info("verify id_token...")
		phone, email, err := utils.IsValidIdToken(idToken)
		if err != nil || (phone == "" && email == "") {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid id_token"})
			return
		}

		//check if phone or email is not used by other
		memberPhone, err := db.Query("select member_id from members where (phone = ? OR email = ?) and member_id != ?", phone, email, memberId)
		log.IfError(err)

		if len(memberPhone) > 0 {
			log.Info("phone '%s' already used by member id : %v | current member id : %s", phone, memberPhone["member_id"], memberId)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 33, Message: "phone already used"})
			return
		}

		if phone != "" {
			newData["phone"] = phone
			member["phone"] = phone
		}
	}

	if email != "" && email != utils.ToString(member["email"]) {
		log.Info("updating email address...")
		if !utils.IsValidEmailAddress(email) {
			log.Info("email '%s' is not valid", email)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 54, Message: "invalid email address"})
			return
		}

		memberEmail, err := db.Query("select email, member_id from members where email = ? and member_id != ? LIMIT 1", email, memberId)
		log.IfError(err)

		if len(memberEmail) > 0 {
			log.Info("email '%s' already used by %v", email, memberEmail["member_id"])
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 32, Message: "email already used"})
			return
		}

		go sendEmailConfirmation(adminId, memberId, email, name)
		newData["email"] = email
		newData["email_verified"] = 0
	}

	log.Info("data will be updated for '%s' : %s | before: %s", memberId, utils.SimplyToJson(newData), utils.SimplyToJson(member))
	_, err = db.Update("members", newData, "member_id = ?", memberId)

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//update additional data
	if additionalData != nil {
		go func(memberId, adminId string, addDataMap map[string]interface{}) {
			sql := `select md.members_detail_id, mdd.*
from members_detail md
         left join members_detail_data mdd on md.members_detail_id = mdd.member_detail_fkid
where md.member_fkid = ?
  and md.admin_fkid = ? `
			memberDetailData, err := db.QueryArray(sql, memberId, adminId)
			if log.IfError(err) {
				return
			}

			memberDetailDataMap := make(map[string]interface{})
			for _, detail := range memberDetailData {
				memberDetailDataMap[utils.ToString(detail["data_key"])] = detail["data_value"]
			}

			for dataKey, dataVal := range addDataMap {
				if strings.TrimSpace(utils.ToString(dataVal)) == "" {
					log.Warn("value for data '%s' is empty", dataKey)
					continue
				}

				if memberDetailDataMap[dataKey] == nil {
					_, err = db.Insert("members_detail_data", map[string]interface{}{
						"member_detail_fkid": memberDetailData[0]["members_detail_id"],
						"data_key":           dataKey,
						"data_value":         dataVal,
					})
					log.IfError(err)
				} else {
					_, err = db.Update("members_detail_data", map[string]interface{}{
						"data_value": dataVal,
					}, "data_key = ? and member_detail_fkid = ?", dataKey, memberDetailData[0]["members_detail_id"])
					log.IfError(err)
				}
			}
		}(memberId, adminId, addDataMap)
	}

	//member, err = db.Query(sql, memberId, adminId)
	//log.IfError(err)
	//
	//member["additional_data"] = addDataMap
	//member["barcode"] = utils.Encrypt(utils.ToString(member["member_id"]), utils.KEY_MEMBER_BARCODE)
	//member["barcode_url"] = fmt.Sprintf("https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=%s", member["barcode"])
	//utils.RemoveField(member, "admin_fkid")

	log.Info("dateOfBirth prev: %v | changed to: %v", member["date_of_birth"], dateBirth)
	if dateBirth != cast.ToString(member["date_of_birth"]) {
		go u.ucPromotion.RunPromotionRole(domain.PromotionRoleParam{AdminId: cast.ToInt(adminId)})
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func ResendEmailConfirmation(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	email := string(ctx.PostArgs().Peek("email"))

	if email == "" || !utils.IsValidEmailAddress(email) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid email address"})
		return
	}

	//check if email is not used by other
	memberEmail, err := db.Query("select member_id from members where email = ? and member_id != ?", email, memberId)
	log.IfError(err)
	if len(memberEmail) > 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "email address is already in use"})
		return
	}

	member, err := db.Query("select m.name, m.email, m.phone, md.status, m.email_verified from members m "+
		"join members_detail md on m.member_id = md.member_fkid where admin_fkid= ? and member_id = ?", adminId, memberId)
	log.IfError(err)

	if len(member) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 50})
		return
	}

	if member["status"] == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 51})
		return
	}

	if email == utils.ToString(member["email"]) && utils.ToString(member["email_verified"]) == "1" {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "email has verified"})
		return
	}

	go sendEmailConfirmation(adminId, memberId, email, utils.ToString(member["name"]))

	//update last_verify_email in db
	//go func(memberId string) {
	//	_, err = db.Update("members", map[string]interface{}{"last_verify_email": time.Now().Unix() * 1000}, "member_id = ?", memberId)
	//	log.IfError(err)
	//}(memberId)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func LostPhoneNumber(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	email := string(ctx.PostArgs().Peek("email"))

	sql := `
select m.member_id, m.email, m.name, m.email_verified, md.status
from members m
         join members_detail md on m.member_id = md.member_fkid
where m.email = ?
  and md.admin_fkid = ?`
	member, err := db.Query(sql, email, adminId)
	log.IfError(err)

	if len(member) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 50, Message: "account is not found"})
		return
	}

	if member["status"] == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 51, Message: "member status is inactive or banned"})
		return
	}

	log.Info("'%s' lost phone number, email verify status : %v", email, member["email_verified"])

	//sending email
	go func(memberId, email, name string) {
		template, err := utils.ReadFile("config/template/email_confirm_template.html")
		if log.IfError(err) {
			return
		}

		hasher := md5.New()
		memberIdEnc := utils.Encrypt(memberId, utils.KEY_ACTIVATION_EMAIL)
		strMd5 := hex.EncodeToString(hasher.Sum([]byte(memberIdEnc)))
		secretKey, err := utils.HashPassword(strMd5[:])
		log.IfError(err)

		_, err = db.Insert("users_key", map[string]interface{}{
			"key_type":     "change_phone",
			"user_level":   "member",
			"email":        email,
			"secret_key":   secretKey,
			"status":       1,
			"data_created": time.Now().Unix() * 1000,
			"data_expired": time.Now().Local().Add(time.Hour*time.Duration(24)).Unix() * 1000,
		})
		if log.IfError(err) {
			return
		}

		urlActivation := fmt.Sprintf(
			"%s/app/user/change_phone/%s/%s/%s", utils.CRM_PAGE, strMd5, "member", url.QueryEscape(email))
		shortLink, err := utils.CreateDynamicLink(urlActivation, utils.GetAndroidPackageName(utils.ToInt(adminId)))

		if !log.IfError(err) && shortLink != "" {
			urlActivation = shortLink
		}

		template = strings.Replace(template, "{email_confirmation_link}", urlActivation, -1)
		template = strings.Replace(template, "{uniq_client_name}", name, 1)
		log.IfError(messaging.SendEmail(email, template, "Change Phone Number Verification"))

	}(utils.ToString(member["member_id"]), email, utils.ToString(member["name"]))

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

// ChangePhoneNumber : this is after client sent to : LostPhoneNumber
func ChangePhoneNumber(ctx *fasthttp.RequestCtx) {
	//adminId := string(ctx.Request.Header.Peek("uid"))
	keyString := utils.ToString(ctx.PostArgs().Peek("key"))
	level := utils.ToString(ctx.PostArgs().Peek("level"))
	email := utils.ToString(ctx.PostArgs().Peek("email"))
	idToken := utils.ToString(ctx.PostArgs().Peek("id_token"))

	if status, msg := utils.IsValidPostInput(ctx, "key", "level", "email", "id_token"); !status {
		log.Debug(msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	phone, _, err := utils.IsValidIdToken(idToken)
	if err != nil || phone == "" {
		log.Info("invalid id_token: %s --> %s", err.Error(), phone)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid id_token"})
		return
	}

	userKeys, err := db.QueryArray("select * from users_key where key_type = 'change_phone' and user_level = ? and email = ? order by data_created desc", level, email)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	errValidation := errors.New("link is invalid or expired")
	var userKeyMatch map[string]interface{}
	for _, key := range userKeys {
		if utils.CheckPasswordHash(keyString, utils.ToString(key["secret_key"])) {
			if utils.ToInt64(key["data_expired"]) < (time.Now().Unix() * 1000) {
				errValidation = errors.New("link is expired")
			} else if utils.ToString(key["status"]) == "0" {
				errValidation = errors.New("link already used")
			} else {
				errValidation = nil
				userKeyMatch = key
			}
			break
		}
	}

	if errValidation != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: errValidation.Error()})
		return
	}

	memberIdHex, err := hex.DecodeString(keyString)
	log.IfError(err)
	memberId := utils.Decrypt(utils.ToString(memberIdHex), utils.KEY_ACTIVATION_EMAIL)
	_, err = db.Update("members", map[string]interface{}{"phone": phone}, "member_id = ?", memberId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//update user keys, prevent it to use again
	go func(userKeyId interface{}) {
		_, err = db.Update("users_key", map[string]interface{}{"status": 0}, "key_id = ?", userKeyId)
		log.IfError(err)
	}(userKeyMatch["key_id"])

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func sendEmailConfirmation(adminId, memberId, email, name string) {
	template, err := utils.ReadFile("config/template/email_confirm_template.html")
	if log.IfError(err) {
		return
	}

	hasher := md5.New()
	memberIdEnc := utils.Encrypt(memberId, utils.KEY_ACTIVATION_EMAIL)
	strMd5 := hex.EncodeToString(hasher.Sum([]byte(memberIdEnc)))
	secretKey, err := utils.HashPassword(strMd5[:72])
	log.IfError(err)

	linkExpired := time.Now().Local().Add(time.Hour*time.Duration(24*3)).Unix() * 1000
	_, err = db.Insert("users_key", map[string]interface{}{
		"key_type":     "activation",
		"user_level":   "member",
		"email":        email,
		"secret_key":   secretKey,
		"status":       1,
		"data_created": time.Now().Unix() * 1000,
		"data_expired": linkExpired,
	})
	if log.IfError(err) {
		return
	}

	admin, err := db.Query("select business_name from admin where admin_id = ?", adminId)
	log.IfError(err)

	appName := cast.ToString(admin["business_name"])
	log.Info(">> appName for %v is: %v", adminId, appName)

	urlActivation := fmt.Sprintf("%s/user/activation/%s/%s/%s", os.Getenv("api-crm"), strMd5, "member", url.QueryEscape(email))
	//shortLink, err := utils.CreateDynamicLink(urlActivation, utils.GetAndroidPackageName(utils.ToInt(adminId)))
	//log.Info("shortLink : %s", shortLink)
	//if !log.IfError(err) {
	//	urlActivation = shortLink
	//}

	shortUrl := utils.ShortUrl(utils.ShortUrlModel{
		Title:     "Email Verification",
		LongUrl:   urlActivation,
		Tags:      []string{"verification"},
		Length:    35,
		ExpiredAt: linkExpired,
	})

	log.Info("[shortUrl] is %v | original: %v", shortUrl, urlActivation)

	if shortUrl != "" {
		urlActivation = shortUrl
	}

	template = strings.Replace(template, "{email_confirmation_link}", urlActivation, -1)
	template = strings.Replace(template, "{uniq_client_name}", name, 1)
	template = strings.Replace(template, "{APP_NAME}", strings.ToUpper(appName), 1)
	log.IfError(messaging.SendEmail(email, template, "Email Confirmation"))
}

func EmailActivation(ctx *fasthttp.RequestCtx) {
	randomString := utils.ToString(ctx.UserValue("random"))
	level := utils.ToString(ctx.UserValue("level"))
	email := utils.ToString(ctx.UserValue("email"))

	err := checkEmailActivation(randomString, level, email)
	if err == nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
	} else {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
	}
}

func EmailVerificationWeb(ctx *fasthttp.RequestCtx) {
	randomString := utils.ToString(ctx.UserValue("random"))
	level := utils.ToString(ctx.UserValue("level"))
	email := utils.ToString(ctx.UserValue("email"))

	err := checkEmailActivation(randomString, level, email)
	if err != nil {
		if verificationErr, ok := err.(*verificationError); ok {
			ctx.Redirect(fmt.Sprintf("%s/info/status/email/%d", utils.CRM_PAGE, verificationErr.code), fasthttp.StatusTemporaryRedirect)
			return
		} else {
			log.Error("email verification unknown error ")
		}
	}

	ctx.Redirect(utils.CRM_PAGE+"/info/status/email/100", fasthttp.StatusTemporaryRedirect)
}

type verificationError struct {
	err  string
	code int
}

func (e *verificationError) Error() string {
	return fmt.Sprintf(e.err)
}

// error code -->
// 100 : success, 101 : link expired, 102 : link already used
func checkEmailActivation(randomString, level, email string) error {
	log.Info("email activation... \nrandom : %s \nlevel : %s \nemail : %s", randomString, level, email)
	userKeys, err := db.QueryArray("select * from users_key where key_type = 'activation' and user_level = ? and email = ?", level, email)
	if log.IfError(err) {
		return err
	}

	if len(userKeys) == 0 {
		return errors.New("invalid link")
	}

	errValidation := errors.New("link is invalid or expired")
	var userKeyMatch map[string]interface{}
	for _, data := range userKeys {
		//find match secret key
		if utils.CheckPasswordHash(randomString, utils.ToString(data["secret_key"])) {
			fmt.Println("found : ", data)
			if utils.ToInt64(data["data_expired"]) < (time.Now().Unix() * 1000) {
				errValidation = &verificationError{err: "link is expired", code: 101}
			} else if utils.ToString(data["status"]) == "0" {
				errValidation = errors.New("link already used")
				errValidation = &verificationError{err: "link already used", code: 102}
			} else {
				errValidation = nil
				userKeyMatch = data
			}
			break
		}
	}

	if errValidation != nil {
		fmt.Println(errValidation)
		return errValidation
	}

	if len(userKeyMatch) == 0 {
		log.IfError(errors.New("status is not error but user_key not found"))
	}

	go func(randomString, email string, userKeyMatch map[string]interface{}) {
		//update user key, make it disable
		_, err = db.Update("users_key", map[string]interface{}{"status": 0}, "key_id = ?", userKeyMatch["key_id"])
		log.IfError(err)

		//update data in member
		memberIdHex, err := hex.DecodeString(randomString)
		log.IfError(err)
		memberId := utils.Decrypt(utils.ToString(memberIdHex), utils.KEY_ACTIVATION_EMAIL)
		_, err = db.Update("members", map[string]interface{}{
			"email":          email,
			"email_verified": 1,
		}, "member_id = ?", memberId)
		log.IfError(err)
	}(randomString, email, userKeyMatch)

	return nil
}

func ChangePhoneNumberRedirect(ctx *fasthttp.RequestCtx) {
	ctx.Redirect("https://www.uniq.id", fasthttp.StatusPermanentRedirect)
}

func SendEmailVerificationCode(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))

	sql := `select m.email, m.email_verified
from members m
         join members_detail md on m.member_id = md.member_fkid
where m.member_id = ?
  and md.admin_fkid = ? limit 1`

	member, err := db.Query(sql, memberId, adminId)
	log.IfError(err)

	if len(member) == 0 {
		log.Warn("member not found")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "no member found"})
		return
	}

	if utils.ToInt(member["email_verified"]) == 1 {
		log.Warn("'%s' : email already verified", member["email"])
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 55, Message: "email address already verified"})
		return
	}

	//generate code, this code will send to email
	code := utils.RandomNumber(100000, 999999)

	//suffixCode := utils.RandomNumber(0, 9)
	////contain : suffix, code & expired time
	//codeWithInfo := fmt.Sprintf("%d%d%d", suffixCode, code, time.Now().Add(60*time.Minute).Unix())
	//if suffixCode%2 == 0 {
	//	codeWithInfo = fmt.Sprintf("%d%d%d", suffixCode, time.Now().Add(60*time.Minute).Unix(), code)
	//}

	timeExpired := time.Now().Add(30 * time.Minute)

	msgTemplate := `untuk memverifikasi akun email anda, masukan kode berikut ini : <br/>
<h2> %d </h2><br/>
kode dapat digunankan maksimal sampai jam %v <br/><br/>
<i>jika anda merasa tidak melakukan permintaan kode, abaikan email ini</i><br/><br/></br>`

	err = messaging.SendEmail(utils.ToString(member["email"]), fmt.Sprintf(msgTemplate, code, timeExpired.Add(7*time.Hour).Format("15:04 01/02/2006")), "Email Confirmation Code")
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	//the token contains code and time expired
	//the token will be used to validate code
	key := utils.Encrypt(fmt.Sprintf("%d%d%s", code, timeExpired.Unix(), memberId), utils.KEY_EMAIL_CODE)
	log.Debug("key : %s | code : %s", key, code)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success, email sent to " + utils.ToString(member["email"]), Data: map[string]interface{}{
		"key":        key,
		"expired_at": timeExpired.Unix() * 1000,
	}})
}

func ValidateEmailVerificationCode(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	//adminId := string(ctx.Request.Header.Peek("uid"))
	key := string(ctx.PostArgs().Peek("key"))
	code := utils.ToInt(string(ctx.PostArgs().Peek("code")))

	if status, msg := utils.IsValidPostInput(ctx, "key", "code"); !status {
		log.Debug(msg)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	keyDecoded := utils.Decrypt(key, utils.KEY_EMAIL_CODE)
	if len(keyDecoded) < 16 {
		log.Warn("key decoded length is not meet requirement")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid verification code"})
		return
	}

	originCode := utils.ToInt(keyDecoded[:6])
	expired := utils.ToInt64(keyDecoded[6:16])
	memberIdDecoded := keyDecoded[16:]

	fmt.Println(keyDecoded)

	if expired < time.Now().Unix() {
		log.Warn("verification failed, expired")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "expired"})
		return
	}

	if code != originCode {
		log.Warn("verification failed, unmatch code, key : '%s' | code : '%s'", key, code)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid verification code"})
		return
	}

	if memberIdDecoded != memberId {
		log.Warn("verification failed, unmatch member id - %s VS %s", memberIdDecoded, memberId)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid verification code"})
		return
	}

	_, err := db.Update("members", map[string]interface{}{"email_verified": 1}, "member_id = ?", memberId)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}
