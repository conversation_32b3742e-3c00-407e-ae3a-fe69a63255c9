package v1

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

var cacheBannerUrl map[string]string

const bannerKey = "tjCG3TCt5C&#r1o4"

func GetBanner(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	userAgent := string(ctx.UserAgent())
	fmt.Println(">> Get banner agent: ", userAgent)
	data, err := db.QueryArray("SELECT * FROM crm_banner WHERE admin_fkid = ? and status=1 and data_status='on' order by COALESCE(position, data_created)", adminId)
	log.IfError(err)

	// if !strings.Contains(userAgent, "dart") {
	// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data})
	// 	return
	// }

	// //exclude: 69, 68,
	// result := make([]map[string]interface{}, 0)
	// for _, row := range data {
	// 	id := cast.ToInt(row["crm_banner_id"])
	// 	if id != 69 && id != 68 {
	// 		result = append(result, row)
	// 	}
	// }

	for i, row := range data {
		if actionDetail, ok := row["action_detail"].(string); ok && actionDetail != "" {
			data[i]["action_detail_origin"] = actionDetail
			data[i]["action_detail"] = getUrlAction(actionDetail, cast.ToInt(row["crm_banner_id"]))
		}
	}

	//if no data, use deals image as default banner
	if len(data) == 0 {
		query := `SELECT photo from promotions 
		where admin_fkid=? 
		and end_promotion_date > UNIX_TIMESTAMP()*1000 
		and promotion_type_id=15 
		and (photo is not null and photo != '')`
		deals, err := db.QueryArray(query, adminId)
		if !log.IfError(err) {
			data = deals
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data})
}

func LogBanner(ctx *fasthttp.RequestCtx) {
	userAgent := string(ctx.UserAgent())
	bannerId := ctx.UserValue("id")
	key := ctx.PostArgs().Peek("key")

	var body struct {
		Key string `json:"key,omitempty"`
	}

	bodyJsonByte, err := json.Marshal(ctx.PostBody())
	log.IfError(err)

	bodyJson := strings.ReplaceAll(string(bodyJsonByte), `"`, "")
	log.Info("body json: '%s'", bodyJson)

	// err = json.Unmarshal(ctx.PostBody(), &body)
	// log.IfError(err)

	fmt.Println(">> Log banner agent: ", userAgent, "id: ", bannerId, "key: ", string(key), "body: ", string(ctx.PostBody()), "key: ", body.Key)

	keyDec := utils.Decrypt(bodyJson, bannerKey)
	log.Info("banner dec: %v", keyDec)

	_, err = db.GetConn().Exec("update crm_banner set total_clicks = total_clicks + 1 where crm_banner_id = ?", bannerId)
	log.IfError(err)

	log.Info("update banner click %v", bannerId)
}

func getUrlAction(url string, id int) string {
	if cache, ok := cacheBannerUrl[url]; ok && cache != "" {
		return cache
	}

	actionUrl := fmt.Sprintf("%s/v1/app/banner-log/%v", os.Getenv("API_CRM"), id)
	actionUrl = utils.Encrypt(actionUrl, bannerKey)

	//in case failed
	if actionUrl == "" {
		log.IfError(fmt.Errorf("failed encrypt action url..."))
		return url
	}

	continueUrl := utils.BuildUrl("https://y.uniq.id/continue", map[string]interface{}{
		"url":    url,
		"action": actionUrl,
	})

	log.Info("%v - action banner: %s -- %s", id, continueUrl, url)
	if cacheBannerUrl == nil {
		cacheBannerUrl = make(map[string]string)
	}
	cacheBannerUrl[url] = continueUrl
	return continueUrl
}
