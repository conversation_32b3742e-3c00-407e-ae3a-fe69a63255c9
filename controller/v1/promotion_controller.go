package v1

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/generate"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

func GetDeals(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	page := ctx.UserValue("page")
	id := ctx.UserValue("id") //optional: to get detail deals by id
	log.Info("user agent: %s", ctx.UserAgent())

	//optional, if set, app will return only deals available for specific member
	memberId := string(ctx.Request.Header.Peek("mid"))

	timeOffset := 25200
	//now := time.Now().Unix() * 1000
	//weekday := time.Unix(time.Now().Unix()+25200, 0).Weekday()

	var sql strings.Builder
	sql.WriteString(`
SELECT p.*,
       coalesce(b.reedem, 0)                                          as total_reedem,
       from_unixtime(start_promotion_date / 1000 + 25200, '%Y-%m-%d') as start_date_db,
       from_unixtime(unix_timestamp() + ?, '%Y-%m-%d')            as time_now,
	   if(cnp.crm_notify_product_id, 'on','off') as notify,
	   if((p.maximum_redeem - coalesce(b.reedem, 0)) > 0, if(p.publish_date > unix_timestamp()*1000, unix_timestamp(), p.promotion_id), 0) as tmp_id
FROM promotions p
         left join (select count(*) as reedem, min(pb.promotion_fkid) as promotion_id
                    from promotion_buy pb
                    where source = 'self_buy'
                    group by pb.promotion_fkid) as b on b.promotion_id = p.promotion_id
		left join crm_notify_product cnp on cnp.promotion_fkid=p.promotion_id and cnp.member_fkid = ?
WHERE promotion_type_id = ?
  and p.admin_fkid = ?
  and p.status = 1
	and p.deals_can_buy = 1
	and p.active = 1 
  and concat(from_unixtime(end_promotion_date / 1000, '%Y-%m-%d'), ' ', end_promotion_time) >=
      from_unixtime(unix_timestamp() + ?, '%Y-%m-%d %H:%i:%s')  
  `)
	//to filter only available:  and (p.maximum_redeem - coalesce(b.reedem, 0)) > 0
	//another way to create default tmp_id: (p.promotion_id * 0.001)

	params := utils.ArrayOf(timeOffset, memberId, utils.PROMO_DEALS, adminId, timeOffset)
	if id != nil {
		sql.WriteString(" and p.promotion_id = ? ")
		params = append(params, id)
	}

	platform := strings.ToLower(string(ctx.Request.Header.Peek("Platform")))
	log.Info("get deals platform: '%v'", platform)
	if platform != "" {
		// sql += " and (p.publish_date is null or p.publish_date = 0 or p.publish_date < (unix_timestamp()*1000) or date(from_unixtime(p.publish_date/1000)) >= DATE(DATE_ADD(NOW(), INTERVAL -1 day))) "
		sql.WriteString(" and (p.publish_date is null or p.publish_date = 0 or  p.publish_date < (unix_timestamp()*1000) or date(from_unixtime(p.publish_date/1000)) >= DATE(DATE_ADD(NOW(), INTERVAL -1 day))) ")
	} else {
		sql.WriteString(" and (p.publish_date is null or p.publish_date = 0 or p.publish_date <= UNIX_TIMESTAMP()*1000 ) ")
	}

	// if memberId != "" && adminId != "" && os.Getenv("server") != "production" {
	// 	memberDetail, err := db.Query("select type_fkid from members_detail where member_fkid = ? and admin_fkid = ?", memberId, adminId)
	// 	log.IfError(err)

	// 	sql += " and p.promotion_id in (select promotion_id from promotion_member_type where member_type_id = ?) "
	// 	params = append(params, memberDetail["type_fkid"])

	// 	sqlJoin += "  LEFT JOIN crm_notify_product cnp on cnp.promotion_id = p.promotion_id and cnp.member_fkid = ? "
	// } else {
	// 	sql += " and (p.publish_date is null or p.publish_date = 0 or date(from_unixtime(p.publish_date/1000)) >= DATE(NOW()) ) "
	// }

	sql.WriteString(" ORDER BY tmp_id DESC ")

	if page != nil {
		MAX := 15
		offset := MAX * (utils.ToInt(page) - 1)
		sql.WriteString(fmt.Sprintf(" limit %d offset %d ", MAX, offset))
	}

	data, err := db.QueryArray(sql.String(), params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) > 0 && (platform == "" || (platform != "" && id != nil)) {
		log.Info("getting detail promo, id: %v | platform: '%v'", id, platform)
		promotionIds := make([]interface{}, 0)
		for _, promo := range data {
			promotionIds = append(promotionIds, promo["promotion_id"])
		}

		sql.Reset()
		sql.WriteString(`select o.name, o.outlet_id, po.promotion_id from promotion_outlets po 
		join outlets o on po.outlet_id = o.outlet_id where po.promotion_id in (` + strings.Repeat("?,", len(promotionIds)-1) + `?)`)
		outlets, err := db.QueryArray(sql.String(), promotionIds...)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		sql.Reset()
		sql.WriteString("select pp.price as price_promo, pd.price_sell, p.name, pd.product_detail_id, p.product_id,pp.promotion_id from promotion_products pp " +
			"join products_detail pd on pp.product_detail_fkid = pd.product_detail_id " +
			"join products p on pd.product_fkid = p.product_id " +
			"where pp.promotion_id in (" + strings.Repeat("?,", len(promotionIds)-1) + "?) and (pp.type != 'order_menu' or pp.type is null) ")
		promoProducts, err := db.QueryArray(sql.String(), promotionIds...)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		fmt.Println("promoProducts : ", len(promoProducts))
		for _, promo := range data {
			outletDetail := make([]map[string]interface{}, 0)
			products := make([]map[string]interface{}, 0)
			for _, outlet := range outlets {
				if promo["promotion_id"] == outlet["promotion_id"] {
					utils.RemoveField(outlet, "promotion_id")
					outletDetail = append(outletDetail, outlet)
				}
			}
			for _, product := range promoProducts {
				if promo["promotion_id"] == product["promotion_id"] {
					utils.RemoveField(product, "promotion_id")
					products = append(products, product)
				}
			}
			if len(products) > 0 {
				promo["products"] = products
			}
			promo["outlet"] = outletDetail
		}
	}

	resp := models.ApiResponse{Status: true, Data: data}
	if id != nil {
		if len(data) > 0 {
			resp.Data = data[0]
		} else {
			resp.Status = false
			resp.Message = "Deals not found or expired"
			resp.Code = 70
			resp.Data = nil
		}
	}

	_ = json.NewEncoder(ctx).Encode(resp)
	data = nil

	// dataJson,_ := json.Marshal(data)
	// fmt.Fprint(ctx, string(dataJson))
}

func BuyDeals(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	dealsId := string(ctx.PostArgs().Peek("deals_id"))
	// transactionId := string(ctx.PostArgs().Peek("transaction_id"))

	sql := `SELECT md.status, total_point, type_fkid, name, email, phone
	FROM members_detail md
	JOIN members m ON m.member_id = md.member_fkid  
	WHERE member_fkid = ? and admin_fkid = ?`
	memberData, err := db.Query(sql, memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//check if member exist
	if len(memberData) == 0 {
		log.Info("buy deals failed, member not found!")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 50, Message: "FetchMember not found"})
		return
	}

	//check if member status is active
	if utils.ToString(memberData["status"]) != "1" {
		log.Info("buy deals failed, FetchMember status is no active")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 51, Message: "FetchMember status is no active"})
		return
	}

	sql = `
SELECT p.*, coalesce(b.reedem, 0) as total_reedem, coalesce(pr.personal_reedem, 0) as total_personal_reedem
FROM promotions p
         left join (select count(*) as reedem, min(pb.promotion_fkid) as promotion_id
                    from promotion_buy pb
                    group by pb.promotion_fkid) as b on b.promotion_id = p.promotion_id
         left join (
    select count(*) as personal_reedem, promotion_fkid
    from promotion_buy pb
    where member_fkid = ?
    group by promotion_fkid
) as pr on pr.promotion_fkid = p.promotion_id
WHERE promotion_type_id = ?
  and admin_fkid = ?
  and from_unixtime(end_promotion_date / 1000 + ?, '%Y-%m-%d') >=
      from_unixtime(unix_timestamp() + ?, '%Y-%m-%d')
  and p.promotion_id = ?
	and p.active = 1
   `

	params := make([]interface{}, 0)
	params = append(params, memberId, utils.PROMO_DEALS, adminId, timeOffset, timeOffset, dealsId)

	if os.Getenv("server") != "production" {
		params = append(params, memberData["type_fkid"])
		sql += " and p.promotion_id in (select promotion_id from promotion_member_type where member_type_id = ?) "
	}

	promotion, err := db.Query(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//check if deal is even exist
	if len(promotion) == 0 {
		log.Info("buy deals failed, Deals not found")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 70, Message: "Deals not found or expired"})
		return
	}

	if utils.ToInt(promotion["deals_can_buy"]) == 0 {
		log.Info("deals can not be bought")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 70, Message: "Deals ini tidak dapat di beli"})
		return
	}

	//if publish date set, check if it matchs the condition
	if publishDate := cast.ToInt64(promotion["publish_date"]); publishDate > 0 {
		if publishDate < time.Now().Unix() {
			log.Info("deals %v not published, publish date: %v", dealsId, publishDate)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "deals belum dapat di beli saat ini"})
			return
		}
	}

	//check if deals is active today
	//weekday := strings.ToLower(time.Unix(time.Now().Unix()+25200, 0).Weekday().String())
	//fmt.Println("weekday : ", weekday, promotion[weekday])
	//if utils.ToString(promotion[weekday]) == "0" {
	//	log.Info("buy deals failed, Deals not active on this day")
	//	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 70, Message: "Deals not active on this day"})
	//	return
	//}

	//check total redeem
	if utils.ToInt(promotion["total_reedem"]) >= utils.ToInt(promotion["maximum_redeem"]) {
		log.Info("buy deals failed, Total redeem exceed limit")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 72, Message: "total redeem exceed the limit"})
		return
	}

	if utils.ToInt(promotion["total_personal_reedem"]) >= utils.ToInt(promotion["member_maximum_redeem"]) {
		log.Info("buy deals failed, member '%s' have bought %d times, while max is %d", memberId, promotion["total_personal_reedem"], promotion["member_maximum_redeem"])
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 75, Message: "Total redeem per member exceed limit"})
		return
	}

	// var paymentNotif models.PaymentNotification
	// paymentData := make(map[string]interface{})
	//check nominal type
	if promotion["voucher_price_type"] == "point" {
		//check point amount owned by member
		if utils.ToInt(memberData["total_point"]) < utils.ToInt(promotion["deals_value"]) {
			log.Info("buy deals failed, point is not enough")
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 71, Message: "Sorry, your point is not enough"})
			return
		}
	} else if promotion["voucher_price_type"] == "money" {
		// if transactionId == "" {
		// 	ctx.SetStatusCode(fasthttp.StatusBadRequest)
		// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "transaction_id is required"})
		// 	return
		// }

		// //check transaction_id, & make sure transaction_id is not used
		// paymentData, err = db.Query("select * from payment_notification "+
		// 	"where transaction_id = ? and transaction_id not in "+
		// 	"(select transaction_id from promotion_buy_payment) "+
		// 	"order by data_created desc limit 1", transactionId)
		// utils.CheckErr(err)

		// if len(paymentData) == 0 {
		// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 73, Message: "Invalid transaction id"})
		// 	return
		// }

		// err = json.Unmarshal([]byte(utils.ToString(paymentData["info"])), &paymentNotif)
		// log.IfError(err)

		// //check amount of money
		// amount := utils.ToFloat(paymentNotif.GrossAmount)
		// if amount < utils.ToFloat(promotion["deals_value"]) {
		// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 74, Message: "Invalid amount of payment"})
		// 	return
		// }

		//TODO: check if payment has been created and not expired

		//create payment

	}

	dealsPrice := 0
	if promotion["voucher_price_type"] != "free" {
		dealsPrice = utils.ToInt(promotion["deals_value"])
	}

	log.Info("promotion : %s", utils.SimplyToJson(promotion))
	resp, err := db.Insert("promotion_buy", map[string]interface{}{
		"member_fkid":         memberId,
		"promotion_fkid":      promotion["promotion_id"],
		"promotion_type_fkid": promotion["promotion_type_id"],
		"price":               dealsPrice,
		"price_type":          promotion["voucher_price_type"],
		"time_created":        time.Now().Unix() * 1000,
		"time_modified":       time.Now().Unix() * 1000,
		"promo_nominal":       promotion["promo_nominal"],
	})

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	responseData := make(map[string]interface{})
	promotionBuyId, _ := resp.LastInsertId()
	if promotion["voucher_price_type"] == "point" {
		fmt.Println("reduce point... to ", utils.ToInt(memberData["total_point"])-dealsPrice)
		fmt.Println(memberData["total_point"], dealsPrice, memberId)
		//reduce amount of member point
		_, err := db.Update("members_detail", map[string]interface{}{
			"total_point": utils.ToInt(memberData["total_point"]) - dealsPrice,
		}, "member_fkid = ? AND admin_fkid = ?", memberId, adminId)
		log.IfError(err)
	} else if promotion["voucher_price_type"] == "money" {
		// paymentStatus := paymentNotif.TransactionStatus
		// if paymentStatus == "settlement" {
		// 	paymentStatus = "paid"
		// }

		paymentResult, err := createPayment(models.PromotionPaymentCreate{
			AdminId: cast.ToInt(adminId),
			Id:      cast.ToString(promotionBuyId),
			Price:   dealsPrice,
			Customer: models.Customer{
				Name:  cast.ToString(memberData["name"]),
				Phone: cast.ToString(memberData["phone"]),
				Email: cast.ToString(memberData["email"]),
			},
		})
		if log.IfError(err) {
			//remove promo buy if failed to crate payment
			_, err = db.Delete("promotion_buy", "promotion_buy_id =?", promotionBuyId)
			log.IfError(err)

			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "failed to create payment"})
			return
		}

		_, err = db.Insert("promotion_buy_payment", map[string]interface{}{
			"promotion_buy_fkid": promotionBuyId,
			"payment_type":       "qris", //paymentNotif.PaymentType,
			"transaction_id":     paymentResult.TransactionID,
			"status":             "pending",
		})
		log.IfError(err)

		responseData["promotion_buy_id"] = promotionBuyId
		responseData["payment"] = map[string]interface{}{
			"payment_type": "qris",
			"qris":         paymentResult.Qris,
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: responseData})
}

func createPayment(promotion models.PromotionPaymentCreate) (models.PromotionPaymentResponse, error) {
	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("%s/v1/payment"),
		Header: map[string]interface{}{
			"business_id": promotion.AdminId,
		},
		PostRequest: utils.PostRequest{
			Body: map[string]interface{}{
				"payment_method": "qris",
				"id":             promotion.Id,
				"amount":         promotion.Price,
				"customer": map[string]interface{}{
					"name":  promotion.Customer.Name,
					"email": promotion.Customer.Email,
					"phone": promotion.Customer.Phone,
				},
			},
		},
	}

	resp, err := req.Execute()
	if err != nil {
		return models.PromotionPaymentResponse{}, err
	}

	var result models.PromotionPaymentResponse
	err = json.Unmarshal([]byte(resp.Body), &result)
	return result, err
}

func GetMyVoucherGroup(ctx *fasthttp.RequestCtx) {
	ctx.Request.Header.Set("group", "1")
	GetMyVoucher(ctx)
}

func GetMyVoucher(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	isGroup := string(ctx.Request.Header.Peek("group")) == "1"
	id := ctx.UserValue("id") //promotion_buy_id
	timeOffset := 25200

	sql := `SELECT pb.*, p.name, start_promotion_date, end_promotion_date, promo_type, term, 
	monday, tuesday, wednesday, thursday, friday, saturday, sunday, p.min_order, p.photo, p.promo_discount_type, 
	start_promotion_time, end_promotion_time, p.promo_nominal, p.promo_discount_maximum, p.max_qty_promo 
	FROM promotion_buy pb 
	JOIN promotions p ON p.promotion_id = pb.promotion_fkid 
	WHERE  pb.status = 'available' and pb.member_fkid = ? and p.admin_fkid = ? and p.status = 1 and p.active = 1  
	and from_unixtime(end_promotion_date / 1000 + ? , '%Y-%m-%d') >= from_unixtime(unix_timestamp() + ? , '%Y-%m-%d')
	and (p.publish_date is null or p.publish_date <= UNIX_TIMESTAMP()*1000) `

	params := make([]interface{}, 0)
	params = append(params, memberId, adminId, timeOffset, timeOffset)

	if promoBuyId := utils.ToInt(id); id != nil && promoBuyId > 0 {
		sql += " and pb.promotion_buy_id = ?  "
		params = append(params, promoBuyId)
	}

	sql += " order by time_created desc "
	data, err := db.QueryArray(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) == 0 {
		if id != nil {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{}})
		} else {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
		}
		return
	}

	for _, promo := range data {
		promo["redeem_qr_code"] = utils.Encrypt(utils.ToString(promo["promotion_buy_id"]), utils.KEY_PROMOTION_BARCODE)
		// promo["redeem_qr_code_url"] = fmt.Sprintf("https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=%s", promo["redeem_qr_code"])
		promo["redeem_qr_code_url"] = generate.QrCode(cast.ToString(promo["redeem_qr_code"]), 100)
		// log.Info("promoId: %v | qr: %v", promo["promotion_buy_id"], promo["redeem_qr_code"])
	}

	promotionIds := make([]interface{}, 0)
	for _, promo := range data {
		promotionIds = append(promotionIds, promo["promotion_fkid"])
	}

	if len(data) > 0 {
		sql := "select o.name, o.outlet_id, po.promotion_id from promotion_outlets po " +
			"join outlets o on po.outlet_id = o.outlet_id where po.promotion_id in (" + strings.Repeat("?,", len(promotionIds)-1) + "?)"
		outlets, err := db.QueryArray(sql, promotionIds...)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		log.Info("promotion ids : %v", promotionIds)
		log.Info("promo outlets : %s", utils.SimplyToJson(array.TakeJust(outlets, "promotion_id")))

		for _, promo := range data {
			outletDetail := make([]map[string]interface{}, 0)
			for _, outlet := range outlets {
				if promo["promotion_fkid"] == outlet["promotion_id"] {
					outletDetail = append(outletDetail, outlet)
					//utils.RemoveField(outletDetail[len(outletDetail)-1], "promotion_id")
				}
			}
			promo["outlet"] = outletDetail
		}

		//adding term product
		//only if request for specific id (performance concern)
		if id != nil && len(data) != 0 {
			promoProduct := getPromotionProduct(data[0]["promotion_fkid"])
			data[0]["term_product"] = promoProduct["term"]
			data[0]["promotion_product"] = promoProduct["promotion_product"]
		}
	}

	if isGroup {
		data = groupByPromotionId(data)
	}

	if id != nil && len(data) == 1 {
		// log.Info(">> data : %s", utils.SimplyToJson(data[0]))
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data[0]})
		return
	}

	log.Info("deals of %v >> %s", memberId, array.TakeJust(data, "promotion_id"))
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func getPromotionProduct(id interface{}) map[string]interface{} {
	sql := `SELECT variant_fkid, product_detail_fkid, price as price_sell_promo, qty, type from promotion_products where promotion_id=?`
	promoProducts, err := db.QueryArray(sql, id)
	log.IfError(err)

	result := make(map[string]interface{})

	log.Info("total promo product for: %v is: %v", id, len(promoProducts))

	if len(promoProducts) == 0 {
		return result
	}

	productDetailIds := make([]interface{}, 0)
	for _, product := range promoProducts {
		productDetailIds = append(productDetailIds, product["product_detail_fkid"])
	}

	sql = `SELECT concat(p.name, COALESCE(concat(' (',pdv.variant_name,')'), '')) as name, 
	p.product_id, pd.product_detail_id, pdv.variant_id, pd.price_sell
	 from products_detail pd 
	join products p on p.product_id=pd.product_fkid
	left join products_detail_variant pdv on pd.variant_fkid=pdv.variant_id
	where product_detail_id in @productDetailIds`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"productDetailIds": productDetailIds,
	})

	products, err := db.QueryArray(sql, params...)
	log.IfError(err)

	productMap := array.FlatMapArray(products, "product_detail_id")

	for i, product := range promoProducts {
		productInfo := productMap[cast.ToString(product["product_detail_fkid"])]
		promoProducts[i]["name"] = productInfo["name"]
		promoProducts[i]["price_sell"] = productInfo["price_sell"]
		promoProducts[i]["product_id"] = productInfo["product_id"]
	}

	if len(promoProducts) > 0 && promoProducts[0]["type"] == "order_menu" {
		productTerm := make([]map[string]interface{}, 0)
		for _, product := range promoProducts {
			if product["type"] == "order_menu" {
				productTerm = append(productTerm, array.TakeOnly(product, "name", "price_sell", "product_detail_fkid", "product_id"))
			}
		}

		result["term"] = map[string]interface{}{
			"qty":      promoProducts[0]["qty"],
			"products": productTerm,
		}
	}

	productPromo := make([]map[string]interface{}, 0)
	for _, product := range promoProducts {
		if product["type"] != "order_menu" {
			productPromo = append(productPromo, array.TakeOnly(product, "name", "price_sell", "price_sell_promo", "product_detail_fkid", "product_id"))
		}
	}

	result["promotion_product"] = productPromo
	return result
}

func groupByPromotionId(data []map[string]interface{}) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)

	promoById := make(map[int][]map[string]interface{})
	for _, item := range data {
		id := cast.ToInt(item["promotion_fkid"])
		if promoById[id] == nil {
			promoById[id] = make([]map[string]interface{}, 0)
		}
		promoById[id] = append(promoById[id], item)
	}

	for _, v := range promoById {
		item := v[0]
		itemDetails := make([]map[string]interface{}, 0)
		for _, detail := range v {
			itemDetails = append(itemDetails, map[string]interface{}{
				"promotion_buy_id": cast.ToInt(detail["promotion_buy_id"]),
			})
		}

		item["group_total"] = len(v)
		item["group_detail"] = itemDetails

		result = append(result, item)
	}

	log.Info("grouping voucher, %v | %v", len(data), len(result))
	return result
}

func GetTotalVoucher(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	timeOffset := 25200

	//did'n yet implement in production
	//if os.Getenv("server") == "production" {
	//	emptyData := make([]map[string]interface{}, 0)
	//	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: emptyData, Message: "success"})
	//	return
	//}

	sql := `select count(*) as total_voucher
from promotion_buy pb
         join promotions p on pb.promotion_fkid = p.promotion_id
where pb.status = 'available' and pb.member_fkid = ?
  and p.admin_fkid = ? and from_unixtime(p.end_promotion_date / 1000 + ? , '%Y-%m-%d') >= from_unixtime(unix_timestamp() + ? , '%Y-%m-%d')
	and p.active = 1 and p.status=1`
	data, err := db.Query(sql, memberId, adminId, timeOffset, timeOffset)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: utils.ToInt(data["total_voucher"])})
}

func GetMyVoucherDetail(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	id := ctx.UserValue("id")

	sql := `
select pb.*,
       p.name,
       start_promotion_date,
       end_promotion_date,
       promo_type,
       term,
       monday,
       tuesday,
       wednesday,
       thursday,
       friday,
       saturday,
       sunday,
       p.min_order,
       p.photo
from promotion_buy pb
         join promotions p on pb.promotion_fkid = p.promotion_id
where promotion_buy_id = ?
  and member_fkid = ?
  and p.admin_fkid = ?
  and pb.status = 'available'
group by promotion_buy_id `

	promotion, err := db.Query(sql, id, memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(promotion) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "not found"})
		return
	}

	promotion["redeem_qr_code"] = utils.Encrypt(utils.ToString(promotion["promotion_buy_id"]), utils.KEY_PROMOTION_BARCODE)
	// promotion["redeem_qr_code_url"] = fmt.Sprintf("https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=%s", promotion["redeem_qr_code"])
	promotion["redeem_qr_code_url"] = generate.QrCode(cast.ToString(promotion["redeem_qr_code"]), 100)
	log.Info("promoId: %v | qr: %v", promotion["promotion_buy_id"], promotion["redeem_qr_code"])

	sql = `
select o.outlet_id, o.name
from promotion_outlets po
         join outlets o on po.outlet_id = o.outlet_id
where po.promotion_id = ?`
	outlets, err := db.QueryArray(sql, promotion["promotion_fkid"])
	log.IfError(err)

	promotion["outlet"] = outlets

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: promotion})
}

func GetMyVoucherStatus(ctx *fasthttp.RequestCtx) {
	memberId := string(ctx.Request.Header.Peek("mid"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	id := ctx.UserValue("id")

	sql := `
select pb.status
from promotion_buy pb
         join promotions p on pb.promotion_fkid = p.promotion_id
where promotion_buy_id = ?
  and member_fkid = ?
  and p.admin_fkid = ? LIMIT 1`
	data, err := db.Query(sql, id, memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "not found"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data["status"]})
}

func GetPromotionByOutlet(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))

	if os.Getenv("server") == "production" && adminId != "143" {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: []map[string]interface{}{}})
		return
	}

	outletId := string(ctx.QueryArgs().Peek("outlet_id"))
	filterActive := string(ctx.QueryArgs().Peek("active")) == "true"
	filterPromotionTypes := strings.Split(strings.TrimSpace(string(ctx.QueryArgs().Peek("promotion_type"))), ",")

	//validate outletId
	outlets, err := db.Query("select outlet_id from outlets where admin_fkid = ?", adminId)
	log.IfError(err)
	if len(outlets) == 0 {
		log.Info("can not get promotion for outlet: '%s' for admin_id: %s", outletId, adminId)
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	log.Info("get promo query: %s", ctx.QueryArgs().String())

	var wg sync.WaitGroup
	promotionChan := make(chan []map[string]interface{}, 1)
	promotionTypeChan := make(chan []map[string]interface{}, 1)
	promotionProductChan := make(chan []map[string]interface{}, 1)

	sql := `
select p.*, group_concat(pmt.member_type_id) as member_type_ids, group_concat(p2.name) as member_type_names
from promotions p
         left join promotion_member_type pmt on p.promotion_id = pmt.promotion_id
         left join members_type mt on mt.type_id = pmt.member_type_id
         left join products p2 on mt.product_fkid = p2.product_id
where p.promotion_id in (select promotion_outlets.promotion_id from promotion_outlets where outlet_id = ?)
and p.end_promotion_date > UNIX_TIMESTAMP()*1000
%s
group by p.promotion_id `

	params := make([]interface{}, 0)
	params = append(params, outletId)
	addWhere := ""

	if filterActive {
		utcTime := time.Now().Add(7 * time.Hour)
		currTime := utcTime.Format("15:04:05")
		weekDay := strings.ToLower(utcTime.Weekday().String())

		sqlWhere := `
and p.start_promotion_date < UNIX_TIMESTAMP()*1000
and p.%s = 1
and p.start_promotion_time < ?
and p.end_promotion_time > ?  `
		addWhere = fmt.Sprintf(sqlWhere, weekDay)
		params = append(params, currTime, currTime)
	}

	sql = fmt.Sprintf(sql, addWhere)

	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionChan, sql, params...)

	sql = `select * from promotion_types`
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionTypeChan, sql)

	sql = `
select pp.*
from promotion_products pp
         join products_detail pd on pp.product_detail_fkid = pd.product_detail_id
         join promotions p on p.promotion_id=pp.promotion_id
where pd.outlet_fkid = ? and p.end_promotion_date > UNIX_TIMESTAMP()*1000 `
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionProductChan, sql, outletId)

	wg.Wait()

	promotions := <-promotionChan
	promotionTypes := <-promotionTypeChan
	promotionProducts := <-promotionProductChan

	promotionParent := cast.ToSingleMap(promotionTypes, "promotion_type_id", "name")

	for _, promotion := range promotions {
		for _, typeMap := range promotionTypes {
			if utils.ToString(typeMap["promotion_type_id"]) == utils.ToString(promotion["promotion_type_id"]) {
				parent := utils.ToString(promotionParent[typeMap["parent_id"]])
				parent = strings.Replace(parent, " ", "", -1)
				promotion["promotion_type"] = strings.ToLower(fmt.Sprintf("%s_%s", parent, strings.Replace(utils.ToString(typeMap["name"]), " ", "", -1)))
			}
		}

		products := make([]map[string]interface{}, 0)
		for _, product := range promotionProducts {
			if product["promotion_id"] == promotion["promotion_id"] {
				products = append(products, product)
			}
		}

		memberIds := make([]int, 0)
		memberTypes := make([]map[string]interface{}, 0)
		types := strings.Split(utils.ToString(promotion["member_type_names"]), ",")
		ids := strings.Split(utils.ToString(promotion["member_type_ids"]), ",")
		if len(types) != len(ids) {
			log.Error("length member ids and name not same, outlet id : %d | %d VS %d \nnames : %v\nids : %v", outletId, len(types), len(ids), promotion["member_type_names"], promotion["member_type_ids"])
		}

		for i, id := range ids {
			if id == "" {
				continue
			}
			memberIds = append(memberIds, utils.ToInt(id))
			memberTypeName := "[unknown]"
			if i < len(types) {
				memberTypeName = types[i]
			}
			memberTypes = append(memberTypes, map[string]interface{}{
				"type_id": utils.ToInt(id),
				"name":    memberTypeName,
			})
		}

		promotion["promotion_product"] = products
		//promotion["member_type_id"] = promotion["member_type_ids"]
		promotion["member_type_ids"] = memberIds
		promotion["member_type"] = memberTypes
	}

	if len(filterPromotionTypes) > 0 && filterPromotionTypes[0] != "" {
		fmt.Println("filter --> ", filterPromotionTypes)
		log.Info("total promo before apply filter: %d", len(promotions))
		result := make([]map[string]interface{}, 0)
		for _, promo := range promotions {
			for _, filterType := range filterPromotionTypes {
				if strings.ToLower(filterType) == utils.ToString(promo["promotion_type"]) {
					result = append(result, promo)
					break
				}
			}
		}
		promotions = result
		log.Info("total promo after apply filter: %d", len(promotions))
	}

	//log.Info("result --> %s", utils.SimplyToJson(promotions))
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: promotions})
}
