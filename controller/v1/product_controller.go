package v1

import (
	"encoding/json"
	"fmt"
	"mime/multipart"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/cache"
)

func GetProduct(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	adminId := string(ctx.Request.Header.Peek("uid"))

	sql := `
select product_id,
       p.name             as name,
       p.min_qty_order,
       photo,
       barcode,
       min(pd.price_sell) as price
from products p
         join products_detail pd on p.product_id = pd.product_fkid
         join outlets o on pd.outlet_fkid = o.outlet_id
         left join (select count(*) total_available, product_fkid
                    from crm_products_available
                    group by product_fkid) cpac
                   on cpac.product_fkid = p.product_id
         left join (select product_fkid, day, hour_start, hour_end
                    from crm_products_available
                    where hour_start <= from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and hour_end > from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and convert(day USING utf8) = lower(dayname(from_unixtime(unix_timestamp() + ?)))) cpaa
                   on cpaa.product_fkid = p.product_id
where o.admin_fkid = ?
  and p.data_status = 'on'
  and p.app_show = 1
  and o.data_status = 'on'
  and o.app_show = 1
  and (pd.active = 'on_all' or pd.active = 'on_sales')
  and if(cpac.total_available > 0, cpaa.hour_start is not null, true)
group by p.product_id
order by name `
	products, err := db.QueryArray(sql, timeOffset, timeOffset, timeOffset, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	for _, product := range products {
		if product["photo"] == nil || product["photo"] == "" {
			// product["photo"] = utils.DEFAULT_IMAGE
		} else {
			product["photo"] = fmt.Sprintf("%s/assets/images/products/%s/%s", os.Getenv("base_url"), adminId, product["photo"])
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: products})
}

func GetProductWithAvailability(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := cast.ToInt(ctx.Request.Header.Peek("mid"))
	group := ctx.UserValue("group")
	productId := ctx.UserValue("id")
	include := string(ctx.QueryArgs().Peek("include"))

	log.Info("get product of: %s - group by: %v", adminId, group)

	var wg sync.WaitGroup
	productChan := make(chan []map[string]interface{}, 1)
	outletChan := make(chan []map[string]interface{}, 1)
	workingHoursChan := make(chan []map[string]interface{}, 1)

	wg.Add(1)
	// group by varian
	//	select product_id,
	//		pdv.variant_id,
	//		coalesce(concat(p.name, ' (', pdv.variant_name, ')'), p.name)                           as name,
	//		photo,
	//		barcode,
	//		min(pd.price_sell)                                                                      as price,
	//		group_concat(pd.outlet_fkid)                                                            as outlets,
	//		group_concat(pd.price_sell separator '-')                                               as prices,
	//		group_concat(pd.product_detail_id separator '-')                                        as detail_ids,
	//		ps.product_subcategory_id,
	//		ps.name                                                                                 as subcategory,
	//		u.name                                                                                  as unit,
	//		u.description                                                                           as unit_description,
	//		p.description,
	//		group_concat(IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', pd.stock)) as stock_status,
	//		group_concat(pdv.variant_name)
	//		from products p
	//		join products_detail pd on p.product_id = pd.product_fkid
	//		join outlets o on pd.outlet_fkid = o.outlet_id
	//		join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
	//		join unit u on p.unit_fkid = u.unit_id
	//		left join products_detail_variant pdv on pd.variant_fkid=pdv.variant_id
	//		where p.admin_fkid = 7
	//		and p.app_show = 1
	//		and o.app_show = 1
	//		and p.data_status = 'on'
	//  and o.data_status = 'on'
	//  and (pd.active = 'on_all' or pd.active = 'on_sales')
	//		and pd.data_status='on'
	//group by p.product_id, pdv.variant_id
	//		order by subcategory, name
	sql := `
select product_id,
       p.name,
       photo,
       barcode,
p.min_qty_order,
       min(pd.price_sell)                                                                      as price,
       min(pd.price_sell)                                                                      as price_sell,
       group_concat(pd.outlet_fkid)                                                            as outlets,
       group_concat(pd.price_sell separator '-')                                               as prices,
       group_concat(pd.product_detail_id separator '-')                                        as detail_ids,
       ps.product_subcategory_id,
       ps.name                                                                                 as subcategory,
       u.name                                                                                  as unit,
       u.description                                                                           as unit_description,
       p.description,
	   group_concat( if(p.stock_management = 1, if(pd.stock_qty <= 0 OR pd.stock_qty < p.min_qty_order, 'unavailable', 'available'), pd.stock)) AS stock_status,
       group_concat(IF(p.stock_management = 1 and (pd.stock_qty <= 0 or pd.stock_qty < p.min_qty_order), 'unavailable', pd.stock)) as stock_status_v1
       $select
from products p
         join products_detail pd on p.product_id = pd.product_fkid
         join outlets o on pd.outlet_fkid = o.outlet_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         join unit u on p.unit_fkid = u.unit_id
         left join (select count(*) total_available, product_fkid
                    from crm_products_available
                    group by product_fkid) cpac
                   on cpac.product_fkid = p.product_id
         left join (select product_fkid, day, hour_start, hour_end
                    from crm_products_available
                    where hour_start <= from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and hour_end > from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and convert(day USING utf8) = lower(dayname(from_unixtime(unix_timestamp() + ?)))) cpaa
                   on cpaa.product_fkid = p.product_id
		$join
where p.admin_fkid = ?
  and p.app_show = 1
  and o.app_show = 1
  and p.data_status = 'on'
  and o.data_status = 'on'
  and (pd.active = 'on_all' or pd.active = 'on_sales')
  and if(cpac.total_available > 0, cpaa.hour_start is not null, true)
  $where_cond
group by p.product_id
order by ps.name, p.name `

	args := utils.ArrayOf(timeOffset, timeOffset, timeOffset)

	fmt.Println(">> memberId: ", memberId)
	if memberId > 0 {
		sql = strings.Replace(sql, "$select", ",if(count(cpw.crm_product_wishlist_id) > 0, true, false) as is_wishlist", 1)
		sql = strings.Replace(sql, "$join", "left join crm_product_wishlist cpw on cpw.product_fkid=p.product_id and cpw.member_fkid = ?", 1)
		args = append(args, memberId)
	}

	args = append(args, adminId)

	if productId != nil {
		sql = strings.Replace(sql, "$where_cond", " and p.product_id = ?", 1)
		args = append(args, productId)
	}

	//clear what's left...
	sql = strings.Replace(sql, "$where_cond", "", 1)
	sql = strings.Replace(sql, "$select", "", 1)
	sql = strings.Replace(sql, "$join", "", 1)

	go db.QueryArrayGo(&wg, productChan, sql, args...)

	wg.Add(1)
	go db.QueryArrayGo(&wg, outletChan, "SELECT outlet_id, name FROM outlets WHERE admin_fkid = ? and app_show=1 and data_status= 'on' ", adminId)

	wg.Add(1)
	sql = `SELECT
	ow.*
FROM
	outlets_workinghour ow
	JOIN outlets o ON o.outlet_id = ow.outlet_fkid
WHERE
	o.admin_fkid = ?
	AND o.app_show = 1
	AND o.data_status = 'on'`
	go db.QueryArrayGo(&wg, workingHoursChan, sql, adminId)

	taxByProductDetailId := make(map[int][]map[string]interface{})
	if include == "tax" {
		sql = `SELECT pdt.product_detail_fkid, g.* from products_detail_taxdetail pdt 
join products_detail pd on pd.product_detail_id=pdt.product_detail_fkid
join gratuity g on g.gratuity_id=pdt.tax_fkid
where pdt.data_status='on' and g.data_status='on'
and g.tax_status='permanent' and g.tax_category in ('service','tax')
and pd.product_fkid = ?
and g.admin_fkid = ? 
order by pdt.product_detail_fkid`
		taxes, _ := db.QueryArray(sql, productId, adminId)
		for _, tax := range taxes {
			if taxByProductDetailId[cast.ToInt(tax["product_detail_fkid"])] == nil {
				taxByProductDetailId[cast.ToInt(tax["product_detail_fkid"])] = make([]map[string]interface{}, 0)
			}
			taxByProductDetailId[cast.ToInt(tax["product_detail_fkid"])] = append(taxByProductDetailId[cast.ToInt(tax["product_detail_fkid"])], tax)
		}
	}

	wg.Wait()

	dateNow := time.Now().Add(7 * time.Hour)
	today := strings.ToLower(dateNow.Weekday().String())
	tNow, err := time.Parse("15:4:5", fmt.Sprintf("%d:%d:%d", dateNow.Hour(), dateNow.Minute(), dateNow.Day()))
	log.IfError(err)
	fmt.Println("time now: ", tNow.Format("15:4:5"))
	fmt.Println("time now: ", tNow)

	workingHours := <-workingHoursChan
	enableOrderByOutlet := make(map[string]interface{})
	outletMap := make(map[string]string)
	for _, outlet := range <-outletChan {
		outletMap[utils.ToString(outlet["outlet_id"])] = utils.ToString(outlet["name"])

		isEnableOrder := true
		for _, wh := range workingHours {
			if utils.ToString(wh["outlet_fkid"]) == utils.ToString(outlet["outlet_id"]) {
				isEnableOrder = false
				if cast.ToString(wh["day"]) == today {
					tOpen, _ := time.Parse("15:04:05", cast.ToString(wh["time_open"]))
					tClose, _ := time.Parse("15:04:05", cast.ToString(wh["time_close"]))
					fmt.Printf("outlet  %v | open: %v | close: %v -- %v VS %v \n", outlet["outlet_id"], tOpen, tClose, tNow.After(tOpen), tNow.Before(tClose))
					if tNow.After(tOpen) && tNow.Before(tClose) {
						isEnableOrder = true
					}
					break
				}
			}
		}

		enableOrder := map[string]interface{}{
			"status": "enable",
		}

		if !isEnableOrder {
			enableOrder = map[string]interface{}{
				"status": "disable",
				"reason": "closed",
			}
		}

		enableOrderByOutlet[utils.ToString(outlet["outlet_id"])] = enableOrder
	}

	products := <-productChan
	productBySubCategory := make(map[string][]map[string]interface{})
	subcategory := make(map[string]string)
	for _, product := range products {
		outletDetail := make([]map[string]interface{}, 0)

		prices := strings.Split(utils.ToString(product["prices"]), "-")
		stockStatus := strings.Split(utils.ToString(product["stock_status"]), ",")
		productDetailIds := strings.Split(utils.ToString(product["detail_ids"]), "-")

		for index, outlet := range strings.Split(utils.ToString(product["outlets"]), ",") {
			dataDetail := map[string]interface{}{
				"outlet_id":         outlet,
				"name":              outletMap[outlet],
				"price_sell":        prices[index],
				"stock_status":      stockStatus[index],
				"product_detail_id": productDetailIds[index],
				"enable_order":      enableOrderByOutlet[outlet],
			}
			if include == "tax" {
				dataDetail["tax"] = taxByProductDetailId[cast.ToInt(productDetailIds[index])]
				if taxByProductDetailId[cast.ToInt(productDetailIds[index])] == nil {
					dataDetail["tax"] = []interface{}{}
				}
			}
			outletDetail = append(outletDetail, dataDetail)
		}
		product["available"] = outletDetail
		if product["photo"] == nil || product["photo"] == "" {
			// product["photo"] = utils.DEFAULT_IMAGE
		} else if !strings.HasPrefix(utils.ToString(product["photo"]), "https://") {
			product["photo"] = fmt.Sprintf("%s/assets/images/products/%s/%s", os.Getenv("base_url"), adminId, product["photo"])
		}
		delete(product, "outlets")
		delete(product, "prices")

		subCatId := utils.ToString(product["product_subcategory_id"])
		if len(productBySubCategory[subCatId]) == 0 {
			//productBySubCategory[subCatId] = make(map[string]interface{},0)
			subcategory[subCatId] = utils.ToString(product["subcategory"])
		}

		productBySubCategory[subCatId] = append(productBySubCategory[subCatId], product)
	}

	if group == "subcategory" {
		productGroup := make([]map[string]interface{}, 0)
		for id, prodSub := range productBySubCategory {
			productGroup = append(productGroup, map[string]interface{}{
				"subcategory_id":   id,
				"subcategory_name": subcategory[id],
				"products":         prodSub,
			})
		}
		log.Debug("get product group return %d data", len(productGroup))
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: productGroup})
	} else {
		log.Debug("get product return %d data", len(products))
		response := models.ApiResponse{Status: true, Data: products}
		if productId != nil && len(products) > 0 {
			response.Data = products[0]
		}
		_ = json.NewEncoder(ctx).Encode(response)
	}
}

func GetProductByOutlet(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := cast.ToInt(ctx.Request.Header.Peek("mid"))
	outletId := ctx.UserValue("outletId")
	group := ctx.UserValue("group")
	include := string(ctx.QueryArgs().Peek("include"))

	separator := "[UNIQ]"

	sql := `
select product_id,
       min(pd.product_detail_id)                                                               as product_detail_id,
       group_concat(pd.product_detail_id)                                                      as product_detail_ids,
       p.name, p.min_qty_order, 
       photo,
p.min_qty_order,
       p.description,
       p.barcode,
       min(pd.price_sell)                                                                      as price_sell,
       group_concat(pd.price_sell)                                                             as prices_sell,
       ps.product_subcategory_id,
       ps.name                                                                                 as subcategory,
       u.name                                                                                  as unit,
       u.description                                                                           as unit_description,
	   group_concat( if(p.stock_management = 1, if(pd.stock_qty <= 0 OR pd.stock_qty < p.min_qty_order, 'unavailable', 'available'), pd.stock)) as stock_statuses, 
       group_concat(IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', pd.stock)) as stock_statuses_v1,
       group_concat(if(pd.stock = 'unavailable', pd.stock,
                       if(cpac.total_available > 0 and cpaa.hour_start is null,
                          'unavailable', 'available')))                                        as stock_statuses_old_2,

	   group_concat(IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', if(pd.stock = 'unavailable', pd.stock,
                       if(cpac.total_available > 0 and cpaa.hour_start is null,
                          'unavailable', 'available')))) as stock_statuses_new,

		min(IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', if(pd.stock = 'unavailable', pd.stock,
                       if(cpac.total_available > 0 and cpaa.hour_start is null,
                          'unavailable', 'available')))) as stock_status, 

       min(IF(p.stock_management = 1 and pd.stock_qty <= 0, 'unavailable', pd.stock))          as stock_status_old,
        min(if(pd.stock = 'unavailable' or (cpac.total_available > 0 and cpaa.hour_start is null), 'unavailable',
              'available'))                                                                    as stock_status_old,
       group_concat(replace(pdv.variant_name, '[UNIQ]', ' ') separator '[UNIQ]')               as variant_names,
       group_concat(pdv.variant_id)                                                            as variant_ids,
		min(pd.stock_qty) as stock_qty, p.stock_management
		$SELECT
from products p
         join products_detail pd on p.product_id = pd.product_fkid
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         join unit u on p.unit_fkid = u.unit_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
         left join (select count(*) total_available, product_fkid
                    from crm_products_available
                    group by product_fkid) cpac
                   on cpac.product_fkid = p.product_id
         left join (select product_fkid, day, hour_start, hour_end
                    from crm_products_available
                    where hour_start <= from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and hour_end > from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and convert(day USING utf8) = lower(dayname(from_unixtime(unix_timestamp() + ?)))) cpaa
                   on cpaa.product_fkid = p.product_id
		$JOIN
where p.admin_fkid = ?
  and p.app_show = 1
  and pd.outlet_fkid = ?
  and pd.data_status = 'on'
  and p.data_status = 'on'
  and (pd.active = 'on_all' or pd.active = 'on_sales')
  and if(cpac.total_available > 0, cpaa.hour_start is not null, true)
group by p.product_id
order by ps.name, name  `

	args := make([]interface{}, 0)
	args = append(args, timeOffset, timeOffset, timeOffset)
	fmt.Println("memberId: ", memberId)
	if memberId > 0 {
		sql = strings.Replace(sql, "$SELECT", ",(COALESCE(ANY_VALUE(cpw.crm_product_wishlist_id), 0) > 0) AS is_in_wishlist", 1)
		sql = strings.Replace(sql, "$JOIN", "LEFT JOIN crm_product_wishlist cpw ON cpw.product_fkid = p.product_id AND cpw.member_fkid = ?", 1)
		args = append(args, memberId)
	}

	//cleaning up
	sql = strings.ReplaceAll(sql, "$SELECT", "")
	sql = strings.ReplaceAll(sql, "$JOIN", "")

	args = append(args, adminId, outletId)
	products, err := db.QueryArray(sql, args...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	taxes := make([]map[string]interface{}, 0)
	if strings.ContainsAny(include, "tax") {
		sql = `SELECT pdt.product_detail_fkid,
       g.gratuity_id,
       g.name,
       tax_category,
       tax_status,
       tax_type,
       jumlah
FROM   products_detail_taxdetail pdt
       JOIN gratuity g
         ON pdt.tax_fkid = g.gratuity_id
       JOIN products_detail pd
         ON pd.product_detail_id = pdt.product_detail_fkid
WHERE  g.data_status = 'on'
       AND pd.outlet_fkid = ?
ORDER  BY pdt.product_detail_fkid  `
		taxes, err = db.QueryArray(sql, outletId)
		log.IfError(err)
	}

	result := make([]map[string]interface{}, 0)
	if len(products) > 0 {
		productGroup := make([]map[string]interface{}, 0)
		var lastCatName, lastCatId interface{} = "", ""
		for index, product := range products {
			if product["photo"] == nil || product["photo"] == "" {
				// product["photo"] = utils.DEFAULT_IMAGE
			} else if !strings.HasPrefix(utils.ToString(product["photo"]), "https://") {
				product["photo"] = fmt.Sprintf("%s/assets/images/products/%s/%s", os.Getenv("base_url"), adminId, product["photo"])
			}

			products[index]["is_in_wishlist"] = cast.ToInt(products[index]["is_in_wishlist"]) == 1

			//set variants
			variants := make([]map[string]interface{}, 0)
			stockStatuses := strings.Split(utils.ToString(product["stock_statuses"]), ",")
			pricesSell := strings.Split(utils.ToString(product["prices_sell"]), ",")
			productDetailIds := strings.Split(utils.ToString(product["product_detail_ids"]), ",")

			//if product has variant, then set the variant
			if product["variant_names"] != nil {
				variantNames := strings.Split(utils.ToString(product["variant_names"]), separator)
				for iv, variantId := range strings.Split(utils.ToString(product["variant_ids"]), ",") {
					if variantId == "" {
						continue
					}
					variants = append(variants, map[string]interface{}{
						"variant_id":        utils.ToInt(variantId),
						"name":              variantNames[iv],
						"stock_status":      stockStatuses[iv],
						"price_sell":        utils.ToInt(pricesSell[iv]),
						"product_detail_id": utils.ToInt(productDetailIds[iv]),
					})
				}
			}
			product["variant"] = variants

			if utils.ToInt(product["stock_management"]) == 1 && product["stock_status"] == "available" && utils.ToInt(product["min_qty_order"]) > 1 && utils.ToInt(product["stock_qty"]) < utils.ToInt(product["min_qty_order"]) {
				product["stock_status"] = "unavailable"
			}

			product["stock_status"] = product["stock_statuses"]
			if len(stockStatuses) > 0 { //if product has variant, check if one of the variant stock is available
				product["stock_status"] = "unavailable"
				for _, status := range stockStatuses {
					if status == "available" {
						product["stock_status"] = status
						break
					}
				}
			}

			utils.RemoveField(product, "variant_ids", "variant_names", "stock_statuses", "product_detail_ids", "prices_sell", "stock_qty")

			if len(taxes) > 0 {
				productTaxes := make([]map[string]interface{}, 0)
				isFound := false
				for _, tax := range taxes {
					if utils.ToString(product["product_detail_id"]) == utils.ToString(tax["product_detail_fkid"]) {
						isFound = true
						productTaxes = append(productTaxes, tax)
					} else if isFound {
						break
					}
				}
				product["tax"] = productTaxes
			}

			if group == "subcategory" {
				if product["subcategory"] != lastCatName && index > 0 {
					result = append(result, map[string]interface{}{
						"category_id":   lastCatId,
						"category_name": lastCatName,
						"products":      productGroup,
					})
					productGroup = make([]map[string]interface{}, 0)
				}

				lastCatName = product["subcategory"]
				lastCatId = product["product_subcategory_id"]

				utils.RemoveField(product, "subcategory", "product_subcategory_id")
				productGroup = append(productGroup, product)
			}
		}
		result = append(result, map[string]interface{}{
			"category_id":   lastCatId,
			"category_name": lastCatName,
			"products":      productGroup,
		})
	}

	ctx.SetContentType("application/json")

	if group == "subcategory" {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
	} else {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: products})
	}
}

//func GetProductAvailability(ctx *fasthttp.RequestCtx) {
//	adminId := string(ctx.Request.Header.Peek("uid"))
//	productId := ctx.UserValue("productId")
//
//	sql := `select pd.product_detail_id, pd.product_fkid, pdv.variant_id, COALESCE(pdv.variant_name, p.name) as variant_name, pd.price_sell, o.name as outlet_name
//from products_detail pd
//       join outlets o on pd.outlet_fkid = o.outlet_id
//       join products p on pd.product_fkid = p.product_id
//       left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
//where pd.product_fkid = ? and p.admin_fkid = ? and p.app_show = 1 order by p.name`
//
//	products, err := db.QueryArray(sql, productId, adminId)
//	if utils.CheckError(ctx, err) {
//		return
//	}
//
//	//result := make([]map[string]interface{},0)
//	//byVariant := make(map[string][]map[string]interface{})
//	//
//	//for _,product := range products {
//	//	found := byVariant[utils.ToString(product["variant_name"])]
//	//	fmt.Println(found)
//	//}
//
//	ctx.SetContentType("application/json")
//	_=json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: products})
//}

func SaveSearchProductHistory(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	searchText := string(ctx.PostArgs().Peek("text"))
	outletId := string(ctx.PostArgs().Peek("outlet_id"))

	resp, err := db.Insert("crm_search_history", map[string]interface{}{
		"member_fkid":  memberId,
		"admin_fkid":   adminId,
		"search_text":  searchText,
		"data_created": time.Now().Unix() * 1000,
	})

	if !log.IfError(err) && outletId != "" {
		searchId, _ := resp.LastInsertId()
		//validate outlet id & set outlet_fkid (by update)
		go func(id int64) {
			outlet, err := db.Query("select outlet_id from outlets where admin_fkid = ? and outlet_id= ? ", adminId, outletId)
			log.IfError(err)
			if len(outlet) > 0 {
				_, err = db.Update("crm_search_history", map[string]interface{}{"outlet_fkid": outletId}, "crm_search_history_id = ?", id)
				log.IfError(err)
			} else {
				log.Warn("can not set outlet_fkid in crm_search_history outlet '%s' is not belong to '%s'", outletId, adminId)
			}
		}(searchId)
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func GetSearchHistory(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	outletId := string(ctx.QueryArgs().Peek("outlet_id"))

	sql := `
select min(search_text) as search_text,
       count(*)                   as       count,
       group_concat(crm_search_history_id) ids,
       group_concat(data_created) as       date_createds,
       max(data_created)          as       last_time_search
from crm_search_history
where outlet_fkid is null
  and member_fkid = ?
  and admin_fkid = ?
group by search_text
order by last_time_search desc, count desc
limit 15`
	params := []interface{}{memberId, adminId}

	if outletId != "" {
		sql = `
select min(search_text) as search_text,
       count(*)                   as       count,
       group_concat(crm_search_history_id) ids,
       group_concat(data_created) as       date_createds, 
       max(data_created)          as       last_time_search
from crm_search_history
where member_fkid = ?
  and admin_fkid = ?
  and outlet_fkid = ?
group by search_text
order by last_time_search desc, count desc
limit 15 `
		params = append(params, outletId)
	}

	data, err := db.QueryArray(sql, params...)
	log.IfError(err)

	for _, search := range data {
		detail := make([]map[string]interface{}, 0)
		ids := strings.Split(utils.ToString(search["ids"]), ",")
		dates := strings.Split(utils.ToString(search["date_createds"]), ",")

		for index, id := range ids {
			detail = append(detail, map[string]interface{}{
				"crm_search_history_id": id,
				"date_created":          utils.GetSafe(dates, index),
			})
		}
		utils.RemoveField(search, "ids", "date_createds")
		search["detail"] = detail
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func MakePurchaseOrder(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	description := strings.TrimSpace(string(ctx.PostArgs().Peek("description")))
	qty := utils.ToInt(string(ctx.PostArgs().Peek("qty")))

	if description == "" {
		description = strings.TrimSpace(string(ctx.FormValue("description")))
	}

	if qty == 0 {
		qty = utils.ToInt(string(ctx.FormValue("qty")))
	}

	if len(description) < 10 {
		log.Debug("description is too short : '%s'", description)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "description is too short"})
		return
	}

	if qty <= 0 {
		qty = 1
	}

	description += fmt.Sprintf("\nJumlah Order : %s", utils.CurrencyFormat(qty))

	resp, err := db.Insert("crm_comments", map[string]interface{}{
		"comment":      description,
		"member_fkid":  memberId,
		"admin_fkid":   adminId,
		"data_created": time.Now().Unix() * 1000,
	})

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	poId, err := resp.LastInsertId()

	//upload photo
	withPhoto := false
	header, err := ctx.FormFile("photo")
	if err == nil {
		withPhoto = true
		fmt.Println("PO filename : ", header.Filename)
		go func(header *multipart.FileHeader, poId int64) {
			basePath := "temp/po"
			_, err = os.Stat(basePath)
			if os.IsNotExist(err) {
				log.IfError(os.MkdirAll(basePath, os.ModePerm))
			}

			imgFileName := adminId + "_" + utils.RandStringBytes(16) + utils.ToString(time.Now().Unix()) + ".jpg"
			tmpFileName := fmt.Sprintf("%s/%s", basePath, imgFileName)
			err = fasthttp.SaveMultipartFile(header, tmpFileName)
			if !log.IfError(err) {
				uploadStart := time.Now()
				file, err := os.Open(tmpFileName)
				if !log.IfError(err) {
					//defer log.IfError(file.Close())

					filePath := os.Getenv("server") + "/crm/po/" + adminId + "/" + imgFileName
					imgUrl, err := google.UploadFile(file, filePath, true)
					if err == nil {
						log.Debug("PO image of %d is %s", poId, imgUrl)
						_, err = db.Update("crm_comments", map[string]interface{}{
							"attachment": imgUrl,
						}, "crm_comments_id = ?", poId)
						log.IfError(err)
					}
				}
				if err = os.Remove(tmpFileName); err != nil {
					fmt.Println("Removing tmp file : ", tmpFileName, " - error : ", err)
				}
				log.Info("upload image took : %v", time.Since(uploadStart))
			}
		}(header, poId)
	} else {
		log.Info("po with no photo - %v", err)
	}

	log.Info("po finish... member %s (%s) request PO : '%s' | with photo ? %v", memberId, adminId, description, withPhoto)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func GetPurchaseOrderHistory(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	page := ctx.UserValue("page")

	sql := `select * from crm_comments where admin_fkid = ? and member_fkid = ? order by data_created desc `

	if page != nil {
		MAX := 15
		offset := MAX * (utils.ToInt(page) - 1)
		sql += fmt.Sprintf(" limit %d offset %d ", MAX, offset)
	}

	data, err := db.QueryArray(sql, adminId, memberId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("member %s (%s) request history po, with page : %v", memberId, adminId, page)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func AddNotifyProductOutStock(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	productDetailId := string(ctx.PostArgs().Peek("product_detail_id"))
	qty := string(ctx.PostArgs().Peek("qty"))

	//data, err := db.QueryArray("select product_detail_fkid from crm_product_notify where admin_fkid")

	if utils.ToInt(qty) <= 0 {
		qty = "1"
	}

	_, err := db.Insert("crm_notify_product", map[string]interface{}{
		"admin_fkid":          adminId,
		"member_fkid":         memberId,
		"product_detail_fkid": productDetailId,
		"qty":                 qty,
		"data_created":        time.Now().Unix() * 1000,
	})

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//insert to crm_comment
	//	go func(productDetailId, memberId, adminId string) {
	//		sql := `
	//select p.name as product_name, o.name as outlet_name from products_detail pd join products p on pd.product_fkid = p.product_id
	//join outlets o on pd.outlet_fkid = o.outlet_id where product_detail_id = ? `
	//		data, err := db.Query(sql, productDetailId)
	//		if log.IfError(err) {
	//			return
	//		}
	//
	//		_, err = db.Insert("crm_comment", map[string]interface{}{
	//			"comment":      fmt.Sprintf("%s (%s)", data["product_name"], data["outlet_name"]),
	//			"member_fkid":  memberId,
	//			"admin_fkid":   adminId,
	//			"data_created": time.Now().Unix() * 1000,
	//			"comment_type": "notify_product",
	//		})
	//
	//	}(productDetailId, memberId, adminId)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func GetNotifyProductList(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	sql := `
select min(admin_fkid) as admin_fkid, max(data_created) as data_created, member_fkid, product_detail_fkid
from crm_notify_product
where member_fkid = ?
  and admin_fkid = ?
group by member_fkid, product_detail_fkid `

	data, err := db.QueryArray(sql, memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func GetGratuityByOutletId(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	outletId := ctx.UserValue("outletId")
	fileterTaxStatus := string(ctx.QueryArgs().Peek("tax_status"))

	//memberId := string(ctx.Request.Header.Peek("mid"))

	sql := `SELECT pdt.product_detail_fkid,
       g.gratuity_id,
       g.name,
       tax_category,
       tax_status,
       tax_type,
       jumlah
FROM   products_detail_taxdetail pdt
       JOIN gratuity g
         ON pdt.tax_fkid = g.gratuity_id
       JOIN products_detail pd
         ON pd.product_detail_id = pdt.product_detail_fkid
WHERE  g.data_status = 'on'       
       and g.admin_fkid = ?
 `

	params := make([]interface{}, 0)
	params = append(params, adminId)

	if fileterTaxStatus != "" {
		// sql = strings.Replace(sql, "#WHERE", " and tax_status = ? ", 1)
		params = append(params, fileterTaxStatus)
		sql += " and tax_status = ? "
	}

	if outletId != nil || cast.ToInt(outletId) > 0 {
		params = append(params, outletId)
		sql += " AND pd.outlet_fkid = ? "
	}

	//put order query last
	sql += " ORDER  BY g.gratuity_id "
	result := make([]map[string]interface{}, 0)

	cacheKey := fmt.Sprintf("%v-gratuity-by-%s", os.Getenv("ENV"), utils.ConcatArgs(params))
	cacheImplement := cache.NewCacheDbRedis(db.GetRedisClient())
	gratuityCache, err := cacheImplement.Get(cacheKey)
	if err != nil && !(err == redis.Nil || strings.Contains(err.Error(), "unknown port")) {
		log.IfError(err)
	}
	if err == nil && gratuityCache != "" {
		if !log.IfError(json.Unmarshal([]byte(gratuityCache), &result)) {
			log.Info("GetGratuityByOutletId use cache... size %v, cahceSize: %v", len(result), len(gratuityCache))
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
			return
		}
	}

	gratuities, err := db.QueryArray(sql, params...)
	log.IfError(err)

	products := make([]map[string]interface{}, 0)
	for i, gratuity := range gratuities {
		if i == len(gratuities)-1 || gratuity["gratuity_id"] != gratuities[i+1]["gratuity_id"] {
			gratuity["products"] = products
			delete(gratuity, "product_detail_fkid")
			result = append(result, gratuity)
			products = make([]map[string]interface{}, 0)
		} else {
			products = append(products, map[string]interface{}{
				"product_detail_fkid": gratuity["product_detail_fkid"],
			})
		}
	}

	log.Info("get tax of outlet %v got: %d data", outletId, len(result))

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func GetProductUrl(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	id := utils.ToString(ctx.UserValue("id"))
	redirect := string(ctx.QueryArgs().Peek("redirect"))
	log.Info("id: '%s' --> redirect: %s", id, redirect)

	if id == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	idDecrypt := utils.Encrypt(id, utils.KEY_PRODUCT_ID)
	redirect = strings.Replace(redirect, "[id]", idDecrypt, 1)

	//validate redirect url
	if !strings.HasPrefix(redirect, "http://localhost") {
		allowedDomain := []string{"uniq.id", "uniqdev.xyz", "uniqdev.tech"}
		baseDomain := utils.FindBaseDomain(redirect)
		log.Info("base domain: %s", baseDomain)
		valid := false
		for _, domain := range allowedDomain {
			if domain == baseDomain {
				valid = true
				break
			}
		}
		if !valid {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: fmt.Sprintf("domain is not allowed")})
			return
		}
	}

	productUrl := redirect
	if appId := getAppCrmId(utils.ToInt(adminId)); appId != "" {
		//crate dynamic link
		fullLink := fmt.Sprintf("https://crm-page.uniq.id/product/%s?redirect=%s", id, redirect)
		req := utils.HttpRequest{
			Method: "POST",
			Url:    fmt.Sprintf("https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=%s", os.Getenv("FIREBASE_CRM_KEY")),
			PostRequest: utils.PostRequest{Body: map[string]interface{}{
				"dynamicLinkInfo": map[string]interface{}{
					"domainUriPrefix": "https://crm-page.uniq.id/link",
					"link":            fullLink,
					"androidInfo": map[string]interface{}{
						"androidPackageName":  appId,
						"androidFallbackLink": fullLink,
					},
				},
				"suffix": map[string]interface{}{
					"option": "SHORT",
				},
			}},
		}
		res, err := req.Execute()
		log.IfError(err)

		var respMap map[string]interface{}
		if !log.IfError(json.Unmarshal([]byte(res.Body), &respMap)) {
			if respMap["shortLink"] != "" {
				productUrl = utils.ToString(respMap["shortLink"])
			} else {
				log.Error("firebase dynamic link doesn't return shortLink. %v", res.Body)
			}
		} else {
			fmt.Println("err json from firebase: " + res.Body)
		}
	}

	fmt.Println(getAppCrmId(utils.ToInt(adminId)), adminId, os.Getenv("server"))
	log.Info("url for '%s' --> %s", id, productUrl)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"url": productUrl,
	}})
}

func getAppCrmId(adminId int) string {
	if os.Getenv("server") == "development" {
		//if adminId == 1 {
		//	return "com.uniq.uniqmembership.yamiepanda"
		//}
		return "com.uniq.uniqmembership.redshit"
	} else if os.Getenv("server") == "staging" {
		if adminId == 10 {
			return "com.uniq.uniqmembership.yamiepanda"
		} else if adminId == 1 {
			return "com.uniq.uniqmembership.alive"
		}
	} else if os.Getenv("server") == "production" {
		if adminId == 2 {
			return "com.uniq.uniqmembership.packmarket"
		}
	}

	return ""
}
