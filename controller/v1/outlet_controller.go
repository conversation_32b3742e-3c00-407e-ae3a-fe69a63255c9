package v1

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

// func GetOutlet(ctx *fasthttp.RequestCtx) {
// 	//ctx.Response.Header.Set("Access-Control-Allow-Credentials", "true")
// 	//ctx.Response.Header.Set("Access-Control-Allow-Origin", "*")
// 	//log.Info("get outlet... | resp headers : %v | req headers : %v", ctx.Response.Header.String(), ctx.Request.Header.String())

// 	adminId := string(ctx.Request.Header.Peek("uid"))
// 	sql := `SELECT outlet_id, name, address, phone, country, province, city, postal_code,
// 	outlet_logo as receipt_logo, outlet_logo, latitude, longitude, feature
// 	FROM outlets WHERE admin_fkid = ? and data_status = 'on' and app_show = 1
// 	order by name`
// 	outlets, err := db.QueryArray(sql, adminId)

// 	if log.IfErrorSetStatus(ctx, err) {
// 		return
// 	}

// 	//get day open
// 	sql = `
// SELECT DAYOFWEEK(from_unixtime(time_open / 1000 + 25200, '%Y-%m-%d')) as dayOfWeek,
//        case DAYOFWEEK(from_unixtime(time_open / 1000 + 25200, '%Y-%m-%d'))
//            when 1 then 'Sunday'
//            when 2 then 'Monday'
//            when 3 then 'Tuesday'
//            when 4 then 'Wednesday'
//            when 5 then 'Thursday'
//            when 6 then 'Friday'
//            when 7 then 'Saturday'
//            end                                                        as day,
//        from_unixtime(min(time_open) / 1000 + 25200, '%H:%i')             as open,
//        from_unixtime(max(time_close) / 1000 + 25200, '%H:%i')            as close,
//        outlet_fkid
// from open_shift
//   where time_open >= (SELECT UNIX_TIMESTAMP(DATE_ADD(NOW(), INTERVAL -7 DAY)) * 1000 + 25200)
// group by dayOfWeek, day, outlet_fkid
// order by outlet_fkid, dayOfWeek`
// 	daysOpen, err := db.QueryArray(sql)
// 	log.IfError(err)

// 	for _, outlet := range outlets {
// 		dayOpenMap := make([]map[string]interface{}, 0)
// 		for _, dayOpen := range daysOpen {
// 			if utils.ToString(dayOpen["outlet_fkid"]) == utils.ToString(outlet["outlet_id"]) {
// 				dayOpenMap = append(dayOpenMap, dayOpen)
// 			}
// 			if len(dayOpenMap) == 7 {
// 				break
// 			}
// 		}
// 		outlet["business_hour"] = dayOpenMap
// 	}

// 	for i, outlet := range outlets {
// 		if outlet["outlet_logo"] == nil || outlet["outlet_logo"] == "" {
// 			outlets[i]["outlet_logo"] = utils.DEFAULT_IMAGE
// 		} else if !strings.HasPrefix(utils.ToString(outlet["outlet_logo"]), "https") {
// 			outlets[i]["outlet_logo"] = fmt.Sprintf("%s/assets/images/outlets/%s/%s", os.Getenv("base_url"), adminId, outlet["outlet_logo"])
// 		}

// 		if utils.ToString(outlet["outlet_logo"]) != "" {
// 			outlets[i]["receipt_logo"] = outlet["outlet_logo"]
// 		}

// 		if feature := cast.ToString(outlet["feature"]); feature != "" {
// 			var outletFeature models.OutletFeature
// 			log.IfError(json.Unmarshal([]byte(feature), &outletFeature))
// 			outlets[i]["feature"] = outletFeature
// 		}
// 	}

// 	log.Info("total outlet of adminId: %v is: %v", adminId, len(outlets))
// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: outlets})
// }

func GetDetailOutlet(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	outletId := ctx.UserValue("id")

	// log.Info("get outlet detail... | resp headers : %v | req headers : %v", ctx.Response.Header.String(), ctx.Request.Header.String())

	data, err := db.Query("SELECT outlet_id, name, address, phone, country, province, city, postal_code, "+
		"receipt_logo, outlet_logo, latitude, longitude, feature  FROM outlets WHERE outlet_id = ? and admin_fkid = ? and data_status = 'on' and app_show = 1 "+
		"order by name", outletId, adminId)

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	workingHours, err := db.QueryArray("select * from outlets_workinghour where outlet_fkid = ?", outletId)
	log.IfError(err)
	data["working_hour"] = workingHours
	data["enable_order"] = map[string]interface{}{
		"status": "enable",
	}

	if feature := cast.ToString(data["feature"]); feature != "" {
		var outletFeature models.OutletFeature
		log.IfError(json.Unmarshal([]byte(feature), &outletFeature))
		data["feature"] = outletFeature
	}

	if len(workingHours) > 0 {
		dateNow := time.Now().Add(7 * time.Hour)
		today := strings.ToLower(dateNow.Weekday().String())
		tNow, err := time.Parse("15:4:5", fmt.Sprintf("%d:%d:%d", dateNow.Hour(), dateNow.Minute(), dateNow.Day()))
		log.IfError(err)

		//set default to close (in case the day not found in db)
		data["enable_order"] = map[string]interface{}{
			"status": "disable",
			"reason": "closed",
		}

		for _, day := range workingHours {
			if utils.ToString(day["day"]) == today {
				tOpen, err := time.Parse("15:04:05", cast.ToString(day["time_open"]))
				log.IfError(err)

				tClose, err := time.Parse("15:04:05", cast.ToString(day["time_close"]))
				log.IfError(err)

				if tNow.Before(tOpen) || tNow.After(tClose) {
					data["enable_order"] = map[string]interface{}{
						"status": "disable",
						"reason": "closed",
					}
				} else {
					data["enable_order"] = map[string]interface{}{
						"status": "enable",
					}
				}
				break
			}
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: data})
}
