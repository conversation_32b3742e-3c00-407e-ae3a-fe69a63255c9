package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"mime/multipart"
	"net/url"
	"os"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/payment/midtrans"
	"gitlab.com/uniqdev/backend/api-membership/core/payment/xendit"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/generate"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

const (
	PrefixId = "INV"
)

func OrderSale(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	tgl, _ := utils.MillisToDateTime(time.Now().Unix() + 25200)

	order := models.Order{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&order)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Debug("order online : %v", utils.SimplyToJson(order))

	if len(order.OrderList) == 0 {
		log.Warn("order error : no item")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "at least please select 1 product", Code: 102})
		return
	}

	if order.IDOrder[:3] != "CRM" {
		log.Warn("order error : invalid order id")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "order id prefix didn't match", Code: 100})
		return
	}

	transactionId := order.Payments[0].TransactionId
	//check transactionID
	data, err := db.Query("SELECT transaction_id, info FROM payment_notification WHERE transaction_id = ? limit 1", transactionId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	//orderStatus := "pending"
	if len(data) == 0 {
		log.Info("transaction_id not found id database")
		paymentStatus, err := midtrans.Default().CheckPaymentStatus(transactionId)
		//orderStatus = GetOrderStatus(paymentStatus.TransactionStatus)
		log.IfError(err)
		if paymentStatus.StatusCode == midtrans.PAYMENT_NOT_FOUND {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "transaction ID not found", Code: 101})
			return
		}
	}

	var paymentNotif models.PaymentNotification
	if data["info"] != nil {
		info := utils.ToString(data["info"])
		fmt.Printf("json : '%s'", info)
		err = json.Unmarshal([]byte(info), &paymentNotif)
		if log.IfErrorSetStatus(ctx, err) {
			fmt.Println("Parse json payment notif error ", err)
			return
		}
	}

	bankId := int64(0)
	bank, err := db.Query("SELECT * FROM payment_media_bank where name = ? and admin_fkid = ?", paymentNotif.VaNumbers[0].Bank, adminId)
	if len(bank) > 0 {
		bankId = utils.ToInt64(bank["bank_id"])
	} else {
		resp, err := db.Insert("payment_media_bank", map[string]interface{}{
			"name":          paymentNotif.VaNumbers[0].Bank,
			"no_rekening":   "0000",
			"admin_fkid":    adminId,
			"data_created":  tgl,
			"data_modified": tgl,
			"data_status":   "on",
		})
		log.IfError(err)
		bankId, _ = resp.LastInsertId()
	}

	member, err := db.Query("select name from members m join members_detail md on m.member_id = md.member_fkid where member_id = ? and admin_fkid = ? ", memberId, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}
	if len(member) <= 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "member id not found!"})
		return
	}
	memberName := utils.ToString(member["name"])

	//productDetail, err := db.Query("select outlet_fkid from products_detail pd join products p on pd.product_fkid = p.product_id "+
	//	"where product_detail_id = ? and admin_fkid = ? limit 1", order.OrderList[0].ProductDetailID, adminId)
	//if utils.CheckError(ctx, err) {
	//	return
	//}

	sales := map[string]interface{}{
		"sales_id":      order.IDOrder,
		"noNota":        order.IDOrder,
		"displayNota":   order.IDOrder,
		"display_nota":  order.IDOrder,
		"payment":       "CARD",
		"customer":      memberName,
		"member_fkid":   memberId,
		"customersQty":  1,
		"data_status":   "on",
		"outletFkid":    order.OutletId,
		"status":        "Success",
		"outletID":      order.OutletId,
		"date_created":  tgl,
		"date_modified": tgl,
		"timeCreated":   time.Now().Unix() * 1000,
		"timeModified":  time.Now().Unix() * 1000,
		"grandTotal":    order.GrandTotal,
		"table":         "",
		//"taxes":         "[]",
		"discount": map[string]interface{}{
			"discount":        0,
			"discountNominal": 0,
			"discount_info":   "-",
		},
	}

	//save to sales
	//_, err = db.Insert("sales", sales)
	//if utils.CheckErr(err) {
	//	ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	//	return
	//}

	payments := make([]map[string]interface{}, 0)
	products := make([]map[string]interface{}, 0)
	menuNames := ""
	for _, menu := range order.OrderList {
		menuNames += menu.Name + ", "
		product := map[string]interface{}{
			"sales_fkid":          order.IDOrder,
			"time_created":        time.Now().Unix() * 1000,
			"product_fkid":        menu.ProductID,
			"product_detail_fkid": menu.ProductDetailID,
			"qty":                 menu.Qty,
			"price":               menu.Price,
			"sub_total":           menu.Price * menu.Qty,
			"tmpId":               time.Now().UnixNano() / 1000000,
			"employee_fkid":       1,
			"product": map[string]interface{}{
				"name":              menu.Name,
				"product_detail_id": menu.ProductDetailID,
				"product_id":        menu.ProductID,
			},
		}

		products = append(products, product)
		//_, err = db.Insert("sales_detail", product)
		//utils.CheckErr(err)
	}

	for _, payment := range order.Payments {
		if payment.Method == "midtrans" {
			pay := map[string]interface{}{
				"sales_fkid":   order.IDOrder,
				"method":       "CARD",
				"total":        payment.Total,
				"pay":          payment.Total,
				"time_created": time.Now().Unix() * 1000,
				"bank_fkid":    bankId,
				"bank": map[string]interface{}{
					"name":           paymentNotif.VaNumbers[0].Bank,
					"bank_id":        bankId,
					"account_number": "",
				},
			}
			payments = append(payments, pay)
		}
	}

	sales["payments"] = payments
	sales["orderList"] = products
	sales["order_id"] = order.IDOrder
	items, _ := json.Marshal(sales)

	_, err = db.Insert("order_sales", map[string]interface{}{
		"order_sales_id": transactionId,
		"order_type":     "pickup",
		"status":         "pending",
		"pickup_time":    order.PickupTime,
		"outlet_fkid":    order.OutletId,
		"time_order":     time.Now().Unix() * 1000,
		"time_modified":  time.Now().UnixNano() / 1000000,
		"items":          string(items),
	})

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	/*	tgl, _ := utils.Date(time.Now().Unix() * 1000)
		_, err = db.Insert("sales", map[string]interface{}{
			"sales_id":      order.IDOrder,
			"payment":       "CARD",
			"member_fkid":   memberId,
			"qty_customers": 0,
			"data_status":   "on",
			"outlet_fkid":   productDetail["outlet_fkid"],
			"status":        "success",
			"date_created":  tgl,
			"date_modified": tgl,
			"time_created":  time.Now().Unix() * 1000,
			"time_modified": time.Now().Unix() * 1000,
			"discount":      0,
			"voucher":       0,
			"grand_total":   order.GrandTotal,
			"display_nota":  order.IDOrder,
		})

		if utils.CheckError(ctx, err) {
			return
		}

		for _, detail := range order.OrderList {
			res, err := db.Insert("sales_detail", map[string]interface{}{
				"sales_fkid":          order.IDOrder,
				"time_created":        time.Now().Unix() * 1000,
				"product_fkid":        detail.ProductID,
				"product_detail_fkid": detail.ProductDetailID,
				"qty":                 detail.Qty,
				"price":               detail.Price,
				"sub_total":           detail.SubTotal,
				"discount":            0,
			})
			if utils.CheckError(ctx, err) {
				return
			}

			detailId, _ := res.LastInsertId()
			for _, tax := range detail.Taxes {
				_, err := db.Insert("sales_detail_tax", map[string]interface{}{
					"tax_fkid":          tax.ID,
					"total":             tax.Total,
					"sales_detail_fkid": detailId,
				})
				if utils.CheckError(ctx, err) {
					return
				}
			}
		}

		for _, tax := range order.Taxes {
			_, err := db.Insert("sales_tax", map[string]interface{}{
				"tax_fkid":   tax.ID,
				"total":      tax.Total,
				"sales_fkid": order.IDOrder,
			})
			if utils.CheckError(ctx, err) {
				return
			}
		}*/

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func CreateOrder(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	fmt.Println(adminId, memberId)
	log.Info("new online transaction: %s\n", utils.SimplyToJson(string(ctx.PostBody())))
	var sales models.Sales
	var orderSale map[string]interface{}
	log.IfError(json.Unmarshal(ctx.PostBody(), &orderSale))
	if orderSale["items"] == nil { //old version
		err := json.Unmarshal(ctx.PostBody(), &sales)
		if log.IfError(err) {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			return
		}
	} else {
		err := json.Unmarshal([]byte(utils.ToString(orderSale["items"])), &sales)
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			return
		}
	}

	orderType := sales.OrderType
	if orderType == "" {
		orderType = "delivery"
	}

	timeStart := time.Now()

	//validation
	sql := "SELECT id from order_configuration_detail_ordertype_availability where outlet_fkid = ? and order_type = ? limit 1"
	orderConfig, err := db.Query(sql, sales.OutletId, orderType)
	log.IfError(err)

	if len(orderConfig) == 0 {
		log.Info("order type: %s is disable for outlet %v", orderType, sales.OutletId)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: fmt.Sprintf("Transaksi %s tidak dapat dilakukan di outlet ini", orderType)})
		return
	}

	logTime := fmt.Sprintf("validation: %v", time.Since(timeStart))
	timeStart = time.Now()

	//generate id
	//get code
	sql = `select display_nota
from sales
where outlet_fkid = ?
#and sales_id not in (select CAST(CONVERT(order_sales_id USING utf8) AS binary) from order_sales)
order by time_created desc
limit 1`
	displayNote, err := db.Query(sql, sales.OutletId)
	log.IfError(err)

	log.Info("outletId: %d - %v", sales.OutletId, displayNote)

	//t, _ := time.Parse("2006/01/02 15:04", time.Now().Format("2006/01/02") + " 00:00")
	//startTime := t.Unix() * 1000

	sql = `select count(*) as cnt
from order_sales
where outlet_fkid = ?
  and DATE_FORMAT(from_unixtime(time_order / 1000), '%Y-%m-%d') = DATE_FORMAT(now(), '%Y-%m-%d')`
	cnt, err := db.Query(sql, sales.OutletId)
	log.IfError(err)

	if displayNote["display_nota"] == nil {
		displayNote["display_nota"] = "TRX"
	}
	code := regexp.MustCompile("[0-9]+").ReplaceAllString(utils.ToString(displayNote["display_nota"]), "")
	dates := time.Now().UTC().Add(7 * time.Hour).Format("20060102")
	transactionId := fmt.Sprintf("%s-%s%s%d", PrefixId, code, dates, utils.ToInt(cnt["cnt"])+1)
	sales.DisplayNota = transactionId
	//sales.UniqueCode = 0 //rand.Intn(99) + 1
	grandTotalPayment := sales.GrandTotal + sales.UniqueCode

	logTime += fmt.Sprintf(" | generateId: %v", time.Since(timeStart))
	timeStart = time.Now()

	for _, item := range sales.OrderList {
		item.SubTotal = item.Price * item.Qty
	}

	for _, promo := range sales.Promotions {
		grandTotalPayment -= promo.Value
	}

	items, err := json.Marshal(sales)
	log.IfError(err)

	resp, err := db.Insert("order_sales", map[string]interface{}{
		"order_sales_id":      transactionId,
		"order_type":          orderType,
		"status":              "pending",
		"member_fkid":         memberId,
		"outlet_fkid":         sales.OutletId,
		"time_order":          time.Now().Unix() * 1000,
		"time_modified":       time.Now().UnixNano() / 1000000,
		"items":               string(items),
		"grand_total":         sales.GrandTotal,
		"grand_total_payment": grandTotalPayment,
		"order_note":          orderSale["order_note"],
	})

	if log.IfErrorSetStatus(ctx, err) {
		return
	}
	orderId, _ := resp.LastInsertId()

	logTime += fmt.Sprintf(" | insert: %v", time.Since(timeStart))
	timeStart = time.Now()

	//log history status
	go func(orderId int64, transactionId, adminId string) {
		_, err = db.Insert("order_sales_status_log", map[string]interface{}{
			"order_sales_fkid": orderId,
			"order_sales_id":   transactionId,
			"status":           "pending",
			"admin_id":         adminId,
			"time_created":     time.Now().UnixNano() / 1000000,
		})
		log.IfError(err)
	}(orderId, transactionId, adminId)

	if orderSale["shipment"] != nil {
		go func(shipmentData interface{}, memberId string, orderId int64, transactionId string) {
			shipmentStr, err := json.Marshal(shipmentData)
			log.IfError(err)

			var shipment map[string]interface{}
			log.IfError(json.Unmarshal(shipmentStr, &shipment))

			memberData, _ := db.Query("select name, phone from members where member_id = ?", memberId)

			if shipment["shipping_phone"] == nil {
				shipment["shipping_phone"] = memberData["phone"]
			}

			shipment["shipping_name"] = memberData["name"]
			shipment["order_sales_fkid"] = orderId
			shipment["order_sales_id"] = transactionId
			shipment["time_created"] = time.Now().Unix() * 1000
			shipment["time_modified"] = time.Now().Unix() * 1000

			_, err = db.Insert("order_sales_shipments", shipment)
			log.IfError(err)
		}(orderSale["shipment"], memberId, orderId, transactionId)
	}

	go UpdatePromotionBuy(sales, "redeem")
	go PublishOrderStatusUpdate(transactionId, "pending")
	go RemoveItemFromCart(sales, memberId)

	logTime += fmt.Sprintf(" | ending: %v", time.Since(timeStart))
	log.Info("new online transaction timeLog: %v", logTime)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"order_sales_id": transactionId,
	}})
}

func RemoveItemFromCart(sales models.Sales, memberId string) {
	params := make([]interface{}, 0)
	params = append(params, memberId)
	questionMarks := make([]string, 0)

	for _, item := range sales.OrderList {
		params = append(params, item.ProductDetailFkid)
		questionMarks = append(questionMarks, "?")
	}

	whereIn := strings.Join(questionMarks, ",")
	log.Info("remove these from cart: %v", params[1:])

	db.Delete("crm_cart", "member_fkid = ? and product_detail_fkid in ("+whereIn+")", params...)
}

func UpdatePromotionBuy(sales models.Sales, status string) {
	log.Info("%s has total promotions: %d", sales.SalesID, len(sales.Promotions))
	for _, promo := range sales.Promotions {
		log.Info("updating promotion_buy id : '%s' to '%s'", promo.Code, status)
		_, err := db.Update("promotion_buy", map[string]interface{}{
			"status":        status,
			"time_modified": time.Now().Unix() * 1000,
		}, "promotion_buy_id = ?", promo.Code)
		log.IfError(err)
	}

	for _, item := range sales.OrderList {
		if item.Promotion != nil && item.Promotion.Code != "" {
			log.Info("updating promotion_buy id : '%s' to '%s'", item.Promotion.Code, status)
			_, err := db.Update("promotion_buy", map[string]interface{}{
				"status":        status,
				"time_modified": time.Now().Unix() * 1000,
			}, "promotion_buy_id = ?", item.Promotion.Code)
			log.IfError(err)
		}
	}
}

func UpdateOrder(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	orderId := ctx.UserValue("id")

	log.Info("update order: %v to status: %s", orderId, ctx.FormValue("status"))
	header, err := ctx.FormFile("attachment")
	// if string(ctx.FormValue("status")) == "payment_verification" {
	// 	// headerAttachment, errAttachment := ctx.FormFile("attachment")
	// 	if err != nil {
	// 		ctx.SetStatusCode(fasthttp.StatusUnprocessableEntity)
	// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
	// 		return
	// 	}
	// 	if header == nil {
	// 		ctx.SetStatusCode(fasthttp.StatusBadRequest)
	// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "attachment is required..."})
	// 		return
	// 	}
	// }

	var sales models.Sales
	var orderSale map[string]interface{}
	err = json.Unmarshal(ctx.PostBody(), &orderSale)
	fmt.Printf("can not convert body to orderSale --> %s \nerr: %v\n", string(ctx.PostBody()), err)
	if err == nil && orderSale["items"] != nil {
		//validation
		//only order with status 'reject' can be updated (the items)
		order, err := db.Query("select status, order_sales_fkid from order_sales_status_log where order_sales_id = ? order by id desc limit 1", orderId)
		log.IfError(err)

		if len(order) == 0 {
			ctx.SetStatusCode(fasthttp.StatusNotFound)
			return
		}

		if utils.ToString(order["status"]) != "reject" {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "only reject order can be updated"})
			return
		}

		err = json.Unmarshal([]byte(utils.ToString(orderSale["items"])), &sales)
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			return
		}

		oldOrder, err := db.Query("select items from order_sales where order_sales_id = ? limit 1", orderId)
		log.IfError(err)

		var salesOld models.Sales
		log.IfError(json.Unmarshal([]byte(utils.ToString(oldOrder["items"])), &salesOld))

		log.Info("current unique code: %d --> %s", salesOld.UniqueCode, oldOrder["items"])
		sales.UniqueCode = salesOld.UniqueCode
		itemsNew, _ := json.Marshal(sales)

		_, err = db.Update("order_sales", map[string]interface{}{
			"items":       string(itemsNew),
			"grand_total": sales.GrandTotal,
		}, "order_sales_id = ?", orderId)
		if log.IfError(err) {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			return
		}

		orderSalesFkid := utils.ToInt64(order["order_sales_fkid"])
		go func(orderId, adminId interface{}, orderSalesFkid int64) {
			//log history status
			orderLog := map[string]interface{}{
				"order_sales_id": orderId,
				"status":         "pending",
				"admin_id":       adminId,
				"time_created":   time.Now().UnixNano() / 1000000,
			}
			if orderSalesFkid > 0 {
				orderLog["order_sales_fkid"] = orderSalesFkid
			}
			_, err = db.Insert("order_sales_status_log", orderLog)
			log.IfError(err)

			_, err = db.Update("order_sales", map[string]interface{}{
				"status":        "pending",
				"time_modified": time.Now().UnixNano() / 1000000,
			}, "order_sales_id = ?", orderId)
			log.IfError(err)

			//_, err = db.Delete("order_sales_shipments", "order_sales_id = ?", orderId)
			//log.IfError(err)

			PublishOrderStatusUpdate(utils.ToString(orderId), "pending")
		}(orderId, adminId, orderSalesFkid)

		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
			"order_sales_id": orderId,
		}})
		return
	}

	//status available: cancel, payment_verification (should include attachment)
	statusAvailable := []string{"cancel", "payment_verification", "received"}
	status := string(ctx.FormValue("status"))
	log.Info("updating order '%s' to -> %s", orderId, status)
	if status != "" {
		isValidStatus := false
		for _, st := range statusAvailable {
			if status == st {
				isValidStatus = true
			}
		}

		if !isValidStatus {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid status"})
			return
		}

		resp, err := db.Update("order_sales", map[string]interface{}{
			"status":        status,
			"time_modified": time.Now().UnixNano() / 1000000,
		}, "order_sales_id = ? and member_fkid = ?", orderId, memberId)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		rows, _ := resp.RowsAffected()
		log.Info("total row updated: %d", rows)
		if rows == 0 {
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "not found"})
			return
		}

		order, err := db.Query("select status,order_sales_fkid from order_sales_status_log where order_sales_id = ? order by id desc limit 1", orderId)
		log.IfError(err)

		resp, err = db.Insert("order_sales_status_log", map[string]interface{}{
			"order_sales_id":   orderId,
			"order_sales_fkid": order["order_sales_fkid"],
			"status":           status,
			"admin_id":         adminId,
			"time_created":     time.Now().UnixNano() / 1000000,
		})
		log.IfError(err)
		logId, _ := resp.LastInsertId()

		go PublishOrderStatusUpdate(utils.ToString(orderId), status)

		//upload attachment
		// header, err := ctx.FormFile("attachment")
		if err == nil && header != nil {
			fmt.Println("payment confirm filename : ", header.Filename)
			go func(orderId, logId interface{}, header *multipart.FileHeader) {
				basePath := "temp/po"
				_, err = os.Stat(basePath)
				if os.IsNotExist(err) {
					log.IfError(os.MkdirAll(basePath, os.ModePerm))
				}

				imgFileName := adminId + "_" + utils.RandStringBytes(16) + utils.ToString(time.Now().Unix()) + ".jpg"
				tmpFileName := fmt.Sprintf("%s/%s", basePath, imgFileName)
				err = fasthttp.SaveMultipartFile(header, tmpFileName)
				if log.IfError(err) {
					return
				}

				file, err := os.Open(tmpFileName)
				if !log.IfError(err) {
					//defer log.IfError(file.Close())
					filePath := os.Getenv("server") + "/crm/order/" + adminId + "/" + imgFileName
					imgUrl, err := google.UploadFile(file, filePath, true)
					logMsg, _ := json.Marshal(map[string]interface{}{
						"attachment": imgUrl,
					})
					if err == nil {
						_, err = db.Update("order_sales_status_log", map[string]interface{}{
							"message": string(logMsg),
						}, "id = ?", logId)
						log.IfError(err)
					}
				}
				if err = os.Remove(tmpFileName); err != nil {
					fmt.Println("Removing tmp file : ", tmpFileName, " - error : ", err)
				}

			}(orderId, logId, header)
		}

		if status == "cancel" {
			go func(orderId interface{}) {
				order, err := db.Query("select items from order_sales where order_sales_id = ?", orderId)
				log.IfError(err)

				var sales models.Sales
				log.IfError(json.Unmarshal([]byte(utils.ToString(order["items"])), &sales))
				UpdatePromotionBuy(sales, "available")
			}(orderId)
		}

		//go PublishOrderStatusUpdate(utils.ToString(orderId), status)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
	}

}

func GetHistoryDetailOrder(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	orderId := ctx.UserValue("id")

	data, err := db.Query("select * from order_sales where order_sales_id = ? and member_fkid = ?", orderId, memberId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	orderShipment, err := db.Query("select * from order_sales_shipments where order_sales_id = ?", orderId)
	log.IfError(err)

	if len(orderShipment) > 0 && utils.ToString(data["order_type"]) == "delivery" {
		data["shipment"] = orderShipment
	}

	if data["payment_info"] != nil {
		var paymentInfo map[string]interface{}
		err = json.Unmarshal([]byte(utils.ToString(data["payment_info"])), &paymentInfo)
		if err == nil {
			data["payment_info"] = paymentInfo
		} else {
			log.Info("failed to convert to map... %v", data["payment_info"])
		}
	}

	//get status
	sql := `select *
from order_sales_status_log
where order_sales_id = ?
order by id desc
limit 1`
	status, err := db.Query(sql, orderId)
	if !log.IfError(err) {
		data["status"] = status["status"]
		if data["status"] != "payment_verification" {
			data["message"] = status["message"]
		}
	}

	//get payment bank info
	if data["status"] != "pending" {
		sql = `SELECT
    pmb.bank_id,
    pmb.name,
    pmb.no_rekening,
	pmb.owner
FROM
    payment_media_bank_detail pmbd
JOIN payment_media_bank pmb ON
    pmb.bank_id = pmbd.bank_fkid
WHERE
    pmbd.outlet_fkid = ? AND pmb.admin_fkid = ? AND pmb.data_status = 'on' AND LENGTH(pmb.no_rekening) > 5
	and pmbd.data_status='on' and pmbd.active_on_crm = 1 `
		banks, err := db.QueryArray(sql, data["outlet_fkid"], adminId)
		log.IfError(err)
		data["payment_media"] = banks

		dataBankTransfer := make([]map[string]interface{}, 0)
		for _, bank := range banks {
			if bank["owner"] != nil {
				bank["owner"] = fmt.Sprintf("a/n. %s", bank["owner"])
			}
			dataBankTransfer = append(dataBankTransfer, map[string]interface{}{
				"name": bank["name"],
				"id":   bank["bank_id"],
				"info": bank["owner"],
				"payment_info": map[string]interface{}{
					"type":  "text",
					"value": bank["no_rekening"],
				},
			})
		}

		paymentMethods := make([]map[string]interface{}, 0)
		if len(dataBankTransfer) > 0 {
			paymentMethods = append(paymentMethods, map[string]interface{}{
				"type":  "bank_transfer",
				"title": "Transfer Bank (Verifikasi Manual)",
				"data":  dataBankTransfer,
			})
		}

		isUseQris := os.Getenv("server") == "development"
		if !isUseQris {
			payConfig, err := db.Query("select * from order_configuration where config = ? and admin_id = ?", "payment_gateway", adminId)
			log.IfError(err)
			isUseQris = len(payConfig) > 0
		}

		if isUseQris {
			paymentMethods = append(paymentMethods, map[string]interface{}{
				"type":           "e_wallet",
				"title":          "E-Wallet",
				"require_detail": true,
				"data": []map[string]interface{}{
					{
						"name": "QRIS",
						"id":   "qris",
					},
				},
			})
		}
		data["payment"] = paymentMethods
	}

	feedback, err := db.Query("select stars, comment from sales_feedback where sales_fkid = ?", orderId)
	log.IfError(err)
	data["feedback"] = feedback

	//set expired manual
	sql = `
SELECT Max(time_created) AS time_created
FROM   order_sales_status_log
WHERE  status = 'accept'
       AND order_sales_id = ? `
	orderStatusAccept, err := db.Query(sql, orderId)
	timeAccept := utils.ToInt64(orderStatusAccept["time_created"])
	if !log.IfError(err) && timeAccept > 0 {
		orderExpiredAt := time.Unix(timeAccept/1000, 0).Add(24*time.Hour).Unix() * 1000
		data["time_order_expired"] = orderExpiredAt
		if orderExpiredAt <= (time.Now().Unix() * 1000) {
			data["status"] = "cancel"
			data["message"] = "melebihi batas waktu pembayaran"
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func GetOrderPaymentDetail(ctx *fasthttp.RequestCtx) {
	paymentType := string(ctx.QueryArgs().Peek("type"))
	paymentId := string(ctx.QueryArgs().Peek("id"))
	orderId := utils.ToString(ctx.UserValue("id"))
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	if paymentType == "bank_transfer" {
		paymentBank, err := db.Query("select name, no_rekening, owner from payment_media_bank where bank_id = ? and admin_fkid = ?", paymentId, adminId)
		log.IfError(err)

		info := ""
		if paymentBank["owner"] != nil && utils.ToString(paymentBank["owner"]) != "" {
			info = fmt.Sprintf("a/n. %s", paymentBank["owner"])
		}

		responseData := map[string]interface{}{
			"type": paymentType,
			"id":   paymentId,
			"info": info,
			"name": utils.ToString(paymentBank["name"]),
			"payment_info": map[string]interface{}{
				"type":  "text",
				"value": utils.ToString(paymentBank["no_rekening"]),
			},
		}

		go savePaymentInfo(orderId, responseData)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: responseData})
		return
	}

	if paymentType != "e_wallet" && paymentId != "qris" {
		return
	}

	//xendit or midtrans still using dev environment, can not be used in production
	if paymentId == "qris" && os.Getenv("server") == "production" {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	sql := `
select *
from order_sales os
         left join order_sales_shipments oss on os.order_sales_id = oss.order_sales_id
where os.order_sales_id = ?
  and os.member_fkid = ? `
	orderSales, err := db.Query(sql, orderId, memberId)
	log.IfError(err)

	if len(orderSales) == 0 {
		ctx.SetStatusCode(fasthttp.StatusNotFound)
		log.IfError(fmt.Errorf("no order sales found for id: %s", orderId))
		return
	}

	var sales models.Sales
	log.IfError(json.Unmarshal([]byte(utils.ToString(orderSales["items"])), &sales))

	promotionTotal := 0
	for _, promo := range sales.Promotions {
		promotionTotal += promo.Value
	}
	for _, order := range sales.OrderList {
		if order.Promotion != nil {
			promotionTotal += order.Promotion.Value
		}
	}

	grandTotal := utils.ToInt(orderSales["grand_total"]) + utils.ToInt(orderSales["shipping_charge"]) + utils.ToInt(sales.UniqueCode) - promotionTotal

	//get member data
	memberData, err := db.Query("select name, email, phone from members m where member_id = ?", memberId)
	log.IfError(err)

	paymentData := models.Payment{
		OrderId:   orderId,
		GranTotal: grandTotal,
		Customer: models.Customer{
			Name:  cast.ToString(memberData["name"]),
			Email: cast.ToString(memberData["email"]),
			Phone: cast.ToString(memberData["phone"]),
		},
	}

	//get payment setting
	paymentConfig, err := db.Query("select * from order_configuration where config = 'payment_gateway' and admin_id = ?", adminId)
	log.IfError(err)

	if len(paymentConfig) == 0 {
		if os.Getenv("server") == "development" {
			paymentConfig["value"] = fmt.Sprintf("midtrans-%s", midtrans.Default().ServerKey)
		} else {
			ctx.SetStatusCode(fasthttp.StatusForbidden)
			return
		}
	}

	i := strings.Index(cast.ToString(paymentConfig["value"]), "-")
	if i <= 0 {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	var paymentResult models.PaymentResult
	paymentGateway := cast.ToString(paymentConfig["value"])[:i]
	key := cast.ToString(paymentConfig["value"])[i+1:]
	log.Info("gateway: %s | key: '%s'", paymentGateway, key)
	if paymentGateway == "midtrans" {
		paymentResult, err = midtrans.Config{ServerKey: key}.CreateQrisPayment(paymentData)
	} else if paymentGateway == "xendit" {
		paymentResult, err = xendit.Config{SecretKey: key}.CreatePaymentQris(paymentData)
	} else {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	qrPayment := paymentResult.QrCode

	if qrPayment == "" {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		log.IfError(fmt.Errorf("create payment error: can not get qr url"))
		return
	}

	//save payment resp result
	//go func() {
	//	var respMap map[string]interface{}
	//
	//	db.Insert("payment_notification", map[string]interface{}{
	//		"transaction_id": respMap["transaction_id"],
	//		"order_id": orderId,
	//		"info": paymentResult.Response,
	//		"transaction_status": "pending",
	//	})
	//}()

	////create request
	////Midtrans
	//midtransPayment := midtrans.Default()
	//paymentStatus, err := midtransPayment.CheckPaymentStatus(orderId)
	//log.IfError(err)
	//
	////if pending cancel and create new
	//if paymentStatus.TransactionStatus == "pending" {
	//	_, err = midtrans.Default().UpdatePaymentStatus(orderId, midtrans.PAYMENT_STATUS_CANCEL)
	//	log.IfError(err)
	//}
	//
	//data := map[string]interface{}{
	//	"payment_type": "qris",
	//	"qris": map[string]interface{}{
	//		"acquirer": "airpay shopee", //or gopay
	//	},
	//	"transaction_details": map[string]interface{}{
	//		"order_id":     orderId,
	//		"gross_amount": grandTotal,
	//	},
	//	//"item_details": []map[string]interface{}{
	//	//	{
	//	//		"id":       "id1",
	//	//		"price":    0,
	//	//		"quantity": 1,
	//	//		"name":     "minuman",
	//	//	},
	//	//},
	//	"customer_details": map[string]interface{}{
	//		"first_name": memberData["name"],
	//		"email":      memberData["email"],
	//		"phone":      memberData["phone"],
	//	},
	//}
	//
	////check if transaction created
	//req := utils.HttpRequest{
	//	Method: "POST",
	//	Url:    "https://api.sandbox.midtrans.com/v2/charge",
	//	Header: map[string]interface{}{
	//		"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:", os.Getenv("MIDTRANS_SECRET_KEY")))),
	//		"Content-Type":  "application/json",
	//	},
	//	PostRequest: utils.PostRequest{
	//		Body: data,
	//	},
	//}
	//
	//resp, err := req.Execute()
	//log.IfError(err)
	//log.Info("midtrans response: %s", resp.Body)
	//
	////Xendit:
	////check if qr-code is created before
	////req := utils.HttpRequest{
	////	Method: "GET",
	////	Url:    fmt.Sprintf("https://api.xendit.co/qr_codes/%s", orderId),
	////	Header: map[string]interface{}{
	////		"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:", os.Getenv("XENDIT_SECRET_KEY")))),
	////	},
	////}
	////resp, err := req.Execute()
	////log.IfError(err)
	////
	////if resp.StatusCode == 404 {
	////	req = utils.HttpRequest{
	////		Method: "POST",
	////		Url:    "https://api.xendit.co/qr_codes",
	////		Header: map[string]interface{}{
	////			"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:", os.Getenv("XENDIT_SECRET_KEY")))),
	////		},
	////		PostRequest: utils.PostRequest{Form: map[string]string{
	////			"external_id":  orderId,
	////			"type":         "DYNAMIC",
	////			"callback_url": "https://uniqdev.xyz",
	////			"amount":       utils.ToString(utils.ToInt(orderSales["grand_total"])),
	////		}},
	////	}
	////	resp, err = req.Execute()
	////	log.IfError(err)
	////}
	//
	//if resp.StatusCode != 200 {
	//	ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	//	log.IfError(fmt.Errorf("request to xendit err, code %d - %s", resp.StatusCode, resp.Body))
	//	return
	//}
	//
	//var result map[string]interface{}
	//log.IfError(json.Unmarshal([]byte(resp.Body), &result))
	//
	//if code, ok := result["status_code"]; ok {
	//	if utils.ToInt(code) != 200 {
	//		log.IfError(fmt.Errorf("request failed. response : %s", resp.Body))
	//	}
	//}
	//
	//urlQr := ""
	//if actions, ok := result["actions"].([]interface{}); ok {
	//	if action1, ok1 := actions[0].(map[string]interface{}); ok1 {
	//		urlQr = utils.ToString(action1["url"])
	//		log.Info("url qr : %s", urlQr)
	//	}
	//}

	//for development
	//if os.Getenv("server") == "development" && adminId == "7" {
	//	_, err = db.Insert("scheduled_message", map[string]interface{}{
	//		"title":        "QRIS",
	//		"message":      urlQr,
	//		"time_deliver": time.Now().Unix() * 1000,
	//		"data_created": time.Now().Unix() * 1000,
	//		"media":        "whatsapp",
	//		"receiver":     "6281392296075",
	//	})
	//	log.IfError(err)
	//}

	// qrCode := utils.ParseQuery("https://chart.googleapis.com/chart", map[string]interface{}{
	// 	"chs": "300x300",
	// 	"chl": qrPayment,
	// 	"cht": "qr",
	// })
	qrCode := generate.QrCode(qrPayment, 300)

	log.Info("order: %s --> qr: %s", orderId, qrCode)

	responseData := map[string]interface{}{
		"type": paymentType,
		"id":   paymentId,
		"name": "QRIS",
		"payment_info": map[string]interface{}{
			"type":       "image",
			"value":      qrCode,
			"expired_at": time.Now().Add(5*time.Minute).Unix() * 1000,
		},
	}

	if os.Getenv("server") == "development" {
		var result map[string]interface{}
		_ = json.Unmarshal([]byte(paymentResult.Response), &result)

		if actions, ok := result["actions"].([]interface{}); ok {
			if action1, ok1 := actions[0].(map[string]interface{}); ok1 {
				responseData["image_url_simulation"] = utils.ToString(action1["url"])
			}
		}
	}

	go savePaymentInfo(orderId, responseData)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: responseData})
}

func savePaymentInfo(orderId string, paymentData interface{}) {
	paymentInfo, err := json.Marshal(paymentData)
	log.IfError(err)

	_, err = db.Update("order_sales", map[string]interface{}{
		"payment_info": string(paymentInfo),
	}, "order_sales_id = ?", orderId)
	log.IfError(err)
}

func GetHistory(ctx *fasthttp.RequestCtx) {
	//did'n yet implement in production
	//if os.Getenv("server") == "production" {
	//	emptyData := make([]map[string]interface{}, 0)
	//	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: emptyData, Message: "success"})
	//	return
	//}

	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	startRequest := time.Now()

	page := 1
	if ctx.UserValue("page") != nil {
		page = utils.ToInt(ctx.UserValue("page"))
	}

	var wg sync.WaitGroup
	dealsChan := make(chan []map[string]interface{}, 1)
	salesChan := make(chan []map[string]interface{}, 1)
	ordersChan := make(chan []map[string]interface{}, 1)

	maxRequest := 10 //max request per type : deals, sales, order
	offset := maxRequest * (page - 1)

	sql := `
select pb.promotion_buy_id as id, pb.time_created, pbp.status, pb.price as total, 'buy_deals' as type, p.name as item from promotion_buy pb
join promotion_buy_payment pbp on pb.promotion_buy_id = pbp.promotion_buy_fkid
join promotions p on pb.promotion_fkid = p.promotion_id
where price_type='money' and admin_fkid = ? and pb.member_fkid = ?
order by time_created desc limit ? offset ? `

	wg.Add(1)
	go db.QueryArrayGo(&wg, dealsChan, sql, adminId, memberId, maxRequest, offset)

	sql = `
select s.sales_id,s.status, s.grand_total, left(group_concat(p.name), 103) as items, s.time_created
from sales s
         join sales_detail sd on s.sales_id=sd.sales_fkid
join products p on sd.product_fkid = p.product_id
join outlets o on s.outlet_fkid = o.outlet_id
left join order_sales os on CAST(CONVERT(os.order_sales_id USING utf8) AS binary) = s.sales_id
where s.member_fkid = ?
and o.admin_fkid = ?
and s.data_status='on'
and os.id is null
group by s.sales_id
order by s.time_created desc limit ? offset ? `

	wg.Add(1)
	go db.QueryArrayGo(&wg, salesChan, sql, memberId, adminId, maxRequest, offset)

	sql = `select os.* from order_sales os
	join outlets o on o.outlet_id=os.outlet_fkid
	 where member_fkid = ? and o.admin_fkid = ?
	 order by os.time_order desc limit ? offset ? `
	wg.Add(1)
	go db.QueryArrayGo(&wg, ordersChan, sql, memberId, adminId, maxRequest, offset)

	wg.Wait()
	deals := <-dealsChan
	sales := <-salesChan
	orders := <-ordersChan

	result := make([]models.Transaction, 0)
	for _, d := range deals {
		item := utils.ToString(d["item"])
		log.Info("len : %d", len(item))
		if len(item) > 100 {
			item = fmt.Sprintf("%s...", item[:100])
		}
		result = append(result, models.Transaction{
			Id:          utils.ToString(d["id"]),
			Item:        utils.ToString(d["item"]),
			Status:      utils.ToString(d["status"]),
			TimeCreated: utils.ToInt64(d["time_created"]),
			Total:       utils.ToInt(d["total"]),
			Type:        "buy_deals"})
	}

	for _, s := range sales {
		item := utils.ToString(s["items"])
		if len(item) > 100 {
			item = fmt.Sprintf("%s...", item[:100])
		}
		result = append(result, models.Transaction{
			Id:          utils.ToString(s["sales_id"]),
			Item:        item,
			Status:      utils.ToString(s["status"]),
			TimeCreated: utils.ToInt64(s["time_created"]),
			Total:       utils.ToInt(s["grand_total"]),
			Type:        "sales"})
	}

	for _, data := range orders {
		salesJson := utils.ToString(data["items"])
		log.Info(salesJson)
		item := ""
		var transaction models.Sales
		err := json.Unmarshal([]byte(salesJson), &transaction)
		log.IfError(err)
		for _, menu := range transaction.OrderList {
			item += menu.Product.Name + ", "
			if len(item) > 90 {
				break
			}
		}
		result = append(result, models.Transaction{
			Id:          utils.ToString(data["order_sales_id"]),
			Item:        item,
			Status:      utils.ToString(data["status"]),
			TimeCreated: utils.ToInt64(data["time_order"]),
			Total:       transaction.GrandTotal,
			Type:        "order_online"})
	}

	//sort by time created
	sort.Sort(models.ByTimeTransactionDesc(result))
	log.Debug("member %v request page : %d - return %d data | in %v", memberId, page, len(result), time.Since(startRequest))

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func GetHistoryDetailBuyDeals(ctx *fasthttp.RequestCtx) {
	//did'n yet implement in production
	if os.Getenv("server") == "production" {
		emptyData := make([]map[string]interface{}, 0)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: emptyData, Message: "success"})
		return
	}

	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	id := ctx.UserValue("id")

	sql := `
select pb.promotion_buy_id,pb.time_created, pbp.status, pb.price, p.name as item, payment_type, pn.info
from promotion_buy pb
         join promotion_buy_payment pbp on pb.promotion_buy_id = pbp.promotion_buy_fkid
         join promotions p on pb.promotion_fkid = p.promotion_id
left join (select * from payment_notification order by payment_notification_id desc) pn on pn.transaction_id=pbp.transaction_id
where price_type='money' and promotion_buy_id= ? and admin_fkid = ? and pb.member_fkid = ?
order by time_created, pn.payment_notification_id desc limit 1`

	data, err := db.Query(sql, id, adminId, memberId)
	if utils.CheckError(ctx, err) {
		return
	}

	if data["payment_type"] != "gopay" {
		var paymentNotif models.PaymentNotification
		err = json.Unmarshal([]byte(utils.ToString(data["info"])), &paymentNotif)
		if utils.CheckError(ctx, err) {
			return
		}

		data["payment_detail"] = paymentNotif.VaNumbers[0]
	}
	delete(data, "info")

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: data})
}

func GetHistoryDetailSales(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))
	id := ctx.UserValue("id")
	startRequest := time.Now()
	log.Debug("get history sales, sales_id: %s, member_id: %s", id, memberId)

	var wg sync.WaitGroup
	salesChan := make(chan []map[string]interface{}, 1)
	taxesChan := make(chan []map[string]interface{}, 1)
	paymentsChan := make(chan []map[string]interface{}, 1)
	feedbackChan := make(chan map[string]interface{}, 1)
	promotionsChan := make(chan []map[string]interface{}, 1)

	sql := `
select sales_id,
       s.display_nota,
       s.payment,
       s.status,
       s.time_created,
       s.discount,
       s.discount_info,
       s.voucher,
       s.voucher_info,
       s.grand_total,
	   s.customer_name, 
	   e.name as employee_name,
	   coalesce(o.receipt_logo, o.outlet_logo) as outlet_logo,
	   coalesce(o.receipt_phone, o.phone) as receipt_phone,
	   coalesce(o.receipt_address, o.address) as receipt_address,
	   o.receipt_note, o.receipt_socialmedia, o.name as outlet_name,
       sd.product_detail_fkid,
       p.name                                                                                   as product_name,
       COALESCE(CONCAT(p.name, ' (', pdv.variant_name, ')'), p.name)                            AS name,
       (sd.qty - coalesce(sum(sv.qty), 0))                                                      as qty,
       sd.price,
       (sd.sub_total - coalesce(sum(if(sv.sub_total < 0, sv.sub_total * -1, sv.sub_total)), 0)) as sub_total,
       sd.discount                                                                              as discount_item,
       sd.discount_info                                                                         as discount_item_info,
       sd.parent,
       sd.sales_detail_id,
       o.name                                                                                   as outlet_name,
       min(tp.name)                                                                             as promotion_detail_type,
       coalesce(min(sdp.promotion_value - abs(coalesce(sdpv.promotion_value, 0))),
                0)                                                                              as promotion_detail_value
from sales s
         join sales_detail sd on sd.sales_fkid = s.sales_id
         left join sales_detail_promotion sdp on sd.sales_detail_id = sdp.sales_detail_fkid
         left join promotions p1 on p1.promotion_id = sdp.promotion_fkid
         left join promotion_types tp on tp.promotion_type_id = p1.promotion_type_id
         join products p on sd.product_fkid = p.product_id
         JOIN products_detail pd on sd.product_detail_fkid = pd.product_detail_id
         LEFT JOIN products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
         join outlets o on s.outlet_fkid = o.outlet_id
		 LEFT join employee e on e.employee_id=s.employee_fkid
         left join sales_void sv on sd.sales_detail_id = sv.sales_detail_fkid
		 left join sales_detail_promotion sdpv on sdpv.sales_void_fkid = sv.sales_void_id
where s.sales_id = ?
  and (s.member_fkid = ? or s.member_fkid is null)
  and o.admin_fkid = ?
group by sd.sales_detail_id
order by sd.time_created desc `

	wg.Add(1)
	go db.QueryArrayGo(&wg, salesChan, sql, id, memberId, adminId)

	sql = `
select g.name, g.tax_category, st.total
from sales_tax st
         join gratuity g on st.tax_fkid = g.gratuity_id
where sales_fkid = ?
  and g.admin_fkid = ?`

	wg.Add(1)
	go db.QueryArrayGo(&wg, taxesChan, sql, id, adminId)

	sql = `select method, total, pay, pmb.name
from sales_payment sp
         left join sales_payment_bank spb on sp.payment_id = spb.sales_payment_fkid
		left join payment_media_bank pmb on spb.bank_fkid = pmb.bank_id
where sales_fkid = ?`
	wg.Add(1)
	go db.QueryArrayGo(&wg, paymentsChan, sql, id)

	sql = `select * from sales_feedback where sales_fkid = ? LIMIT 1`
	wg.Add(1)
	go db.QueryGo(&wg, feedbackChan, sql, id)

	sql = `select sp.promotion_value, sp.voucher_code, p.name
from sales_promotion sp
         join promotions p on p.promotion_id = sp.promotion_fkid
where sales_fkid = ?`
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionsChan, sql, id)

	wg.Wait()
	sales := <-salesChan
	taxes := <-taxesChan
	payments := <-paymentsChan
	feedback := <-feedbackChan
	promotions := <-promotionsChan

	if len(sales) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "not found"})
		return
	}

	result := make(map[string]interface{})
	salesDetail := make([]map[string]interface{}, 0)
	items := make([]models.Items, 0)
	subItems := make([]models.Items, 0)
	salesFirst := utils.CopyMap(sales[0])
	keys := "sales_id,display_nota,payment,status,time_created,discount,discount_info,voucher,voucher_info,grand_total,outlet_name,promotion_name,promotion_value"
	for index, data := range sales {
		if index == 0 {
			for _, key := range strings.Split(keys, ",") {
				result[key] = data[key]
			}
		}

		if utils.ToInt(data["qty"]) > 0 {
			if utils.ToInt(data["discount_item"]) > 0 {
				subItems = append(subItems, models.Items{
					Product:  " discount ",
					SubTotal: utils.CurrencyFormat(utils.ToInt(data["discount_item"])),
				})
			}

			if utils.ToInt(data["parent"]) > 0 {
				subItems = append(subItems, models.Items{
					Product:  utils.ToString(data["name"]),
					Qty:      utils.ToString(data["qty"]),
					SubTotal: utils.CurrencyFormat(utils.ToInt(data["sub_total"])),
				})
			} else {
				promotions := make([]models.Promotion, 0)
				if utils.ToInt(data["promotion_detail_value"]) > 0 {
					promotions = append(promotions, models.Promotion{
						PromoType:  utils.ToString(data["promotion_detail_type"]),
						PromoValue: utils.CurrencyFormat(utils.ToInt(data["promotion_detail_value"])),
					})
				}
				items = append(items, models.Items{
					Product:    utils.ToString(data["name"]),
					Qty:        utils.ToString(data["qty"]),
					SubTotal:   utils.CurrencyFormat(utils.ToInt(data["sub_total"])),
					SubItems:   subItems,
					Promotions: promotions,
				})
				subItems = make([]models.Items, 0)
			}
		}

		//we only need data from table sales_detail, so delete data got from table sales
		for _, key := range strings.Split(keys, ",") {
			delete(data, key)
		}
		salesDetail = append(salesDetail, data)
	}

	taxAndDisc := make([]models.TaxAndDisc, 0)
	if utils.ToInt(result["voucher"]) > 0 {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  "Voucher",
			Total: utils.CurrencyFormat(utils.ToInt(result["voucher"])),
		})
	}

	if utils.ToInt(result["discount"]) > 0 {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  "Discount",
			Total: utils.CurrencyFormat(utils.ToInt(result["discount"])),
		})
	}

	if utils.ToInt64(result["promotion_value"]) > 0 {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  utils.ToString(result["promotion_name"]),
			Total: utils.CurrencyFormat(utils.ToInt(result["promotion_value"])),
		})
	}

	voucherCodes := make([]string, 0)
	for _, promo := range promotions {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  utils.ToString(promo["name"]),
			Total: utils.CurrencyFormat(utils.ToInt(promo["promotion_value"])),
		})

		if cast.ToString(promo["voucher_code"]) != "" {
			voucherCodes = append(voucherCodes, strings.ToUpper(cast.ToString(promo["voucher_code"])))
		}
	}

	for _, tax := range taxes {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  utils.ToString(tax["name"]),
			Total: utils.CurrencyFormat(utils.ToInt(tax["total"])),
		})
	}

	receiptTemplate := "config/template/receipt_template_v2.html"
	tmpl, err := utils.ReadFile(receiptTemplate)
	log.IfError(err)

	templateReceipt, err := template.New("receipt_note").Parse(tmpl)
	log.IfError(err)

	timeOffset := int64(25200)
	timeCreated := utils.ToInt64(salesFirst["time_created"])

	data := models.Receipt{
		ReceiptNo:     cast.ToString(salesFirst["display_nota"]),
		Date:          time.Unix((timeCreated/1000)+timeOffset, 0).Format("02-01-2006 03:04"),
		Cashier:       cast.ToString(salesFirst["employee_name"]), //employee_name
		Outlet:        cast.ToString(salesFirst["outlet_name"]),
		OutletAddress: cast.ToString(salesFirst["receipt_address"]),
		OutletPhone:   cast.ToString(salesFirst["receipt_phone"]),
		OutletLogo:    cast.ToString(salesFirst["outlet_logo"]), //outlet_logo
		Items:         items,
		TaxAndDisc:    taxAndDisc,
		GrandTotal:    utils.CurrencyFormat(utils.ToInt(result["grand_total"])),
		//Payments:      nil,
		Return:        "",
		ReceiptNote:   cast.ToString(salesFirst["receipt_note"]),
		ReceiptSosMed: cast.ToString(salesFirst["receipt_socialmedia"]), //receipt_socialmedia
		FeedbackUrl:   "",
		VoucherCode:   strings.Join(voucherCodes, ", "),
		Customer:      cast.ToString(salesFirst["customer_name"]),
		Payment:       cast.ToString(salesFirst["payment"]),
	}

	var resultHtml bytes.Buffer
	err = templateReceipt.Execute(&resultHtml, data)
	log.IfError(err)

	result["data_html"] = resultHtml.String()
	result["order_list"] = salesDetail
	result["taxes"] = taxes
	result["feedback"] = feedback
	result["payments"] = payments

	//log.Info("HTML : %s", result["data_html"])
	log.Debug("[REQ DETAIL TRANSACTION] member %s - in %v", memberId, time.Since(startRequest))

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func GiveFeedBack(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := string(ctx.Request.Header.Peek("mid"))

	salesId := string(ctx.PostArgs().Peek("sales_id"))
	stars := utils.ToFloat(ctx.PostArgs().Peek("stars"))
	opinion := ctx.PostArgs().Peek("opinion")
	comment := ctx.PostArgs().Peek("comment")

	log.Info("%s giving feedback for %s. star : %v | comment : %s", memberId, salesId, stars, comment)

	if isValid, msg := utils.IsValidPostInput(ctx, "sales_id", "stars"); !isValid {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	sql := "select sales_id,member_fkid  from sales s join outlets o on s.outlet_fkid = o.outlet_id where (member_fkid = ? or member_fkid is null) and admin_fkid = ? and sales_id = ? LIMIT 1"
	sales, err := db.Query(sql, memberId, adminId, salesId)
	log.IfError(err)

	if len(sales) <= 0 {
		log.Info("no transaction with id '%s'", salesId)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "no transaction with id " + salesId})
		return
	}

	if stars < 0 || stars > 5 {
		log.Info("stars should between 0 until 5")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "stars should between 0 until 5"})
		return
	}

	_, errInsert := db.Insert("sales_feedback", map[string]interface{}{
		"sales_fkid":   salesId,
		"stars":        stars,
		"opinion":      opinion,
		"comment":      comment,
		"date_created": time.Now().Unix() * 1000,
	})

	if errInsert != nil {
		//if error, check if feedback is already given
		data, err2 := db.Query("select sales_fkid from sales_feedback where sales_fkid =? ", salesId)
		if !log.IfError(err2) && len(data) > 0 {
			log.Info("feedback already given before!")
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "feedback already given before!"})
			return
		}
	}

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	fmt.Println(sales["member_fkid"])
	if sales["member_fkid"] == nil {
		go func(salesId string) {
			_, err = db.Update("sales", map[string]interface{}{
				"member_fkid": memberId,
			}, "sales_id=?", salesId)
			log.IfError(err)
		}(salesId)
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "feedback successfully added"})
}

func AddSelfOrder(ctx *fasthttp.RequestCtx) {
	timeOffset := 25200
	adminId := string(ctx.Request.Header.Peek("uid"))
	memberId := ctx.Request.Header.Peek("mid")
	code := utils.RandomNumber(10000, 99999)

	order := models.Order{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&order)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("self order body %s", ctx.PostBody())
	log.Info("self order json : %s", utils.SimplyToJson(order))

	//check if user has authorization to use such a outlet_id
	data, err := db.Query("select outlet_id from outlets where admin_fkid = ? and outlet_id = ?", adminId, order.OutletId)
	log.IfError(err)

	if len(data) == 0 {
		log.Warn("order using unauthorize outlet id. idmin id : %s | outlet id : %d", adminId, order.OutletId)
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "you are unauthenticated to save this order"})
		return
	}

	if len(order.OrderList) == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "please order at least 1 item"})
		return
	}

	//validate the products
	notFound := make([]string, 0)
	productValidationMap := make([]map[string]interface{}, 0)
	var wg sync.WaitGroup
	for i, product := range order.OrderList {
		wg.Add(1)
		go func(productDetailId int) {
			defer wg.Done()
			sql := `
SELECT product_detail_id,
       if(pd.stock = 'unavailable', pd.stock,
          if((p.app_show = 0 or cpac.total_available > 0) and cpaa.hour_start is null,
             'unavailable', 'available')) as status
from products_detail pd
         join products p on pd.product_fkid = p.product_id
         left join (select count(*) total_available, product_fkid
                    from crm_products_available
                    group by product_fkid) cpac
                   on cpac.product_fkid = p.product_id
         left join (select product_fkid, day, hour_start, hour_end
                    from crm_products_available
                    where hour_start <= from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and hour_end > from_unixtime(unix_timestamp() + ?, '%H:%i:%s')
                      and convert(day USING utf8) = lower(dayname(from_unixtime(unix_timestamp() + ?)))) cpaa
                   on cpaa.product_fkid = p.product_id
where product_detail_id = ?
  and outlet_fkid = ? `
			data, err := db.Query(sql, timeOffset, timeOffset, timeOffset, productDetailId, order.OutletId)
			if !log.IfError(err) && len(data) == 0 {
				notFound = append(notFound, utils.ToString(productDetailId))
				productValidationMap = append(productValidationMap, map[string]interface{}{
					"product_detail_id": productDetailId,
					"reason":            "not found",
				})
			} else if utils.ToString(data["status"]) == "unavailable" {
				notFound = append(notFound, utils.ToString(productDetailId))
				productValidationMap = append(productValidationMap, map[string]interface{}{
					"product_detail_id": productDetailId,
					"reason":            "unavailable",
				})
			}
		}(product.ProductDetailID)

		order.OrderList[i].Note = utils.ReplaceEmoji(product.Note)
	}
	wg.Wait()

	if len(notFound) > 0 {
		log.Warn("products not found : %s", utils.SimplyToJson(productValidationMap))
		//ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "product not found or unavailable : " + strings.Join(notFound, ","), Data: map[string]interface{}{
			"errors": productValidationMap,
		}})
		return
	}

	orderJson, err := json.Marshal(order)
	log.IfError(err)

	codes := make([]interface{}, 0)
	for i := 0; i < 5; i++ {
		codes = append(codes, utils.RandomNumber(10000, 99999))
	}

	existCodes, err := db.QueryArray("select order_code from self_order where order_code in ("+strings.Repeat("?,", len(codes)-1)+"?)", codes...)
	log.IfError(err)

	//remove code exists in db
	log.Info("generated codes: %v | from db: %v", codes, existCodes)
	for _, c := range existCodes {
		codes = utils.RemoveArray(codes, utils.ToInt(c["order_code"]))
	}
	log.Info("generated codes after removing: %v", codes)

	//add one random data, just in case all data removed
	for i := len(codes); i <= 2; i++ {
		codes = append(codes, utils.RandomNumber(10000, 99999))
	}
	code = utils.ToInt(codes[0])

	expired := time.Now().Add(time.Hour*time.Duration(2)).Unix() * 1000
	dataSelfOrder := map[string]interface{}{
		"outlet_fkid":   order.OutletId,
		"time_created":  time.Now().Unix() * 1000,
		"time_modified": time.Now().Unix() * 1000,
		"order_code":    code,
		"order_item":    string(orderJson),
		"expired_at":    expired,
		"member_fkid":   memberId,
	}
	if order.CustomerName != "" && len(order.CustomerName) >= 25 {
		dataSelfOrder["customer_name"] = order.CustomerName[:25]
	}
	if order.ReceiptReceiver != "" {
		dataSelfOrder["contact"] = order.ReceiptReceiver
	}

	resp, err := db.Insert("self_order", dataSelfOrder)

	//if code is duplicate somehow... try once more
	if err != nil && utils.GetErrCodeSQL(err.Error()) == utils.SQL_ERR_DUPLICATE {
		dataSelfOrder["order_code"] = codes[1]
		resp, err = db.Insert("self_order", dataSelfOrder)
	}

	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "server encounter some problems"})
	} else {
		id, _ := resp.LastInsertId()
		log.Info("self-order id: %v", id)

		qrCode := generate.QrCode(cast.ToString(code), 250)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
			"order_code": code,
			"qrcode":     qrCode,
			"expired":    expired,
		}})

		//set contact
		//if memberId != nil {
		//	go func(id int64, mId string) {
		//		member, _ := db.Query("select phone from members where member_id = ?", mId)
		//		_, err = db.Update("self_order", utils.MapOf("contact", member["phone"]), "self_order_id = ?", id)
		//		log.IfError(err)
		//	}(id, string(memberId))
		//}
	}
}

func GetSelfOrder(ctx *fasthttp.RequestCtx) {
	//adminId := string(ctx.Request.Header.Peek("uid"))
	idEnc := utils.ToString(ctx.UserValue("id"))
	outletId := ctx.UserValue("outletId")

	id := utils.Decrypt(idEnc, utils.KEY_SELF_ORDER)
	if !utils.IsNumber(id) && utils.IsNumber(idEnc) {
		id = idEnc
	}

	log.Info("get self-order id: %v | at: %v", id, outletId)
	data, err := db.Query("select order_item, expired_at, applied_at, outlet_fkid from self_order where order_code = ? and outlet_fkid = ? LIMIT 1", id, outletId)
	log.IfError(err)

	if len(data) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "not found"})
		return
	}

	if utils.ToInt64(data["expired_at"]) < time.Now().Unix()*1000 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "pesanan ini sudah expired"})
		return
	}

	if utils.ToInt64(data["applied_at"]) > 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "pesanan ini sudah digunakan"})
		return
	}

	var selfOrder map[string]interface{}
	log.IfError(json.Unmarshal([]byte(utils.ToString(data["order_item"])), &selfOrder))
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: selfOrder})
}

func UpdateSelfOrder(ctx *fasthttp.RequestCtx) {
	//adminId := string(ctx.Request.Header.Peek("uid"))
	idEnc := utils.ToString(ctx.UserValue("id"))

	orderCode := utils.Decrypt(idEnc, utils.KEY_SELF_ORDER)
	data, err := db.Query("select self_order_id, expired_at, applied_at, outlet_fkid from self_order where order_code = ? LIMIT 1", orderCode)
	log.IfError(err)

	if len(data) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "not found"})
		return
	}

	if utils.ToInt64(data["expired_at"]) < time.Now().Unix()*1000 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "pesanan ini sudah expired"})
		return
	}

	if utils.ToInt64(data["applied_at"]) > 0 {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "pesanan ini sudah digunakan"})
		return
	}

	order := models.Order{}
	err = json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&order)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("new self order : %s", utils.SimplyToJson(order))

	if len(order.OrderList) == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "please order at least 1 item"})
		return
	}

	//validate the products
	notFound := ""
	var wg sync.WaitGroup
	for _, product := range order.OrderList {
		wg.Add(1)
		go func(productDetailId, outletId int) {
			defer wg.Done()
			data, err := db.Query("SELECT product_detail_id from products_detail where product_detail_id = ? and outlet_fkid = ?", productDetailId, outletId)
			if !log.IfError(err) && len(data) == 0 {
				notFound += fmt.Sprintf("%d, ", productDetailId)
			}
		}(product.ProductDetailID, utils.ToInt(data["outlet_fkid"]))
	}
	wg.Wait()

	if notFound != "" {
		log.Warn("products not found : %s", notFound)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "these products ids are not found : " + notFound})
		return
	}

	orderJson, err := json.Marshal(order)
	log.IfError(err)

	_, err = db.Update("self_order", map[string]interface{}{"order_item": string(orderJson)}, "self_order_id = ?", data["self_order_id"])
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	params := url.Values{}
	params.Add("cht", "qr")
	params.Add("chs", "250x250")
	params.Add("chl", utils.ToString(orderCode))

	baseUrl, err := url.Parse("https://chart.googleapis.com")
	log.IfError(err)
	baseUrl.Path = "chart"
	baseUrl.RawQuery = params.Encode()
	log.Info(baseUrl.String())

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: map[string]interface{}{
		"order_code": orderCode,
		"qrcode":     baseUrl.String(),
		"expired":    data["expired_at"],
	}})
}

func SendSelfOrderCode(ctx *fasthttp.RequestCtx) {
	adminId := utils.ToInt(ctx.Request.Header.Peek("uid"))
	orderCode := ctx.PostArgs().Peek("order_code")
	contact := string(ctx.PostArgs().Peek("contact"))
	log.Info("code : %s | contact : %s", orderCode, contact)

	if valid, msg := utils.IsValidPostInput(ctx, "order_code", "contact"); !valid {
		log.Warn(msg)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: msg})
		return
	}

	//validate contact
	if len(contact) < 9 || len(contact) > 15 {
		log.Warn("contact is invalid length : %d ", len(contact))
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid contact format"})
		return
	}

	if !utils.IsNumber(contact) {
		log.Warn("contact is not number ")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid contact"})
		return
	}

	selfOrder, err := db.Query("select self_order_id, contact, expired_at, o.outlet_id, o.name from self_order so join outlets o on so.outlet_fkid = o.outlet_id where order_code = ? ", orderCode)
	log.IfError(err)

	if len(selfOrder) == 0 {
		log.Warn("order code not found!")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "invalid order code"})
		return
	}

	if selfOrder["contact"] != nil {
		log.Warn("order code already sent")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "order code already sent"})
		return
	}

	if utils.ToInt64(selfOrder["expired_at"]) < (time.Now().Unix() * 1000) {
		log.Warn("order is expired")
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "order already expired"})
		return
	}

	// params := url.Values{}
	// params.Add("cht", "qr")
	// params.Add("chs", "250x250")
	// params.Add("chl", utils.ToString(orderCode))

	// baseUrl, err := url.Parse("https://chart.googleapis.com")
	// log.IfError(err)
	// baseUrl.Path = "chart"
	// baseUrl.RawQuery = params.Encode()
	// log.Info(baseUrl.String())

	qrCode := generate.QrCode(cast.ToString(orderCode), 250)

	savePath := fmt.Sprintf("temp/%s.jpg", orderCode)
	err = utils.HttpRequest{Url: qrCode}.DownloadFile(savePath)
	log.IfError(err)

	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "create qrcode error"})
		return
	}

	//convert qrcode image to base 64
	qrCodeBase64, err := utils.ToBase64(savePath)
	log.IfError(err)
	utils.DeleteFile(savePath)

	expiredHour := time.Unix(utils.ToInt64(selfOrder["expired_at"])/1000+25200, 0).Format("15:04")

	selfOrderUrl := getSelfOrderUrl(adminId, utils.ToInt(selfOrder["outlet_id"]))
	editUrl := fmt.Sprintf("%s?edit=%s", selfOrderUrl, utils.Encrypt(string(orderCode), utils.KEY_SELF_ORDER))
	fmt.Println("edit url : ", editUrl)

	caption := fmt.Sprintf("KODE PESANAN : *%s*  \nTunjukan QRCode ini di kasir *%s* agar pesanan kamu mulai diproses \nBatas Maksimal jam *%s* \n\n",
		string(orderCode), selfOrder["name"], expiredHour)

	if selfOrderUrl != "" {
		caption += fmt.Sprintf("untuk mengubah pesanan klik : %s\n \n", editUrl)
	}

	caption += "_powered by www.uniq.id_"

	//send it to whatsapp
	request := utils.HttpRequest{
		Method: "POST",
		Url:    "https://bot.uniq.id/send/wa/media",
		Header: map[string]interface{}{
			"Authorization": os.Getenv("wa_auth"),
		},
		PostRequest: utils.PostRequest{
			Form: map[string]string{
				"phone":     contact,
				"caption":   caption,
				"file":      qrCodeBase64,
				"file_type": "base64",
			},
		},
	}

	resp, err := request.Execute()
	log.Info("send wa media response code : %d | body : %s", resp.StatusCode, resp.Body)

	if log.IfError(err) {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "failed sending to whatsapp"})
		return
	}

	//update self order data
	_, err = db.Update("self_order", map[string]interface{}{"contact": contact}, "self_order_id = ?", selfOrder["self_order_id"])
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func getSelfOrderUrl(adminId, outletId int) string {
	if os.Getenv("server") == "staging" {
		if adminId == 10 {
			baseUrl := "https://yamie-order.uniq.id/"
			if outletId == 52 {
				return "https://y.uniq.id/tamsis"
			} else {
				return baseUrl + utils.ToString(outletId)
			}
		}
	}

	fmt.Printf("can not get url for env %v, adminId : %d, outletId : %d\n", os.Getenv("server"), adminId, outletId)
	return ""
}

func GetNoteSuggestion(ctx *fasthttp.RequestCtx) {
	adminId := utils.ToInt(ctx.Request.Header.Peek("uid"))
	ids := string(ctx.QueryArgs().Peek("product_detail_ids"))
	prodIds := strings.Split(ids, ",")
	for i, id := range prodIds {
		prodIds[i] = strings.TrimSpace(id)
	}

	sql := `
select product_fkid, note, count(*) as total, min(product_detail_fkid) as product_detail_fkid
from sales_detail
         join products p on p.product_id = sales_detail.product_fkid
where note is not null
  and trim(note) != ''
  and time_created > unix_timestamp(date_add(now(), interval -3 month)) * 1000
   and product_detail_fkid in [IN]
   and p.admin_fkid = ?
group by product_fkid, note
order by product_fkid, total desc `

	sql = strings.Replace(sql, "[IN]", "("+strings.Repeat("?,", len(prodIds)-1)+"?)", 1)
	params := utils.ArrayOf(prodIds, adminId)
	data, err := db.QueryArray(sql, params...)
	log.IfError(err)

	result := make([]map[string]interface{}, 0)
	suggestion := make([]string, 0)
	for i, sales := range data {
		if len(suggestion) <= 5 {
			suggestion = append(suggestion, utils.ToString(sales["note"]))
		}
		if i == len(data)-1 || sales["product_fkid"] != data[i+1]["product_fkid"] {
			result = append(result, map[string]interface{}{
				"product_detail_id": sales["product_detail_fkid"],
				"suggestions":       suggestion,
			})
			suggestion = make([]string, 0)
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}

func SendNotification(notification models.UserNotification) {
	if notification.Title == "" && notification.Message == "" {
		return
	}

	_, err := db.Insert("scheduled_message", map[string]interface{}{
		"title":        notification.Title,
		"message":      notification.Message,
		"time_deliver": time.Now().Unix() * 1000,
		"data_created": time.Now().Unix() * 1000,
		"media":        "push_notif",
		"receiver":     notification.NotificationToken,
	})
	log.IfError(err)

	_, err = db.Insert("system_notification", map[string]interface{}{
		"title":             notification.Title,
		"message":           notification.Message,
		"data_created":      time.Now().Unix() * 1000,
		"type":              "member",
		"receiver_id":       notification.UserId,
		"admin_fkid":        notification.AdminId,
		"notification_type": notification.NotificationType,
		"notification_data": utils.SimplyToJson(notification.NotificationData),
	})
	log.IfError(err)
}
