package models

type ProductAvailability struct {
	ProductID            int               `json:"product_id,omitempty"`
	Name                 string            `json:"name,omitempty"`
	Photo                string            `json:"photo,omitempty"`
	Barcode              string            `json:"barcode,omitempty"`
	MinQtyOrder          int               `json:"min_qty_order,omitempty"`
	PriceSell            int               `json:"price_sell,omitempty"`
	Price                int               `json:"price,omitempty"`
	OutletFkid           int               `json:"outlet_fkid,omitempty"`
	OutletID             int               `json:"outlet_id,omitempty"`
	ProductDetailID      int               `json:"product_detail_id,omitempty"`
	ProductSubcategoryID int               `json:"product_subcategory_id,omitempty"`
	Subcategory          string            `json:"subcategory,omitempty"`
	Unit                 string            `json:"unit,omitempty"`
	UnitDescription      string            `json:"unit_description,omitempty"`
	Description          string            `json:"description,omitempty"`
	Availability         string            `json:"availability,omitempty"`
	StockStatus          string            `json:"stock_status,omitempty"`
	StockStatusV1        string            `json:"stock_status_v1,omitempty"`
	StockStatusOld       string            `json:"stock_status_old,omitempty"`
	VariantID            int               `json:"variant_id,omitempty"`
	VariantName          string            `json:"variant_name,omitempty"`
	VariantSku           string            `json:"variant_sku,omitempty"`
	VariantBarcode       string            `json:"variant_barcode,omitempty"`
	ProductFkid          int               `json:"product_fkid,omitempty"`
	DataCreated          int64             `json:"data_created,omitempty"`
	DataModified         int64             `json:"data_modified,omitempty"`
	DataStatus           int               `json:"data_status,omitempty"`
	TotalAvailable       int               `json:"total_available,omitempty"`
	HourStart            string            `json:"hour_start,omitempty"`
	HourEnd              string            `json:"hour_end,omitempty"`
	SubcategoryPosition  int               `json:"subcategory_position,omitempty"`
	IsInWishlist         bool              `json:"is_in_wishlist,omitempty"`
	Variant              []ProductVariant  `json:"variant,omitempty"`
	Available            []Available       `json:"available,omitempty"`
	Tax                  []GratuityEntity  `json:"tax,omitempty"`
	SubcategoryConfig    SubcategoryConfig `json:"subcategory_config,omitempty"`
	StockQty             int               `json:"stock_qty,omitempty"`
}

type SubcategoryConfig struct {
	ViewType string `json:"view_type,omitempty"`
}

type ProductVariant struct {
	Available []Available `json:"available,omitempty"`
	Name      string      `json:"name,omitempty"`
	VariantID int         `json:"variant_id,omitempty"`
}

type Available struct {
	EnableOrder     EnableOrder      `json:"enable_order,omitempty"`
	Name            string           `json:"name,omitempty"`
	OutletID        int              `json:"outlet_id,omitempty"`
	PriceSell       int              `json:"price_sell,omitempty"`
	ProductDetailID int              `json:"product_detail_id,omitempty"`
	StockStatus     string           `json:"stock_status,omitempty"`
	VariantID       int              `json:"variant_id,omitempty"`
	Tax             []GratuityEntity `json:"tax,omitempty"`
	StockQty        int              `json:"stock_qty,omitempty"`
}

type EnableOrder struct {
	Reason string `json:"reason,omitempty"`
	Status string `json:"status,omitempty"`
}

func (p *ProductAvailability) ToMap() map[string]interface{} {
	data := make(map[string]interface{})
	data["product_id"] = p.ProductID
	data["name"] = p.Name
	data["photo"] = p.Photo
	data["barcode"] = p.Barcode
	data["min_qty_order"] = p.MinQtyOrder
	data["price_sell"] = p.PriceSell
	data["outlet_fkid"] = p.OutletFkid
	data["outlet_id"] = p.OutletID
	data["product_detail_id"] = p.ProductDetailID
	data["product_subcategory_id"] = p.ProductSubcategoryID
	data["subcategory"] = p.Subcategory
	data["unit"] = p.Unit
	data["unit_description"] = p.UnitDescription
	data["description"] = p.Description
	data["availability"] = p.Availability
	data["stock_status"] = p.StockStatus
	data["stock_status_v1"] = p.StockStatusV1
	data["stock_status_old"] = p.StockStatusOld
	data["variant_id"] = p.VariantID
	data["variant_name"] = p.VariantName
	data["variant_sku"] = p.VariantSku
	data["variant_barcode"] = p.VariantBarcode
	data["product_fkid"] = p.ProductFkid
	data["data_created"] = p.DataCreated
	data["data_modified"] = p.DataModified
	data["data_status"] = p.DataStatus
	data["total_available"] = p.TotalAvailable
	data["hour_start"] = p.HourStart
	data["hour_end"] = p.HourEnd
	data["subcategory_position"] = p.SubcategoryPosition
	data["is_in_wishlist"] = p.IsInWishlist
	// var variants []interface{}
	// for _, v := range p.Variant {
	// 	variants = append(variants, v.ToMap())
	// }
	// data["variant"] = variants
	return data
}
