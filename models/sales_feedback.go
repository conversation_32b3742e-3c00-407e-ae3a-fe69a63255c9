package models

type SalesFeedbackEntity struct {
	SalesFeedbackID       int     `json:"sales_feedback_id,omitempty"`
	SalesFkid             string  `json:"sales_fkid,omitempty"`
	Stars                 float32 `json:"stars,omitempty"`
	Opinion               string  `json:"opinion,omitempty"`
	OpinionCode           string  `json:"opinion_code,omitempty"`
	Comment               string  `json:"comment,omitempty"`
	DateCreated           int64   `json:"date_created,omitempty"`
	CrmFeedbackConfigFkid int64   `json:"crm_feedback_config_fkid,omitempty"`
	Attachments           string  `json:"attachments,omitempty"`
	Sentiment             string  `json:"sentiment,omitempty"`
	PointEarned           int     `json:"point_earned,omitempty"`
}

type SalesFeedbackWithUser struct {
	SalesFeedbackEntity
	AdminId int `json:"admin_id,omitempty"`
}

func (s SalesFeedbackEntity) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"sales_fkid":   s.<PERSON>Fkid,
		"stars":        s.<PERSON>,
		"opinion":      s.Opinion,
		"comment":      s.Comment,
		"date_created": s.DateCreated,
	}
}

type SalesFeedbackUpdate struct {
	SalesFeedbackEntity
	MemberId int64
}

type SentimentResponse struct {
	Label          string  `json:"label,omitempty"`
	Score          float64 `json:"score,omitempty"`
	Classification string  `json:"classification,omitempty"`
}

type SalesFeedbackFilter struct {
	DayInterval    int
	HasComment     bool
	HasSentiment   bool
	UnsetSentiment bool
	FeedbackIds    []int
	StartDate      int64
	EndDate        int64
	AdminId        int64
}

type SentimentReportRequest struct {
	Sentiment string   `json:"sentiment"`
	Data      []string `json:"data"`
}

type SentimentReportResponse struct {
	TrendsInsights        []string `json:"trends_insights"`
	ActionableSuggestions []string `json:"actionable_suggestions"`
}

type FeedbackSummaryRequest struct {
	Id   interface{} `json:"id,omitempty"`
	Text string      `json:"text,omitempty"`
}

type FeedbackSummaryResponse struct {
	TrendsInsights []TrendsInsights `json:"trends_insights,omitempty"`
}

type FeedbackSummaryHistoryResponse struct {
	StartDate int64                   `json:"start_date,omitempty"`
	EndDate   int64                   `json:"end_date,omitempty"`
	Summary   FeedbackSummaryResponse `json:"summary,omitempty"`
}

type Insights struct {
	Text      string   `json:"text,omitempty"`
	Sentiment string   `json:"sentiment,omitempty"`
	Ids       []int    `json:"ids,omitempty"`
	Feedback  []string `json:"feedback,omitempty"`
}
type TrendsInsights struct {
	Topic    string     `json:"topic,omitempty"`
	Insights []Insights `json:"insights,omitempty"`
}
