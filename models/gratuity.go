package models

import (
	"reflect"
	"strings"
)

type GratuityEntity struct {
	GratuityID   int    `json:"gratuity_id" db:"gratuity_id"`
	Name         string `json:"name,omitempty" db:"name"`
	TaxCategory  string `json:"tax_category,omitempty" db:"tax_category"`
	TaxStatus    string `json:"tax_status,omitempty" db:"tax_status"`
	TaxType      string `json:"tax_type,omitempty" db:"tax_type"`
	Jumlah       int    `json:"jumlah,omitempty" db:"jumlah"`
	AdminFkid    int    `json:"admin_fkid,omitempty" db:"admin_fkid"`
	DataCreated  int64  `json:"data_created,omitempty" db:"data_created"`
	DataModified int64  `json:"data_modified,omitempty" db:"data_modified"`
}

func (g GratuityEntity) ToMap() map[string]interface{} {
	val := reflect.ValueOf(g)
	var data = make(map[string]interface{})

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		// key := field.Tag.Get("json")
		key := strings.Split(field.Tag.Get("json"), ",")[0]
		value := val.Field(i).Interface()

		data[key] = value
	}

	return data
}

type ProductGratuity struct {
	GratuityEntity
	ProductDetailFkid int `json:"product_detail_fkid,omitempty"`
}

type ProductDetailTaxEntity struct {
	TaxdetailID       int `json:"taxdetail_id"`
	TaxFkid           int `json:"tax_fkid"`
	ProductDetailFkid int `json:"product_detail_fkid"`
}

func (p ProductGratuity) ToGratuity() GratuityEntity {
	return p.GratuityEntity
}
