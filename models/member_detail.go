package models

type MemberDetailEntity struct {
	MembersDetailID int    `db:"members_detail_id" json:"members_detail_id"`
	MemberFkID      int64  `db:"member_fkid" json:"member_fkid"`
	AdminFkID       int    `db:"admin_fkid" json:"admin_fkid"`
	TypeFkID        int    `db:"type_fkid" json:"type_fkid"`
	TotalPoint      int    `db:"total_point" json:"total_point"`
	TotalSpend      int64  `db:"total_spend" json:"total_spend"`
	Status          int    `db:"status" json:"status"`
	Reason          string `db:"reason" json:"reason"`
	ExpiredDate     int64  `db:"expired_date" json:"expired_date"`
	FirebaseToken   string `db:"firebase_token" json:"firebase_token"`
	RegisterDate    int64  `db:"register_date" json:"register_date"`
	SecretID        string `db:"secret_id" json:"secret_id"`
	SecretIDExpired int64  `db:"secret_id_expired" json:"secret_id_expired"`
}

type MemberDetail struct {
	MemberId int
	AdminId  int
}
