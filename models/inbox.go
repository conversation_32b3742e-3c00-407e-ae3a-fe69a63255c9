package models

type InboxEntity struct {
	NotificationID   int    `json:"notification_id"`
	Title            string `json:"title"`
	Message          string `json:"message"`
	IsRead           int    `json:"is_read"`
	IsViewed         int    `json:"is_viewed"`
	DataCreated      int64  `json:"data_created"`
	Type             string `json:"type"`
	ReceiverID       string `json:"receiver_id"`
	ReceiverType     string `json:"receiver_type"`
	AdminFKID        int    `json:"admin_fkid"`
	NotificationType string `json:"notification_type"`
	NotificationData string `json:"notification_data"`
	CampaignFKID     int    `json:"campaign_fkid"`
}
