package models

type Transaction struct {
	Id          string `json:"id"`
	Item        string `json:"item"`
	Status      string `json:"status"`
	TimeCreated int64  `json:"time_created"`
	Total       int    `json:"total"`
	Type        string `json:"type"`
}

type ByTimeTransactionDesc []Transaction

func (t ByTimeTransactionDesc) Len() int           { return len(t) }
func (t ByTimeTransactionDesc) Less(i, j int) bool { return t[j].TimeCreated < t[i].TimeCreated }
func (t ByTimeTransactionDesc) Swap(i, j int)      { t[i], t[j] = t[j], t[i] }
