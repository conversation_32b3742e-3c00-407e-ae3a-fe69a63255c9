package models

type SubcategoryConfigEntity struct {
	ID              int   `json:"id,omitempty"`
	SubcategoryFkid int   `json:"subcategory_fkid,omitempty"`
	Position        int   `json:"position,omitempty"`
	DataCreated     int64 `json:"data_created,omitempty"`
	DataModified    int64 `json:"data_modified,omitempty"`
}

func (s SubcategoryConfigEntity) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":             s.ID,
		"subcategory_fkid": s.SubcategoryFkid,
		"position":       s.Position,
		"data_created":   s.DataCreated,
		"data_modified":  s.DataModified,
	}
}