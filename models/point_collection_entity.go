package models

type PointCollectionEntity struct {
	PointCollectionID int    `json:"point_collection_id"`
	Name              string `json:"name"`
	PointType         string `json:"point_type"`
	Point             int    `json:"point"`
	MemberTypeFkid    int    `json:"member_type_fkid"`
	MinTransaction    int    `json:"min_transaction"`
	DateStart         int64  `json:"date_start"`
	DateEnd           int64  `json:"date_end"`
	TimeStart         string `json:"time_start"`
	TimeEnd           string `json:"time_end"`
	DaySunday         int    `json:"day_sunday"`
	DayMonday         int    `json:"day_monday"`
	DayTuesday        int    `json:"day_tuesday"`
	DayWednesday      int    `json:"day_wednesday"`
	DayThursday       int    `json:"day_thursday"`
	DayFriday         int    `json:"day_friday"`
	DaySaturday       int    `json:"day_saturday"`
	AdminFkid         int    `json:"admin_fkid"`
	DataCreated       int64  `json:"data_created"`
	DataModified      int64  `json:"data_modified"`
	DataStatus        int    `json:"data_status"`
}
