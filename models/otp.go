package models

const (
	KEY_OTP = "CdNyokfHYvulEaerBTmZDNoodSy5XKKS"
)

type AuthOTPEntity struct {
	AuthOTPID       int64  `json:"auth_otp_id"`
	Contact         string `json:"contact"`
	ContactType     string `json:"contact_type"`
	Token           string `json:"token"`
	DateCreated     int64  `json:"date_created"`
	DateExpired     int64  `json:"date_expired"`
	AuthenticatedAt int64  `json:"authenticated_at"`
}

type OtpRequestInput struct {
	Contact     string `json:"contact,omitempty"`      //can be phone, email, wa number
	ContactType string `json:"contact_type,omitempty"` //whatsapp, sms, email
}

type OtpRequestResponse struct {
	ExpiredAt int64  `json:"expired_at,omitempty"`
	Token     string `json:"token,omitempty"`
}
