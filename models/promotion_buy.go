package models

type PromotionBuyEntity struct {
	PromotionBuyID    int    `json:"promotion_buy_id,omitempty"`
	MemberFkid        int    `json:"member_fkid,omitempty"`
	PromotionFkid     int    `json:"promotion_fkid,omitempty"`
	PromotionTypeFkid int    `json:"promotion_type_fkid,omitempty"`
	PromoNominal      int    `json:"promo_nominal,omitempty"`
	Price             int    `json:"price,omitempty"`
	PriceType         string `json:"price_type,omitempty"`
	Status            string `json:"status,omitempty"`
	Source            string `json:"source,omitempty"`
	TimeCreated       int64  `json:"time_created,omitempty"`
	TimeModified      int64  `json:"time_modified,omitempty"`
	SecretCode        string `json:"secret_code,omitempty"`
	SecretCodeExpired int64  `json:"secret_code_expired,omitempty"`
}
