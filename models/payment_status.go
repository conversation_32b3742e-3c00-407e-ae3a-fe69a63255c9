package models

type PaymentStatus struct {
	MaskedCard             string `json:"masked_card"`
	ApprovalCode           string `json:"approval_code"`
	Bank                   string `json:"bank"`
	ChannelResponseCode    string `json:"channel_response_code"`
	ChannelResponseMessage string `json:"channel_response_message"`
	TransactionTime        string `json:"transaction_time"`
	GrossAmount            string `json:"gross_amount"`
	Currency               string `json:"currency"`
	OrderID                string `json:"order_id"`
	PaymentType            string `json:"payment_type"`
	SignatureKey           string `json:"signature_key"`
	StatusCode             string `json:"status_code"`
	TransactionID          string `json:"transaction_id"`
	TransactionStatus      string `json:"transaction_status"`
	FraudStatus            string `json:"fraud_status"`
	CardType               string `json:"card_type"`
	StatusMessage          string `json:"status_message"`
}
