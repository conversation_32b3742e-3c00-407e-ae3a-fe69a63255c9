package models

type OrderStatusEntity struct {
	ID             int    `json:"id,omitempty"`
	OrderSalesFkid int    `json:"order_sales_fkid,omitempty"`
	OrderSalesID   string `json:"order_sales_id,omitempty"`
	Status         string `json:"status,omitempty"`
	AdminID        int    `json:"admin_id,omitempty"`
	EmployeeID     int    `json:"employee_id,omitempty"`
	Message        string `json:"message,omitempty"`
	TimeCreated    int64  `json:"time_created,omitempty"`
}
