package models

type SalesEntity struct {
	SalesID         string `json:"sales_id"`
	DisplayNota     string `json:"display_nota"`
	Payment         string `json:"payment"`
	DiningTable     string `json:"dining_table"`
	CustomerName    string `json:"customer_name"`
	MemberFkid      int    `json:"member_fkid"`
	QtyCustomers    int    `json:"qty_customers"`
	DataStatus      string `json:"data_status"`
	EmployeeFkid    int    `json:"employee_fkid"`
	OutletFkid      int    `json:"outlet_fkid"`
	Status          string `json:"status"`
	TimePrediction  int    `json:"time_prediction"`
	OpenShiftFkid   int    `json:"open_shift_fkid"`
	DateCreated     string `json:"date_created"`
	DateModified    string `json:"date_modified"`
	TimeCreated     int64  `json:"time_created"`
	TimeModified    int64  `json:"time_modified"`
	Discount        int    `json:"discount"`
	DiscountInfo    string `json:"discount_info"`
	Voucher         int    `json:"voucher"`
	VoucherInfo     string `json:"voucher_info"`
	GrandTotal      int    `json:"grand_total"`
	ReceiptReceiver string `json:"receipt_receiver"`
	PointEarned     int    `json:"point_earned"`
}
