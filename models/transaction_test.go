package models

import "testing"

func TestTransactionCreate_CalculateGrandTotal(t *testing.T) {
	type fields struct {
		OrderSalesId string
		OutletID     int
		CustomerName string
		OrderList    []OrderList
		OrderType    string
		Shipment     TransactionShipment
		Pickup       Pickup
		Status       string
		Note         string
		Deal         TransactionPromo
		Promotion    []TransactionPromo
		TimeOrder    int64
	}
	tests := []struct {
		name   string
		fields fields
		want   int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := TransactionCreate{
				OrderSalesId: tt.fields.OrderSalesId,
				OutletID:     tt.fields.OutletID,
				CustomerName: tt.fields.CustomerName,
				OrderList:    tt.fields.OrderList,
				OrderType:    tt.fields.OrderType,
				Shipment:     tt.fields.Shipment,
				Pickup:       tt.fields.Pickup,
				Status:       tt.fields.Status,
				Note:         tt.fields.Note,
				Deal:         tt.fields.Deal,
				Promotion:    tt.fields.Promotion,
				TimeOrder:    tt.fields.TimeOrder,
			}
			if got := tr.CalculateGrandTotal(); got != tt.want {
				t.Errorf("TransactionCreate.CalculateGrandTotal() = %v, want %v", got, tt.want)
			}
		})
	}
}
