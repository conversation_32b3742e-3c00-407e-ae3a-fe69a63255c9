package models

import (
	"gitlab.com/uniqdev/backend/api-membership/core/utils/istime"
)

const (
	ProductOrderEnable  = "enable"
	ProductOrderDisable = "disable"
)

type Product struct {
	ProductID              int    `json:"product_id,omitempty"`
	Name                   string `json:"name,omitempty"`
	VariantName            string `json:"variant_name,omitempty"`
	VariantId              int    `json:"variant_id,omitempty"`
	Photo                  string `json:"photo,omitempty"`
	MinQtyOrder            int    `json:"min_qty_order,omitempty"`
	Description            string `json:"description,omitempty"`
	StockManagement        int    `json:"stock_management,omitempty"`
	StockQty               int    `json:"stock_qty,omitempty"`
	Stock                  string `json:"stock,omitempty"`
	ProductDetailID        int    `json:"product_detail_id,omitempty"`
	OutletFkid             int    `json:"outlet_fkid,omitempty"`
	PriceSell              int    `json:"price_sell,omitempty"`
	UnitFkid               int    `json:"unit_fkid,omitempty"`
	ProductSubcategoryFkid int    `json:"product_subcategory_fkid,omitempty"`
	OutletName             string `json:"outlet_name,omitempty"`
}

func (p *Product) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"product_id":               p.ProductID,
		"name":                     p.Name,
		"variant_name":             p.VariantName,
		"variant_id":               p.VariantId,
		"photo":                    p.Photo,
		"min_qty_order":            p.MinQtyOrder,
		"description":              p.Description,
		"stock_management":         p.StockManagement,
		"stock_qty":                p.StockQty,
		"stock":                    p.Stock,
		"product_detail_id":        p.ProductDetailID,
		"outlet_fkid":              p.OutletFkid,
		"price_sell":               p.PriceSell,
		"unit_fkid":                p.UnitFkid,
		"product_subcategory_fkid": p.ProductSubcategoryFkid,
		"outlet_name":              p.OutletName,
	}
}

func (p *Product) ToAvailale(availableTime ProductAvailableEntity) Available {
	return Available{
		ProductDetailID: p.ProductDetailID,
		PriceSell:       p.PriceSell,
		OutletID:        p.OutletFkid,
		Name:            p.OutletName,
		EnableOrder:     p.GetEnableOrder(availableTime),
	}
}

func (p *Product) GetEnableOrder(availableTime ProductAvailableEntity) EnableOrder {
	enableOrder := EnableOrder{Status: ProductOrderEnable}
	if !istime.Between(availableTime.HourStart, availableTime.HourEnd, 7) {
		enableOrder.Status = "disable"
		enableOrder.Reason = "time"
	} else if p.Stock == "unavailable" {
		enableOrder.Status = ProductOrderDisable
		enableOrder.Reason = "stock"
	} else {
		if p.StockManagement == 1 && p.StockQty <= p.MinQtyOrder {
			enableOrder.Status = ProductOrderDisable
			enableOrder.Reason = "stock"
		}
	}
	return enableOrder
}

func (p *Product) ToProductAvailability(available *[]Available, variant *[]ProductVariant, unit *UnitEntity, subCat *SubcategoryWithPosition) ProductAvailability {
	return ProductAvailability{
		Name:                 p.Name,
		ProductID:            p.ProductID,
		Photo:                p.Photo,
		MinQtyOrder:          p.MinQtyOrder,
		PriceSell:            p.PriceSell,
		ProductSubcategoryID: subCat.ProductSubcategoryID,
		Unit:                 unit.Name,
		UnitDescription:      unit.Description,
		Subcategory:          subCat.Name,
		// SubcategoryConfig:    SubcategoryConfig{ViewType: "grid"},
		SubcategoryPosition: subCat.Position,
		Available:           *available,
		Variant:             *variant,
	}
}
