package models

type ProductUnitConversionEntity struct {
	ProductsUnitConvertionID int64   `json:"products_unit_convertion_id"`
	ProductFKID              int64   `json:"product_fkid"`
	UnitFKID                 int64   `json:"unit_fkid"`
	Qty                      float32 `json:"qty"`
	DataCreated              int64   `json:"data_created"`
	DataModified             int64   `json:"data_modified"`
}

func (p *ProductUnitConversionEntity) ToModel() map[string]interface{} {
	model := map[string]interface{}{
		"products_unit_convertion_id": p.ProductsUnitConvertionID,
		"product_fkid":                p.ProductFKID,
		"unit_fkid":                   p.UnitFKID,
		"qty":                         p.Qty,
		"data_created":                p.DataCreated,
		"data_modified":               p.DataModified,
	}
	return model
}
