package models

const (
	PromotionBuyStatusPending   = "pending"
	PromotionBuyStatusAvailable = "available"
	PromotionBuyStatusRedeem    = "redeem"

	PromotionPriceTypeMoney = "money"
	PromotionPriceTypePoint = "point"
)

type PromotionPaymentCreate struct {
	AdminId     int                    `json:"admin_id,omitempty"`
	Id          string                 `json:"promotion_buy_id,omitempty"`
	Price       int                    `json:"price,omitempty"`
	Customer    Customer               `json:"customer,omitempty"`
	PaymentType string                 `json:"payment_type,omitempty"`
	CallbackUrl string                 `json:"callback_url,omitempty"`
	MetaData    map[string]interface{} `json:"meta_data,omitempty"`
}

type PromotionPaymentResponse struct {
	Qris          Qris   `json:"qris,omitempty"`
	Ewallet       Qris   `json:"ewallet,omitempty"`
	TransactionID string `json:"transaction_id,omitempty"`
}

type Qris struct {
	URL string `json:"url"`
}

type PromotionFilter struct {
	AdminId          int      `json:"admin_id,omitempty"`
	MemberId         int      `json:"member_id,omitempty"`
	MemberTypeId     int      `json:"member_type_id,omitempty"`
	TimeOffset       int      `json:"time_offset,omitempty"`
	PromotionId      int      `json:"promotion_id,omitempty"`
	PromotionTypeId  int      `json:"promotion_type_id,omitempty"`
	PromotionTypeIds []int    `json:"promotion_type_ids,omitempty"`
	Types            []string `json:"types,omitempty"`
	OutetIds         []int    `json:"outet_ids,omitempty"`
}

type PromotionBuy struct {
	MemberFkid        int
	PromotionFkid     int
	PromotionTypeFkid int
	Price             int
	PriceType         string
	PromotionNominal  int
	Status            string
}

type PromotionBuyPayment struct {
	PromotionBuyId int64
	TransactionId  string
	Status         string
	PaymentInfo    string
	PaymentType    string
	ExpiredAt      int64
}

type PromotionProduct struct {
	PricePromo      int    `json:"price_promo"`
	PriceSell       int    `json:"price_sell"`
	Name            string `json:"name"`
	ProductDetailID int    `json:"product_detail_id"`
	ProductID       int    `json:"product_id"`
	PromotionID     int    `json:"promotion_id"`
	Type            string `json:"type"`
	Qty             int    `json:"qty"`
}

type PromotionOutlet struct {
	Name        string `json:"name"`
	OutletID    int    `json:"outlet_id"`
	PromotionID int    `json:"promotion_id"`
}

type PromotionBuyFilter struct {
	PromotionBuyId int
	MemberId       int64
	AdminId        int64
	ExactExpireDay int    // Optional: Fetch promotions that will expire in exactly X days
	Status         string // Optional: Filter by status (available/redeem/unavailable)
}

type PromotionDetailFilter struct {
	PromotionIds []int `json:"promotion_ids,omitempty"`
}

func (p PromotionPaymentResponse) PaymentDetail() map[string]interface{} {
	url := p.Qris.URL
	if url == "" {
		url = p.Ewallet.URL
	}
	return map[string]interface{}{
		"url": url,
	}
}
