package models

type Payment struct {
	OrderId   string
	GranTotal int
	Customer  Customer
}

type Customer struct {
	Name  string `json:"name,omitempty"`
	Email string `json:"email,omitempty"`
	Phone string `json:"phone,omitempty"`
}

type PaymentResult struct {
	QrCode   string
	Response string
}

type PaymentMethod struct {
	Type   string                `json:"type,omitempty"`
	Name   string                `json:"name,omitempty"`
	Detail []PaymentMethodDetail `json:"detail,omitempty"`
}

type PaymentMethodDetail struct {
	ID            string      `json:"id,omitempty"`
	Name          string      `json:"name,omitempty"`
	AccountName   string      `json:"account_name,omitempty"`
	AccountNumber string      `json:"account_number,omitempty"`
	Icon          PaymentIcon `json:"icon,omitempty"`
}

type PaymentIcon struct {
	Small  string `json:"small,omitempty"`
	Medium string `json:"medium,omitempty"`
}

type PaymentMethodRequestParam struct {
	Type     string `json:"type,omitempty"`
	OutletId int    `json:"outlet_id,omitempty"`
}

type PaymentRequestParam struct {
	Id          string `json:"id,omitempty"`
	PaymentType string `json:"payment_type,omitempty"`
	PaymentId   string `json:"payment_id,omitempty"`
	Phone       string `json:"phone,omitempty"`
}

type PaymentCreateResponse struct {
	ID          string            `json:"id,omitempty"`
	PaymentInfo PaymentCreateInfo `json:"payment_info,omitempty"`
	Type        string            `json:"type,omitempty"`
	ExpiredAt   int64             `json:"expired_at,omitempty"`
}

type PaymentCreateInfo struct {
	Type  string `json:"type,omitempty"`
	Value string `json:"value,omitempty"`
}
