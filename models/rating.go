package models

type RatingResponse struct {
	Rating      float32          `json:"rating,omitempty"`
	Count       int              `json:"count,omitempty"`
	CountDetail []RatingResponse `json:"count_detail,omitempty"`
}

type ReviewResponse struct {
	Customer    string   `json:"customer,omitempty"`
	Date        string   `json:"date,omitempty"`
	DateMillis  int64    `json:"date_millis,omitempty"`
	Rating      float32  `json:"rating,omitempty"`
	Comment     string   `json:"comment,omitempty"`
	OrderItem   []string `json:"order_item,omitempty"`
	Attachments string   `json:"attachments,omitempty"`
	Attachment  []string `json:"attachment,omitempty"`
	SalesId     string   `json:"sales_id,omitempty"`
}

type TransactionItem struct {
	SalesId     string `json:"sales_id,omitempty"`
	ProductName string `json:"product_name,omitempty"`
}
