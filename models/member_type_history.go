package models

type TypeHistoryEntity struct {
	MembersTypeHistoryID int    `json:"members_type_history_id,omitempty"`
	MemberTypeFkid       int    `json:"member_type_fkid,omitempty"`
	MemberFkid           int    `json:"member_fkid,omitempty"`
	AdminFkid            int    `json:"admin_fkid,omitempty"`
	Point                int    `json:"point,omitempty"`
	Spend                int    `json:"spend,omitempty"`
	MemberTypePoint      int    `json:"member_type_point,omitempty"`
	MemberTypeSpend      int    `json:"member_type_spend,omitempty"`
	PointLost            int    `json:"point_lost,omitempty"`
	SpendLost            int    `json:"spend_lost,omitempty"`
	ChangeType           string `json:"change_type,omitempty"`
	ChangeLog            string `json:"change_log,omitempty"`
	DataCreated          int64  `json:"data_created,omitempty"`
}
