package models

type MemberTypeEntity struct {
	TypeID       int    `json:"type_id"`
	PointTarget  int    `json:"point_target"`
	SpentTarget  int    `json:"spent_target"`
	Price        int    `json:"price"`
	AdminFkid    int    `json:"admin_fkid"`
	ProductFkid  int    `json:"product_fkid"`
	DataCreated  int64  `json:"data_created"`
	DataModified int64  `json:"data_modified"`
	LifetimeDay  int    `json:"lifetime_day"`
	LifetimeType string `json:"lifetime_type"`
}
