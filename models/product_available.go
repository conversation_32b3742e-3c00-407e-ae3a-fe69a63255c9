package models

type ProductAvailableEntity struct {
	ID           int    `json:"id"`
	ProductFkid  int    `json:"product_fkid"`
	Day          string `json:"day"`
	HourStart    string `json:"hour_start"`
	HourEnd      string `json:"hour_end"`
	TimeModified int64  `json:"time_modified"`
}

func ProductAvailableEntityDefault() *ProductAvailableEntity {
	return &ProductAvailableEntity{
		HourStart: "00:00",
		HourEnd:   "23:59",
	}
}
