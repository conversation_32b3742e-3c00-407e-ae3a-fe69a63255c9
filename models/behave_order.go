package models

type BehaveOrder struct {
	StoreCode  string `json:"store_code"`
	TrxID      string `json:"trx_id"`
	OrderId    string `json:"order_id"`
	PickupTime string `json:"pickup_time"`
	DateTime   string `json:"date_time"`
	Payments   []struct {
		Type    string `json:"type"`
		Name    string `json:"name"`
		Nominal int    `json:"nominal"`
	} `json:"payments"`
	Total      int `json:"total"`
	Service    int `json:"service"`
	Tax        int `json:"tax"`
	Discount   int `json:"discount"`
	GrandTotal int `json:"grand_total"`
	Menu       []struct {
		PluID    string `json:"plu_id"`
		Name     string `json:"name"`
		Price    int    `json:"price"`
		Qty      int    `json:"qty"`
		Category string `json:"category"`
	} `json:"menu"`
	Member struct {
		Name  string `json:"name"`
		Phone string `json:"phone"`
	} `json:"member"`
}
