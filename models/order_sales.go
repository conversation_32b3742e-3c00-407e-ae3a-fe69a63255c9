package models

import (
	"encoding/json"
)

type OrderSalesEntity struct {
	ID                int         `json:"id,omitempty"`
	OrderSalesID      string      `json:"order_sales_id,omitempty"`
	SalesFkid         interface{} `json:"sales_fkid,omitempty"`
	MemberFkid        int         `json:"member_fkid,omitempty"`
	OutletFkid        int         `json:"outlet_fkid,omitempty"`
	OrderType         string      `json:"order_type,omitempty"`
	OrderNote         string      `json:"order_note,omitempty"`
	PickupTime        interface{} `json:"pickup_time,omitempty"`
	Status            string      `json:"status,omitempty"`
	TimeOrder         int64       `json:"time_order,omitempty"`
	TimeModified      int64       `json:"time_modified,omitempty"`
	TimeAcceptReject  int         `json:"time_accept_reject,omitempty"`
	TimeReady         int         `json:"time_ready,omitempty"`
	TimeTaken         int         `json:"time_taken,omitempty"`
	Items             string      `json:"items,omitempty"`
	RejectReason      interface{} `json:"reject_reason,omitempty"`
	GrandTotal        int         `json:"grand_total,omitempty"`
	GrandTotalPayment int         `json:"grand_total_payment,omitempty"`
	PaymentInfo       string      `json:"payment_info,omitempty"`
}

type OrderSalesShipmentEntity struct {
	OrderSalesFKID        uint64 `json:"order_sales_fkid,omitempty"`
	OrderSalesID          string `json:"order_sales_id,omitempty"`
	ShippingAddress       string `json:"shipping_address,omitempty"`
	ShippingPhone         string `json:"shipping_phone,omitempty"`
	ShippingName          string `json:"shipping_name,omitempty"`
	ShippingCharge        int    `json:"shipping_charge,omitempty"`
	ShippingCourier       string `json:"shipping_courier,omitempty"`
	ShippingCourierCustom string `json:"shipping_courier_custom,omitempty"`
	ShippingID            string `json:"shipping_id,omitempty"`
	ShippingReceipt       string `json:"shipping_receipt,omitempty"`
	ShippingAttachment    string `json:"shipping_attachment,omitempty"`
	TimeCreated           int64  `json:"time_created,omitempty"`
	TimeModified          int64  `json:"time_modified,omitempty"`
}

func (order OrderSalesEntity) ToTransaction() TransactionCreate {
	transaction := TransactionCreate{
		OrderSalesId: order.OrderSalesID,
		OutletID:     order.OutletFkid,
		CustomerName: "",            // Set the customer name accordingly
		OrderList:    []OrderList{}, // Initialize with an empty slice
		OrderType:    order.OrderType,
		Shipment:     TransactionShipment{}, // Initialize with default values
		Pickup:       Pickup{},              // Initialize with default values
		Status:       order.Status,
		Note:         order.OrderNote,
		Deal:         TransactionPromo{},   // Initialize with default values
		Promotion:    []TransactionPromo{}, // Initialize with an empty slice
		TimeOrder:    order.TimeOrder,
	}

	// Parse the items from the OrderSalesEntity and populate the OrderList slice
	var sales Sales
	if err := json.Unmarshal([]byte(order.Items), &sales); err == nil {
		for _, item := range sales.OrderList {
			orderList := OrderList{
				ProductDetailID: item.ProductDetailFkid,
				Price:           item.Price,
				Qty:             item.Qty,
			}
			transaction.OrderList = append(transaction.OrderList, orderList)
		}
	}

	return transaction
}
