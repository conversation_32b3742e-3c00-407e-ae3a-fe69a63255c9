package models

type PointExpiryLog struct {
	LogID         int64  `json:"log_id"`
	MemberFkID    int64  `json:"member_fkid"`
	AdminFkID     int    `json:"admin_fkid"`
	ExpiryType    string `json:"expiry_type"`
	PointsExpired int    `json:"points_expired"`
	ExecutionDate int64  `json:"execution_date"`
	ConfigFkId    int    `json:"config_fkid"`
}

type ExpiryConfig struct {
	ConfigID       int    `json:"config_id"`
	TypeID         int    `json:"type_id"`
	ExpiryEnabled  bool   `json:"expiry_enabled"`
	ExpiryModel    string `json:"expiry_model"`
	ExpiryValue    int    `json:"expiry_value"`
	ExpiryDate     string `json:"expiry_date"`
	InactivityDays int    `json:"inactivity_period"`
	DataCreated    int64  `json:"data_created"`
	DataModified   int64  `json:"data_modified"`
}
