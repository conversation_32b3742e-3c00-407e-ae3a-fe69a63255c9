package models

type PaymentWebhookPayload struct {
	TransactionId string `json:"transaction_id,omitempty"`
	OrderId       string `json:"order_id,omitempty"`
	Status        string
}

type PaymentWebhookXendit struct {
	Created string `json:"created,omitempty"`
	Data    struct {
		CaptureAmount int    `json:"capture_amount,omitempty"`
		ChargeAmount  int    `json:"charge_amount,omitempty"`
		Created       string `json:"created,omitempty"`
		ID            string `json:"id,omitempty"`
		ReferenceID   string `json:"reference_id,omitempty"`
		Status        string `json:"status,omitempty"`
	} `json:"data,omitempty"`
	Event string `json:"event,omitempty"`
}

func (p PaymentWebhookXendit) ToPayload() PaymentWebhookPayload {
	return PaymentWebhookPayload{
		TransactionId: p.Data.ID,
		OrderId:       p.Data.ReferenceID,
		Status:        p.Data.Status,
	}
}
