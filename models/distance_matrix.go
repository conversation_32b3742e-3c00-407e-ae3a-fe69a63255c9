package models

type DistanceMatrix struct {
	DestinationAddresses []string `json:"destination_addresses,omitempty"`
	OriginAddresses      []string `json:"origin_addresses,omitempty"`
	Rows                 []Rows   `json:"rows,omitempty"`
	Status               string   `json:"status,omitempty"`
}

type Distance struct {
	Text  string `json:"text,omitempty"`
	Value int    `json:"value,omitempty"`
}
type Duration struct {
	Text  string `json:"text,omitempty"`
	Value int    `json:"value,omitempty"`
}
type Elements struct {
	Distance Distance `json:"distance,omitempty"`
	Duration Duration `json:"duration,omitempty"`
	Status   string   `json:"status,omitempty"`
}
type Rows struct {
	Elements []Elements `json:"elements,omitempty"`
}
