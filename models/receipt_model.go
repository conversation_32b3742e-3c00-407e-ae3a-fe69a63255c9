package models

type Items struct {
	Product    string
	Qty        string
	SubTotal   string
	SubItems   []Items
	Promotions []Promotion
}

type Promotion struct {
	PromoType   string
	PromoValue  string
	Code        string `json:"code"`
	Name        string `json:"name"`
	PromotionId int    `json:"promotion_id"`
	TypeId      int    `json:"type_id"`
	Value       int    `json:"value"`
}

type PaymentMedia struct {
	Method string
	Total  string
}

type TaxAndDisc struct {
	Name  string
	Total string
}

type Receipt struct {
	ReceiptNo     string
	Date          string
	Cashier       string
	Outlet        string
	OutletAddress string
	OutletPhone   string
	OutletLogo    string
	Items         []Items
	TaxAndDisc    []TaxAndDisc
	GrandTotal    string
	Payments      []PaymentMedia
	Return        string
	ReceiptNote   string
	ReceiptSosMed string
	FeedbackUrl   string
	VoucherCode   string
	Customer      string
	Payment       string
}
