package models

type LinkMenuEntity struct {
	LinkmenuID        int                    `json:"linkmenu_id,omitempty"`
	Name              string                 `json:"name,omitempty"`
	Description       string                 `json:"description,omitempty"`
	OrderNo           int                    `json:"order_no,omitempty"`
	IsMultiplechoice  int                    `json:"is_multiplechoice"`
	OutletFkid        int                    `json:"outlet_fkid,omitempty"`
	ProductFkid       int                    `json:"product_fkid,omitempty"`
	ProductDetailFkid int                    `json:"product_detail_fkid,omitempty"`
	LinkMenuDetail    []LinkMenuDetailEntity `json:"link_menu_detail,omitempty"`
}

type LinkMenuDetailEntity struct {
	LinkmenuDetailID  int `json:"linkmenu_detail_id,omitempty"`
	LinkmenuFkid      int `json:"linkmenu_fkid,omitempty"`
	ProductDetailFkid int `json:"product_detail_fkid,omitempty"`
	PriceAdd          int `json:"price_add"`
}
