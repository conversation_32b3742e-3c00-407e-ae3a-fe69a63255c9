package models

type PaymentMediaBankEntity struct {
	BankID       int    `json:"bank_id,omitempty"`
	Name         string `json:"name,omitempty"`
	NoRekening   string `json:"no_rekening,omitempty"`
	Owner        string `json:"owner,omitempty"`
	AdminFkid    int    `json:"admin_fkid,omitempty"`
	DataCreated  string `json:"data_created,omitempty"`
	DataModified string `json:"data_modified,omitempty"`
	DataStatus   string `json:"data_status,omitempty"`
}
