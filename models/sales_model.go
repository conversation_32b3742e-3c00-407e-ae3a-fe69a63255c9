package models

type Sales struct {
	Customer    string           `json:"customer"`
	DisplayNota string           `json:"displayNota"`
	GrandTotal  int              `json:"grandTotal"`
	NoNota      string           `json:"noNota"`
	OrderList   []SalesOrderList `json:"orderList"`
	SalesID     string           `json:"sales_id"`
	Status      string           `json:"status"`
	OutletId    int              `json:"outletId"`
	UniqueCode  int              `json:"unique_code"`
	Promotions  []Promotion      `json:"promotions,omitempty"`
	OrderType   string           `json:"order_type"`
}

type SalesOrderList struct {
	Price             int              `json:"price,omitempty"`
	Qty               int              `json:"qty,omitempty"`
	SubTotal          int              `json:"subTotal,omitempty"`
	ProductDetailFkid int              `json:"product_detail_fkid,omitempty"`
	ProductFkid       int              `json:"product_fkid,omitempty"`
	Note              string           `json:"note,omitempty"`
	Promotion         *Promotion       `json:"promotion,omitempty"`
	Product           OrderListProduct `json:"product,omitempty"`
	Tax               []SalesOrderTax  `json:"tax,omitempty"`
	Extra             []SalesOrderList `json:"extra,omitempty"`
}

type OrderListProduct struct {
	Name            string `json:"name,omitempty"`
	ProductDetailID int    `json:"product_detail_id,omitempty"`
	ProductID       int    `json:"product_id,omitempty"`
}

type Shipment struct {
	OrderSalesID    string `json:"order_sales_id"`
	ShippingAddress string `json:"shipping_address"`
	ShippingCharge  int64  `json:"shipping_charge"`
	TimeCreated     int64  `json:"time_created"`
	TimeModified    int64  `json:"time_modified"`
}

type SalesOrderTax struct {
	GratuityId int    `json:"gratuity_id,omitempty"`
	Total      int    `json:"total,omitempty"`
	Name       string `json:"name,omitempty"`
}
