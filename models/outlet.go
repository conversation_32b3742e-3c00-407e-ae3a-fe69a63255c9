package models

type OutletEntity struct {
	OutletID       int     `json:"outlet_id,omitempty"`
	Name           string  `json:"name,omitempty"`
	Address        string  `json:"address,omitempty"`
	Phone          string  `json:"phone,omitempty"`
	OutletLogo     string  `json:"outlet_logo,omitempty"`
	Country        string  `json:"country,omitempty"`
	Province       string  `json:"province,omitempty"`
	City           string  `json:"city,omitempty"`
	PostalCode     string  `json:"postal_code,omitempty"`
	ReceiptPhone   string  `json:"receipt_phone,omitempty"`
	ReceiptAddress string  `json:"receipt_address,omitempty"`
	Latitude       float64 `json:"latitude,omitempty"`
	Longitude      float64 `json:"longitude,omitempty"`
}

type OutletFeature struct {
	Tableinput bool `json:"tableinput"`
}
