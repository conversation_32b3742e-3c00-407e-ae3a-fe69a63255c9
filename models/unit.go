package models

type UnitEntity struct {
	UnitID       int    `json:"unit_id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	AdminFKID    int    `json:"admin_fkid"`
	DataCreated  string `json:"data_created"`
	DataModified string `json:"data_modified"`
	DataStatus   string `json:"data_status"`
}

func (u *UnitEntity) ToMap() map[string]interface{} {
	unitMap := map[string]interface{}{
		"unit_id":       u.UnitID,
		"name":          u.Name,
		"description":   u.Description,
		"admin_fkid":    u.AdminFKID,
		"data_created":  u.DataCreated,
		"data_modified": u.DataModified,
		"data_status":   u.DataStatus,
	}
	return unitMap
}
