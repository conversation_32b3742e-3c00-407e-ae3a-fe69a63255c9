package models

type MemberEntity struct {
	MemberID      int    `json:"member_id"`
	Email         string `json:"email"`
	EmailVerified int    `json:"email_verified"`
	Password      string `json:"password"`
	Name          string `json:"name"`
	Gender        int    `json:"gender"`
	Phone         string `json:"phone"`
	PhoneVerified int    `json:"phone_verified"`
	DateOfBirth   int64  `json:"date_of_birth"`
	Address       string `json:"address"`
	City          string `json:"city"`
	Province      string `json:"province"`
	PostalCode    int    `json:"postal_code"`
	ExpiredDate   int64  `json:"expired_date"`
	DataCreated   int64  `json:"data_created"`
	Status        int    `json:"status"`
}

type MemberDetailInfo struct {
	Barcode    string `json:"barcode,omitempty"`
	BarcodeUrl string `json:"barcode_url,omitempty"`
	MemberType string `json:"member_type,omitempty"`
}

type MemberWithDetail struct {
	MemberEntity
	MemberDetailEntity
	MemberDetailInfo
}
