package models

type TransactionConfig struct {
	Transaction ConfigTransaction `json:"transaction,omitempty"`
	// OrderType   OrderConfigurationDetail `json:"order_type,omitempty"`
}

type ConfigTransaction struct {
	RefundPolicy       string `json:"refund_policy,omitempty"`
	RefundPolicyDetail struct {
		Nominal int `json:"nominal,omitempty"`
	} `json:"refund_policy_detail,omitempty"`
	StockConfirmation bool `json:"stock_confirmation,omitempty"`
	PaymentTimeout    int  `json:"payment_timeout,omitempty"`
}
